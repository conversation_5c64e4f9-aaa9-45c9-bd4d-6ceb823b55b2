<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// ตรวจสอบการล็อกอิน
requireLogin();

// ฟังก์ชันดึงข้อมูล hardware
function getHardwareInfo() {
    $hardware = [];

    // ตรวจสอบระบบปฏิบัติการ
    $os_type = strtolower(php_uname('s'));

    if (strpos($os_type, 'windows') !== false) {
        // Windows System - ใช้ systeminfo
        $systemInfo = getSystemInfo();

        // ดึงข้อมูลจาก systeminfo
        $hardware['hostname'] = $systemInfo['Host Name'] ?? gethostname();
        $hardware['ip_address'] = getWindowsIPAddress();
        $hardware['os_name'] = $systemInfo['OS Name'] ?? 'Unknown';
        $hardware['system_manufacturer'] = $systemInfo['System Manufacturer'] ?? 'Unknown';
        $hardware['system_model'] = $systemInfo['System Model'] ?? 'Unknown';
        $hardware['serial_number'] = $systemInfo['System Serial Number'] ?? getWindowsBIOSSerial();
        $hardware['monitor'] = getWindowsMonitorInfo();

        // ข้อมูลเพิ่มเติมจาก systeminfo
        $hardware['os_version'] = $systemInfo['OS Version'] ?? 'Unknown';
        $hardware['system_type'] = $systemInfo['System Type'] ?? 'Unknown';
        $hardware['processor'] = $systemInfo['Processor(s)'] ?? 'Unknown';
        $hardware['total_physical_memory'] = $systemInfo['Total Physical Memory'] ?? 'Unknown';
        $hardware['bios_version'] = $systemInfo['BIOS Version'] ?? 'Unknown';
        $hardware['domain'] = $systemInfo['Domain'] ?? 'Unknown';
        $hardware['logon_server'] = $systemInfo['Logon Server'] ?? 'Unknown';

    } elseif (strpos($os_type, 'linux') !== false) {
        // Linux System
        $hardware['hostname'] = gethostname();
        $hardware['ip_address'] = getLinuxIPAddress();
        $hardware['os_name'] = getLinuxInfo('lsb_release -d 2>/dev/null | cut -f2') ?: php_uname('s') . ' ' . php_uname('r');
        $hardware['system_manufacturer'] = getLinuxInfo('dmidecode -s system-manufacturer 2>/dev/null');
        $hardware['system_model'] = getLinuxInfo('dmidecode -s system-product-name 2>/dev/null');
        $hardware['serial_number'] = getLinuxInfo('dmidecode -s system-serial-number 2>/dev/null');
        $hardware['monitor'] = getLinuxMonitorInfo();

    } elseif (strpos($os_type, 'darwin') !== false) {
        // macOS System
        $hardware['hostname'] = gethostname();
        $hardware['ip_address'] = getMacIPAddress();
        $hardware['os_name'] = getMacInfo('sw_vers -productName') . ' ' . getMacInfo('sw_vers -productVersion');
        $hardware['system_manufacturer'] = 'Apple Inc.';
        $hardware['system_model'] = getMacInfo('system_profiler SPHardwareDataType | grep "Model Name" | cut -d: -f2');
        $hardware['serial_number'] = getMacInfo('system_profiler SPHardwareDataType | grep "Serial Number" | cut -d: -f2');
        $hardware['monitor'] = getMacMonitorInfo();

    } else {
        // Unknown System - ใช้ข้อมูลพื้นฐาน
        $hardware['hostname'] = gethostname() ?: ($_SERVER['SERVER_NAME'] ?? 'Unknown');
        $hardware['ip_address'] = $_SERVER['SERVER_ADDR'] ?? 'Unknown';
        $hardware['os_name'] = php_uname('s') . ' ' . php_uname('r');
        $hardware['system_manufacturer'] = 'Unknown';
        $hardware['system_model'] = 'Unknown';
        $hardware['serial_number'] = 'Unknown';
        $hardware['monitor'] = 'Unknown';
    }

    // ข้อมูลเสริมทั่วไป
    $hardware['php_version'] = phpversion();
    $hardware['server_software'] = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
    $hardware['server_time'] = date('Y-m-d H:i:s');
    $hardware['client_ip'] = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $hardware['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';

    return $hardware;
}

// ฟังก์ชันดึงข้อมูลจาก systeminfo (Windows)
function getSystemInfo() {
    if (!function_exists('shell_exec')) {
        return [];
    }

    try {
        $output = shell_exec('systeminfo 2>nul');
        if (!$output) {
            return [];
        }

        $systemInfo = [];
        $lines = explode("\n", $output);

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line) || strpos($line, ':') === false) {
                continue;
            }

            $parts = explode(':', $line, 2);
            if (count($parts) == 2) {
                $key = trim($parts[0]);
                $value = trim($parts[1]);

                // ทำความสะอาดข้อมูล
                if (!empty($value) && $value !== 'N/A') {
                    $systemInfo[$key] = $value;
                }
            }
        }

        return $systemInfo;

    } catch (Exception $e) {
        return [];
    }
}

// ฟังก์ชันดึง IP Address สำหรับ Windows
function getWindowsIPAddress() {
    try {
        $output = shell_exec('ipconfig 2>nul | findstr /i "IPv4"');
        if ($output) {
            preg_match('/(\d+\.\d+\.\d+\.\d+)/', $output, $matches);
            return $matches[1] ?? ($_SERVER['SERVER_ADDR'] ?? 'Unknown');
        }
    } catch (Exception $e) {
        // Fallback
    }

    return $_SERVER['SERVER_ADDR'] ?? gethostbyname(gethostname()) ?? 'Unknown';
}

// ฟังก์ชันดึง BIOS Serial Number สำหรับ Windows
function getWindowsBIOSSerial() {
    try {
        $output = shell_exec('wmic bios get SerialNumber /value 2>nul');
        if ($output) {
            $lines = explode("\n", trim($output));
            foreach ($lines as $line) {
                if (strpos($line, 'SerialNumber=') !== false) {
                    $serial = trim(str_replace('SerialNumber=', '', $line));
                    if (!empty($serial)) {
                        return $serial;
                    }
                }
            }
        }
    } catch (Exception $e) {
        // Fallback
    }

    return 'Unknown';
}

// ฟังก์ชันดึงข้อมูล Monitor สำหรับ Windows
function getWindowsMonitorInfo() {
    if (!function_exists('shell_exec')) {
        return 'Permission Denied';
    }

    try {
        // ลองหลายวิธีในการดึงข้อมูล Monitor
        $monitors = [];

        // วิธีที่ 1: ใช้ wmic path Win32_DesktopMonitor
        $output = shell_exec('wmic path Win32_DesktopMonitor get Name /value 2>nul');
        if ($output) {
            $lines = explode("\n", trim($output));
            foreach ($lines as $line) {
                if (strpos($line, 'Name=') !== false) {
                    $name = trim(str_replace('Name=', '', $line));
                    if (!empty($name) && $name !== 'N/A') {
                        $monitors[] = $name;
                    }
                }
            }
        }

        // วิธีที่ 2: ใช้ wmic path Win32_PnPEntity (สำหรับ Monitor)
        if (empty($monitors)) {
            $output = shell_exec('wmic path Win32_PnPEntity where "Service=\'monitor\'" get Name /value 2>nul');
            if ($output) {
                $lines = explode("\n", trim($output));
                foreach ($lines as $line) {
                    if (strpos($line, 'Name=') !== false) {
                        $name = trim(str_replace('Name=', '', $line));
                        if (!empty($name) && $name !== 'N/A') {
                            $monitors[] = $name;
                        }
                    }
                }
            }
        }

        // วิธีที่ 3: ใช้ PowerShell (ถ้าวิธีอื่นไม่ได้ผล)
        if (empty($monitors)) {
            $output = shell_exec('powershell "Get-WmiObject -Class Win32_DesktopMonitor | Select-Object Name | Format-Table -HideTableHeaders" 2>nul');
            if ($output) {
                $lines = explode("\n", trim($output));
                foreach ($lines as $line) {
                    $line = trim($line);
                    if (!empty($line) && $line !== 'Name' && $line !== '----') {
                        $monitors[] = $line;
                    }
                }
            }
        }

        return !empty($monitors) ? implode(', ', array_unique($monitors)) : 'No Monitor Detected';

    } catch (Exception $e) {
        return 'Error: ' . $e->getMessage();
    }
}

// ฟังก์ชันสำหรับ Linux
function getLinuxInfo($command) {
    if (!function_exists('shell_exec')) {
        return 'Permission Denied';
    }

    try {
        $output = shell_exec($command);
        return $output ? trim($output) : 'Unknown';
    } catch (Exception $e) {
        return 'Error: ' . $e->getMessage();
    }
}

function getLinuxIPAddress() {
    try {
        // ลองหา IP Address หลายวิธี
        $commands = [
            "hostname -I | awk '{print $1}'",
            "ip route get ******* | awk '{print $7; exit}'",
            "ifconfig | grep 'inet ' | grep -v '127.0.0.1' | awk '{print $2}' | head -1"
        ];

        foreach ($commands as $command) {
            $output = shell_exec($command . ' 2>/dev/null');
            if ($output) {
                $ip = trim($output);
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
    } catch (Exception $e) {
        // Fallback
    }

    return $_SERVER['SERVER_ADDR'] ?? 'Unknown';
}

function getLinuxMonitorInfo() {
    if (!function_exists('shell_exec')) {
        return 'Permission Denied';
    }

    try {
        $monitors = [];

        // วิธีที่ 1: ใช้ xrandr
        $output = shell_exec('xrandr --query 2>/dev/null | grep " connected"');
        if ($output) {
            $lines = explode("\n", trim($output));
            foreach ($lines as $line) {
                if (strpos($line, 'connected') !== false) {
                    $parts = explode(' ', $line);
                    if (!empty($parts[0])) {
                        // ดึงข้อมูลความละเอียดด้วย
                        preg_match('/(\d+x\d+)/', $line, $resolution);
                        $monitorInfo = $parts[0];
                        if (!empty($resolution[1])) {
                            $monitorInfo .= ' (' . $resolution[1] . ')';
                        }
                        $monitors[] = $monitorInfo;
                    }
                }
            }
        }

        // วิธีที่ 2: ใช้ /sys/class/drm
        if (empty($monitors)) {
            $output = shell_exec('find /sys/class/drm -name "status" -exec grep -l "connected" {} \; 2>/dev/null');
            if ($output) {
                $files = explode("\n", trim($output));
                foreach ($files as $file) {
                    if (!empty($file)) {
                        $connector = basename(dirname($file));
                        $monitors[] = $connector;
                    }
                }
            }
        }

        // วิธีที่ 3: ใช้ lshw (ถ้ามี)
        if (empty($monitors)) {
            $output = shell_exec('lshw -c display 2>/dev/null | grep "product:"');
            if ($output) {
                $lines = explode("\n", trim($output));
                foreach ($lines as $line) {
                    if (strpos($line, 'product:') !== false) {
                        $product = trim(str_replace('product:', '', $line));
                        if (!empty($product)) {
                            $monitors[] = $product;
                        }
                    }
                }
            }
        }

        return !empty($monitors) ? implode(', ', array_unique($monitors)) : 'No Monitor Detected';

    } catch (Exception $e) {
        return 'Error: ' . $e->getMessage();
    }
}

// ฟังก์ชันสำหรับ macOS
function getMacInfo($command) {
    if (!function_exists('shell_exec')) {
        return 'Permission Denied';
    }

    try {
        $output = shell_exec($command . ' 2>/dev/null');
        if ($output) {
            // ถ้ามี : ให้แยกและเอาส่วนหลัง
            if (strpos($output, ':') !== false) {
                $parts = explode(':', $output, 2);
                return count($parts) == 2 ? trim($parts[1]) : trim($output);
            }
            return trim($output);
        }
    } catch (Exception $e) {
        return 'Error: ' . $e->getMessage();
    }

    return 'Unknown';
}

function getMacIPAddress() {
    try {
        // ลองหา IP Address หลายวิธี
        $commands = [
            "ifconfig | grep 'inet ' | grep -v '127.0.0.1' | awk '{print $2}' | head -1",
            "route get default | grep interface | awk '{print $2}' | xargs ifconfig | grep 'inet ' | awk '{print $2}'"
        ];

        foreach ($commands as $command) {
            $output = shell_exec($command . ' 2>/dev/null');
            if ($output) {
                $ip = trim($output);
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
    } catch (Exception $e) {
        // Fallback
    }

    return $_SERVER['SERVER_ADDR'] ?? 'Unknown';
}

function getMacMonitorInfo() {
    if (!function_exists('shell_exec')) {
        return 'Permission Denied';
    }

    try {
        $monitors = [];

        // วิธีที่ 1: ใช้ system_profiler SPDisplaysDataType
        $output = shell_exec('system_profiler SPDisplaysDataType 2>/dev/null');
        if ($output) {
            $lines = explode("\n", $output);
            $currentDisplay = '';

            foreach ($lines as $line) {
                $line = trim($line);

                // หาชื่อ Display
                if (preg_match('/^([^:]+):$/', $line, $matches)) {
                    $currentDisplay = trim($matches[1]);
                    if (!empty($currentDisplay) && $currentDisplay !== 'Graphics/Displays') {
                        $monitors[] = $currentDisplay;
                    }
                }

                // หาข้อมูล Resolution
                if (strpos($line, 'Resolution:') !== false) {
                    $resolution = trim(str_replace('Resolution:', '', $line));
                    if (!empty($resolution) && !empty($currentDisplay)) {
                        // อัปเดต monitor ล่าสุดด้วย resolution
                        $lastIndex = count($monitors) - 1;
                        if ($lastIndex >= 0) {
                            $monitors[$lastIndex] .= ' (' . $resolution . ')';
                        }
                    }
                }
            }
        }

        // วิธีที่ 2: ใช้ system_profiler SPHardwareDataType (สำหรับ built-in display)
        if (empty($monitors)) {
            $output = shell_exec('system_profiler SPHardwareDataType 2>/dev/null | grep -i display');
            if ($output) {
                $monitors[] = trim($output);
            }
        }

        return !empty($monitors) ? implode(', ', array_unique($monitors)) : 'No Monitor Detected';

    } catch (Exception $e) {
        return 'Error: ' . $e->getMessage();
    }
}

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

// ดึงข้อมูล hardware
$hardwareInfo = getHardwareInfo();

// จัดการการบันทึกข้อมูล
$saveMessage = '';
if ($_POST['action'] ?? '' === 'save_hardware') {
    $saveMessage = saveHardwareData($hardwareInfo);
}

// ฟังก์ชันบันทึกข้อมูล Hardware
function saveHardwareData($data) {
    try {
        $filename = 'hardware_data_' . date('Y-m-d_H-i-s') . '.json';
        $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        if (file_put_contents($filename, $jsonData)) {
            return ['type' => 'success', 'message' => 'บันทึกข้อมูล Hardware สำเร็จ: ' . $filename];
        } else {
            return ['type' => 'error', 'message' => 'ไม่สามารถบันทึกข้อมูลได้'];
        }
    } catch (Exception $e) {
        return ['type' => 'error', 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()];
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ข้อมูล Hardware - Asset Management System</title>
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <link rel="stylesheet" href="assets/style.css">
    
    <style>
        .hardware-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .hardware-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .hardware-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
        }
        
        .hardware-card h3 {
            color: #2d3748;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4299e1;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .hardware-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f7fafc;
        }
        
        .hardware-item:last-child {
            border-bottom: none;
        }
        
        .hardware-label {
            font-weight: 600;
            color: #4a5568;
            flex: 1;
        }
        
        .hardware-value {
            color: #2d3748;
            font-family: 'Courier New', monospace;
            background: #f7fafc;
            padding: 4px 8px;
            border-radius: 4px;
            flex: 2;
            text-align: right;
            word-break: break-all;
        }
        
        .extensions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .extension-item {
            background: #f7fafc;
            padding: 8px 12px;
            border-radius: 6px;
            border-left: 3px solid #4299e1;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .refresh-btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
            transition: background 0.3s;
        }
        
        .refresh-btn:hover {
            background: #3182ce;
        }
        
        .status-good {
            color: #38a169;
            font-weight: bold;
        }
        
        .status-warning {
            color: #d69e2e;
            font-weight: bold;
        }
        
        .status-error {
            color: #e53e3e;
            font-weight: bold;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #4299e1;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            color: #3182ce;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ข้อมูล Hardware</h1>
            <p class="subtitle">Hardware Information - ข้อมูลฮาร์ดแวร์และระบบ</p>
        </div>
    </div>

    <div class="hardware-container">
        <a href="index.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            กลับสู่หน้าหลัก
        </a>
        
        <div style="display: flex; gap: 10px; margin-bottom: 20px;">
            <button class="refresh-btn" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i>
                รีเฟรชข้อมูล
            </button>

            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="save_hardware">
                <button type="submit" class="refresh-btn" style="background: #38a169;">
                    <i class="fas fa-save"></i>
                    บันทึกข้อมูล
                </button>
            </form>
        </div>

        <?php if (!empty($saveMessage)): ?>
        <div style="padding: 10px; margin-bottom: 20px; border-radius: 6px; <?php echo $saveMessage['type'] === 'success' ? 'background: #d4edda; color: #155724; border: 1px solid #c3e6cb;' : 'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;'; ?>">
            <i class="fas <?php echo $saveMessage['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?>"></i>
            <?php echo htmlspecialchars($saveMessage['message']); ?>
        </div>
        <?php endif; ?>

        <div class="hardware-grid">
            <!-- ข้อมูล Hardware หลัก -->
            <div class="hardware-card">
                <h3>
                    <i class="fas fa-desktop"></i>
                    ข้อมูล Hardware
                </h3>
                <div class="hardware-item">
                    <span class="hardware-label">Host Name:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['hostname']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">IP Address:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['ip_address']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">OS Name:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['os_name']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">System Manufacturer:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['system_manufacturer']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">System Model:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['system_model']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Serial Number:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['serial_number']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Monitor:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['monitor']); ?></span>
                </div>
            </div>

            <!-- ข้อมูลระบบเสริม -->
            <div class="hardware-card">
                <h3>
                    <i class="fas fa-info-circle"></i>
                    ข้อมูลระบบเสริม
                </h3>
                <?php if (isset($hardwareInfo['os_version'])): ?>
                <div class="hardware-item">
                    <span class="hardware-label">OS Version:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['os_version']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (isset($hardwareInfo['system_type'])): ?>
                <div class="hardware-item">
                    <span class="hardware-label">System Type:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['system_type']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (isset($hardwareInfo['processor'])): ?>
                <div class="hardware-item">
                    <span class="hardware-label">Processor:</span>
                    <span class="hardware-value" style="font-size: 0.8em;"><?php echo htmlspecialchars($hardwareInfo['processor']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (isset($hardwareInfo['total_physical_memory'])): ?>
                <div class="hardware-item">
                    <span class="hardware-label">Total Memory:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['total_physical_memory']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (isset($hardwareInfo['bios_version'])): ?>
                <div class="hardware-item">
                    <span class="hardware-label">BIOS Version:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['bios_version']); ?></span>
                </div>
                <?php endif; ?>

                <div class="hardware-item">
                    <span class="hardware-label">PHP Version:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['php_version']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Web Server:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['server_software']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Server Time:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['server_time']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Client IP:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['client_ip']); ?></span>
                </div>
            </div>

            <!-- ข้อมูลเครือข่ายและโดเมน -->
            <div class="hardware-card">
                <h3>
                    <i class="fas fa-network-wired"></i>
                    ข้อมูลเครือข่าย
                </h3>
                <?php if (isset($hardwareInfo['domain'])): ?>
                <div class="hardware-item">
                    <span class="hardware-label">Domain:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['domain']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (isset($hardwareInfo['logon_server'])): ?>
                <div class="hardware-item">
                    <span class="hardware-label">Logon Server:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['logon_server']); ?></span>
                </div>
                <?php endif; ?>

                <div class="hardware-item">
                    <span class="hardware-label">User Agent:</span>
                    <span class="hardware-value" style="font-size: 0.7em; word-break: break-all;"><?php echo htmlspecialchars($hardwareInfo['user_agent']); ?></span>
                </div>

                <div class="hardware-item">
                    <span class="hardware-label">Data Source:</span>
                    <span class="hardware-value">
                        <?php
                        $os_type = strtolower(php_uname('s'));
                        if (strpos($os_type, 'windows') !== false) {
                            echo 'Windows SystemInfo Command';
                        } elseif (strpos($os_type, 'linux') !== false) {
                            echo 'Linux System Commands';
                        } elseif (strpos($os_type, 'darwin') !== false) {
                            echo 'macOS System Profiler';
                        } else {
                            echo 'Generic PHP Functions';
                        }
                        ?>
                    </span>
                </div>
            </div>



            <!-- คำแนะนำการใช้งาน -->
            <div class="hardware-card">
                <h3>
                    <i class="fas fa-lightbulb"></i>
                    คำแนะนำการใช้งาน
                </h3>
                <div style="padding: 10px; background: #f8f9fa; border-radius: 6px; margin-top: 10px;">
                    <p style="margin: 0; font-size: 0.9em; line-height: 1.5;">
                        <strong>วิธีการใช้งาน:</strong><br>
                        1. เข้าหน้านี้จากเครื่องที่ต้องการเก็บข้อมูล<br>
                        2. ระบบจะดึงข้อมูล Hardware อัตโนมัติ<br>
                        3. บันทึกข้อมูลเพื่อใช้ในการจัดการ Asset<br>
                        4. ข้อมูลจะอัปเดตทุกครั้งที่เข้าหน้านี้
                    </p>
                </div>
                <div style="padding: 10px; background: #fff3cd; border-radius: 6px; margin-top: 10px;">
                    <p style="margin: 0; font-size: 0.9em; line-height: 1.5;">
                        <strong>หมายเหตุ:</strong><br>
                        บางข้อมูลอาจต้องการสิทธิ์ Administrator หรือ Root<br>
                        เพื่อเข้าถึงข้อมูล Hardware ระดับต่ำ
                    </p>
                </div>
            </div>
        </div>

        <!-- ข้อมูล JSON สำหรับ Export -->
        <div class="hardware-card" style="margin-top: 20px;">
            <h3>
                <i class="fas fa-code"></i>
                ข้อมูล Hardware (JSON Format)
            </h3>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-top: 10px;">
                <pre style="margin: 0; font-size: 0.8em; white-space: pre-wrap; word-wrap: break-word;"><?php echo htmlspecialchars(json_encode($hardwareInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
            </div>
            <div style="margin-top: 10px;">
                <button onclick="copyToClipboard()" class="refresh-btn" style="background: #6f42c1;">
                    <i class="fas fa-copy"></i>
                    คัดลอก JSON
                </button>
            </div>
        </div>
    </div>

    <script>
        // Auto refresh every 60 seconds
        setTimeout(function() {
            location.reload();
        }, 60000);

        // Add loading animation to refresh button
        document.querySelector('.refresh-btn').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> กำลังรีเฟรช...';
        });

        // Copy JSON to clipboard
        function copyToClipboard() {
            const jsonData = <?php echo json_encode(json_encode($hardwareInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?>;

            if (navigator.clipboard) {
                navigator.clipboard.writeText(jsonData).then(function() {
                    alert('คัดลอกข้อมูล JSON สำเร็จ!');
                }).catch(function(err) {
                    console.error('ไม่สามารถคัดลอกได้: ', err);
                    fallbackCopyTextToClipboard(jsonData);
                });
            } else {
                fallbackCopyTextToClipboard(jsonData);
            }
        }

        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    alert('คัดลอกข้อมูล JSON สำเร็จ!');
                } else {
                    alert('ไม่สามารถคัดลอกได้');
                }
            } catch (err) {
                alert('ไม่สามารถคัดลอกได้');
            }

            document.body.removeChild(textArea);
        }

        // Show hardware collection status
        console.log('Hardware Data Collection Status: Active');
        console.log('Last Updated: <?php echo date('Y-m-d H:i:s'); ?>');
    </script>
</body>
</html>
