<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// ตรวจสอบการล็อกอิน
requireLogin();

// ฟังก์ชันดึงข้อมูล hardware
function getHardwareInfo() {
    $hardware = [];

    // Host Name
    $hardware['hostname'] = gethostname() ?: ($_SERVER['SERVER_NAME'] ?? 'Unknown');

    // IP Address
    $hardware['ip_address'] = $_SERVER['SERVER_ADDR'] ?? gethostbyname(gethostname());

    // OS Name
    $hardware['os_name'] = php_uname('s') . ' ' . php_uname('r');
    $hardware['os_full'] = php_uname();

    // System Information - ใช้คำสั่งระบบตามแต่ละ OS
    $os_type = strtolower(php_uname('s'));

    if (strpos($os_type, 'windows') !== false) {
        // Windows System
        $hardware['system_manufacturer'] = getWindowsInfo('wmic computersystem get Manufacturer /value');
        $hardware['system_model'] = getWindowsInfo('wmic computersystem get Model /value');
        $hardware['serial_number'] = getWindowsInfo('wmic bios get SerialNumber /value');
        $hardware['monitor'] = getWindowsMonitorInfo();
    } elseif (strpos($os_type, 'linux') !== false) {
        // Linux System
        $hardware['system_manufacturer'] = getLinuxInfo('dmidecode -s system-manufacturer');
        $hardware['system_model'] = getLinuxInfo('dmidecode -s system-product-name');
        $hardware['serial_number'] = getLinuxInfo('dmidecode -s system-serial-number');
        $hardware['monitor'] = getLinuxMonitorInfo();
    } elseif (strpos($os_type, 'darwin') !== false) {
        // macOS System
        $hardware['system_manufacturer'] = 'Apple Inc.';
        $hardware['system_model'] = getMacInfo('system_profiler SPHardwareDataType | grep "Model Name"');
        $hardware['serial_number'] = getMacInfo('system_profiler SPHardwareDataType | grep "Serial Number"');
        $hardware['monitor'] = getMacMonitorInfo();
    } else {
        // Unknown System
        $hardware['system_manufacturer'] = 'Unknown';
        $hardware['system_model'] = 'Unknown';
        $hardware['serial_number'] = 'Unknown';
        $hardware['monitor'] = 'Unknown';
    }

    // เพิ่มข้อมูลเสริม
    $hardware['php_version'] = phpversion();
    $hardware['server_software'] = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
    $hardware['server_time'] = date('Y-m-d H:i:s');
    $hardware['client_ip'] = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';

    return $hardware;
}

// ฟังก์ชันสำหรับ Windows
function getWindowsInfo($command) {
    if (!function_exists('exec') || !function_exists('shell_exec')) {
        return 'Permission Denied';
    }

    try {
        $output = shell_exec($command . ' 2>nul');
        if ($output) {
            $lines = explode("\n", trim($output));
            foreach ($lines as $line) {
                if (strpos($line, '=') !== false) {
                    $parts = explode('=', $line, 2);
                    if (count($parts) == 2 && !empty(trim($parts[1]))) {
                        return trim($parts[1]);
                    }
                }
            }
        }
    } catch (Exception $e) {
        return 'Error: ' . $e->getMessage();
    }

    return 'Unknown';
}

function getWindowsMonitorInfo() {
    if (!function_exists('shell_exec')) {
        return 'Permission Denied';
    }

    try {
        $output = shell_exec('wmic desktopmonitor get Name /value 2>nul');
        if ($output) {
            $monitors = [];
            $lines = explode("\n", trim($output));
            foreach ($lines as $line) {
                if (strpos($line, 'Name=') !== false) {
                    $name = trim(str_replace('Name=', '', $line));
                    if (!empty($name)) {
                        $monitors[] = $name;
                    }
                }
            }
            return !empty($monitors) ? implode(', ', $monitors) : 'Unknown';
        }
    } catch (Exception $e) {
        return 'Error: ' . $e->getMessage();
    }

    return 'Unknown';
}

// ฟังก์ชันสำหรับ Linux
function getLinuxInfo($command) {
    if (!function_exists('exec') || !function_exists('shell_exec')) {
        return 'Permission Denied';
    }

    try {
        $output = shell_exec($command . ' 2>/dev/null');
        return $output ? trim($output) : 'Unknown';
    } catch (Exception $e) {
        return 'Error: ' . $e->getMessage();
    }
}

function getLinuxMonitorInfo() {
    if (!function_exists('shell_exec')) {
        return 'Permission Denied';
    }

    try {
        // ลองหาข้อมูลจาก xrandr หรือ /sys/class/drm
        $output = shell_exec('xrandr --query 2>/dev/null | grep " connected"');
        if ($output) {
            $monitors = [];
            $lines = explode("\n", trim($output));
            foreach ($lines as $line) {
                if (strpos($line, 'connected') !== false) {
                    $parts = explode(' ', $line);
                    if (!empty($parts[0])) {
                        $monitors[] = $parts[0];
                    }
                }
            }
            return !empty($monitors) ? implode(', ', $monitors) : 'Unknown';
        }

        // ลองวิธีอื่น
        $output = shell_exec('ls /sys/class/drm/*/status 2>/dev/null | xargs grep -l "^connected$" | head -5');
        if ($output) {
            return 'Connected Displays Found';
        }
    } catch (Exception $e) {
        return 'Error: ' . $e->getMessage();
    }

    return 'Unknown';
}

// ฟังก์ชันสำหรับ macOS
function getMacInfo($command) {
    if (!function_exists('shell_exec')) {
        return 'Permission Denied';
    }

    try {
        $output = shell_exec($command . ' 2>/dev/null');
        if ($output) {
            $parts = explode(':', $output, 2);
            return count($parts) == 2 ? trim($parts[1]) : trim($output);
        }
    } catch (Exception $e) {
        return 'Error: ' . $e->getMessage();
    }

    return 'Unknown';
}

function getMacMonitorInfo() {
    if (!function_exists('shell_exec')) {
        return 'Permission Denied';
    }

    try {
        $output = shell_exec('system_profiler SPDisplaysDataType 2>/dev/null | grep "Display Type"');
        return $output ? trim($output) : 'Unknown';
    } catch (Exception $e) {
        return 'Error: ' . $e->getMessage();
    }
}

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

// ดึงข้อมูล hardware
$hardwareInfo = getHardwareInfo();

// จัดการการบันทึกข้อมูล
$saveMessage = '';
if ($_POST['action'] ?? '' === 'save_hardware') {
    $saveMessage = saveHardwareData($hardwareInfo);
}

// ฟังก์ชันบันทึกข้อมูล Hardware
function saveHardwareData($data) {
    try {
        $filename = 'hardware_data_' . date('Y-m-d_H-i-s') . '.json';
        $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        if (file_put_contents($filename, $jsonData)) {
            return ['type' => 'success', 'message' => 'บันทึกข้อมูล Hardware สำเร็จ: ' . $filename];
        } else {
            return ['type' => 'error', 'message' => 'ไม่สามารถบันทึกข้อมูลได้'];
        }
    } catch (Exception $e) {
        return ['type' => 'error', 'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()];
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ข้อมูล Hardware - Asset Management System</title>
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <link rel="stylesheet" href="assets/style.css">
    
    <style>
        .hardware-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .hardware-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .hardware-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
        }
        
        .hardware-card h3 {
            color: #2d3748;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4299e1;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .hardware-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f7fafc;
        }
        
        .hardware-item:last-child {
            border-bottom: none;
        }
        
        .hardware-label {
            font-weight: 600;
            color: #4a5568;
            flex: 1;
        }
        
        .hardware-value {
            color: #2d3748;
            font-family: 'Courier New', monospace;
            background: #f7fafc;
            padding: 4px 8px;
            border-radius: 4px;
            flex: 2;
            text-align: right;
            word-break: break-all;
        }
        
        .extensions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .extension-item {
            background: #f7fafc;
            padding: 8px 12px;
            border-radius: 6px;
            border-left: 3px solid #4299e1;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .refresh-btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
            transition: background 0.3s;
        }
        
        .refresh-btn:hover {
            background: #3182ce;
        }
        
        .status-good {
            color: #38a169;
            font-weight: bold;
        }
        
        .status-warning {
            color: #d69e2e;
            font-weight: bold;
        }
        
        .status-error {
            color: #e53e3e;
            font-weight: bold;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #4299e1;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            color: #3182ce;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ข้อมูล Hardware</h1>
            <p class="subtitle">Hardware Information - ข้อมูลฮาร์ดแวร์และระบบ</p>
        </div>
    </div>

    <div class="hardware-container">
        <a href="index.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            กลับสู่หน้าหลัก
        </a>
        
        <div style="display: flex; gap: 10px; margin-bottom: 20px;">
            <button class="refresh-btn" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i>
                รีเฟรชข้อมูล
            </button>

            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="save_hardware">
                <button type="submit" class="refresh-btn" style="background: #38a169;">
                    <i class="fas fa-save"></i>
                    บันทึกข้อมูล
                </button>
            </form>
        </div>

        <?php if (!empty($saveMessage)): ?>
        <div style="padding: 10px; margin-bottom: 20px; border-radius: 6px; <?php echo $saveMessage['type'] === 'success' ? 'background: #d4edda; color: #155724; border: 1px solid #c3e6cb;' : 'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;'; ?>">
            <i class="fas <?php echo $saveMessage['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?>"></i>
            <?php echo htmlspecialchars($saveMessage['message']); ?>
        </div>
        <?php endif; ?>

        <div class="hardware-grid">
            <!-- ข้อมูล Hardware หลัก -->
            <div class="hardware-card">
                <h3>
                    <i class="fas fa-desktop"></i>
                    ข้อมูล Hardware
                </h3>
                <div class="hardware-item">
                    <span class="hardware-label">Host Name:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['hostname']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">IP Address:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['ip_address']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">OS Name:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['os_name']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">System Manufacturer:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['system_manufacturer']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">System Model:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['system_model']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Serial Number:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['serial_number']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Monitor:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['monitor']); ?></span>
                </div>
            </div>

            <!-- ข้อมูลระบบเสริม -->
            <div class="hardware-card">
                <h3>
                    <i class="fas fa-info-circle"></i>
                    ข้อมูลระบบเสริม
                </h3>
                <div class="hardware-item">
                    <span class="hardware-label">OS Full Info:</span>
                    <span class="hardware-value" style="font-size: 0.8em;"><?php echo htmlspecialchars($hardwareInfo['os_full']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">PHP Version:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['php_version']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Web Server:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['server_software']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Server Time:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['server_time']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Client IP:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['client_ip']); ?></span>
                </div>
            </div>

            <!-- สถานะการเชื่อมต่อ -->
            <div class="hardware-card">
                <h3>
                    <i class="fas fa-signal"></i>
                    สถานะการเชื่อมต่อ
                </h3>
                <div class="hardware-item">
                    <span class="hardware-label">Connection Status:</span>
                    <span class="hardware-value status-good">Connected</span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Data Collection:</span>
                    <span class="hardware-value status-good">Active</span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Last Updated:</span>
                    <span class="hardware-value"><?php echo date('Y-m-d H:i:s'); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Collection Method:</span>
                    <span class="hardware-value">
                        <?php
                        $os_type = strtolower(php_uname('s'));
                        if (strpos($os_type, 'windows') !== false) {
                            echo 'Windows WMI';
                        } elseif (strpos($os_type, 'linux') !== false) {
                            echo 'Linux DMI';
                        } elseif (strpos($os_type, 'darwin') !== false) {
                            echo 'macOS System Profiler';
                        } else {
                            echo 'Generic PHP';
                        }
                        ?>
                    </span>
                </div>
            </div>

            <!-- คำแนะนำการใช้งาน -->
            <div class="hardware-card">
                <h3>
                    <i class="fas fa-lightbulb"></i>
                    คำแนะนำการใช้งาน
                </h3>
                <div style="padding: 10px; background: #f8f9fa; border-radius: 6px; margin-top: 10px;">
                    <p style="margin: 0; font-size: 0.9em; line-height: 1.5;">
                        <strong>วิธีการใช้งาน:</strong><br>
                        1. เข้าหน้านี้จากเครื่องที่ต้องการเก็บข้อมูล<br>
                        2. ระบบจะดึงข้อมูล Hardware อัตโนมัติ<br>
                        3. บันทึกข้อมูลเพื่อใช้ในการจัดการ Asset<br>
                        4. ข้อมูลจะอัปเดตทุกครั้งที่เข้าหน้านี้
                    </p>
                </div>
                <div style="padding: 10px; background: #fff3cd; border-radius: 6px; margin-top: 10px;">
                    <p style="margin: 0; font-size: 0.9em; line-height: 1.5;">
                        <strong>หมายเหตุ:</strong><br>
                        บางข้อมูลอาจต้องการสิทธิ์ Administrator หรือ Root<br>
                        เพื่อเข้าถึงข้อมูล Hardware ระดับต่ำ
                    </p>
                </div>
            </div>
        </div>

        <!-- ข้อมูล JSON สำหรับ Export -->
        <div class="hardware-card" style="margin-top: 20px;">
            <h3>
                <i class="fas fa-code"></i>
                ข้อมูล Hardware (JSON Format)
            </h3>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-top: 10px;">
                <pre style="margin: 0; font-size: 0.8em; white-space: pre-wrap; word-wrap: break-word;"><?php echo htmlspecialchars(json_encode($hardwareInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
            </div>
            <div style="margin-top: 10px;">
                <button onclick="copyToClipboard()" class="refresh-btn" style="background: #6f42c1;">
                    <i class="fas fa-copy"></i>
                    คัดลอก JSON
                </button>
            </div>
        </div>
    </div>

    <script>
        // Auto refresh every 60 seconds
        setTimeout(function() {
            location.reload();
        }, 60000);

        // Add loading animation to refresh button
        document.querySelector('.refresh-btn').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> กำลังรีเฟรช...';
        });

        // Copy JSON to clipboard
        function copyToClipboard() {
            const jsonData = <?php echo json_encode(json_encode($hardwareInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?>;

            if (navigator.clipboard) {
                navigator.clipboard.writeText(jsonData).then(function() {
                    alert('คัดลอกข้อมูล JSON สำเร็จ!');
                }).catch(function(err) {
                    console.error('ไม่สามารถคัดลอกได้: ', err);
                    fallbackCopyTextToClipboard(jsonData);
                });
            } else {
                fallbackCopyTextToClipboard(jsonData);
            }
        }

        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    alert('คัดลอกข้อมูล JSON สำเร็จ!');
                } else {
                    alert('ไม่สามารถคัดลอกได้');
                }
            } catch (err) {
                alert('ไม่สามารถคัดลอกได้');
            }

            document.body.removeChild(textArea);
        }

        // Show hardware collection status
        console.log('Hardware Data Collection Status: Active');
        console.log('Last Updated: <?php echo date('Y-m-d H:i:s'); ?>');
    </script>
</body>
</html>
