<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// ตรวจสอบการล็อกอิน
requireLogin();

// ฟังก์ชันดึงข้อมูล hardware
function getHardwareInfo() {
    $hardware = [];
    
    // ข้อมูลระบบปฏิบัติการ
    $hardware['os'] = php_uname();
    $hardware['php_version'] = phpversion();
    $hardware['server_software'] = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
    
    // ข้อมูล CPU
    if (function_exists('sys_getloadavg')) {
        $load = sys_getloadavg();
        $hardware['cpu_load'] = $load[0] ?? 'N/A';
    } else {
        $hardware['cpu_load'] = 'N/A';
    }
    
    // ข้อมูลหน่วยความจำ
    if (function_exists('memory_get_usage')) {
        $hardware['memory_usage'] = formatBytes(memory_get_usage(true));
        $hardware['memory_peak'] = formatBytes(memory_get_peak_usage(true));
    }
    
    // ข้อมูลดิสก์
    $hardware['disk_free'] = formatBytes(disk_free_space('.'));
    $hardware['disk_total'] = formatBytes(disk_total_space('.'));
    
    // ข้อมูลเครือข่าย
    $hardware['server_ip'] = $_SERVER['SERVER_ADDR'] ?? gethostbyname(gethostname());
    $hardware['client_ip'] = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $hardware['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    
    // ข้อมูลเวลา
    $hardware['server_time'] = date('Y-m-d H:i:s');
    $hardware['timezone'] = date_default_timezone_get();
    
    return $hardware;
}

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

// ดึงข้อมูล hardware
$hardwareInfo = getHardwareInfo();

// ดึงข้อมูล PHP extensions
$phpExtensions = get_loaded_extensions();
sort($phpExtensions);

// ดึงข้อมูล PHP configuration
$phpConfig = [
    'max_execution_time' => ini_get('max_execution_time'),
    'memory_limit' => ini_get('memory_limit'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_file_uploads' => ini_get('max_file_uploads'),
    'session_save_path' => ini_get('session.save_path'),
    'error_reporting' => ini_get('error_reporting'),
    'display_errors' => ini_get('display_errors') ? 'On' : 'Off',
    'log_errors' => ini_get('log_errors') ? 'On' : 'Off'
];
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ข้อมูล Hardware - Asset Management System</title>
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <link rel="stylesheet" href="assets/style.css">
    
    <style>
        .hardware-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .hardware-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .hardware-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
        }
        
        .hardware-card h3 {
            color: #2d3748;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4299e1;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .hardware-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f7fafc;
        }
        
        .hardware-item:last-child {
            border-bottom: none;
        }
        
        .hardware-label {
            font-weight: 600;
            color: #4a5568;
            flex: 1;
        }
        
        .hardware-value {
            color: #2d3748;
            font-family: 'Courier New', monospace;
            background: #f7fafc;
            padding: 4px 8px;
            border-radius: 4px;
            flex: 2;
            text-align: right;
            word-break: break-all;
        }
        
        .extensions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .extension-item {
            background: #f7fafc;
            padding: 8px 12px;
            border-radius: 6px;
            border-left: 3px solid #4299e1;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .refresh-btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
            transition: background 0.3s;
        }
        
        .refresh-btn:hover {
            background: #3182ce;
        }
        
        .status-good {
            color: #38a169;
            font-weight: bold;
        }
        
        .status-warning {
            color: #d69e2e;
            font-weight: bold;
        }
        
        .status-error {
            color: #e53e3e;
            font-weight: bold;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #4299e1;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            color: #3182ce;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ข้อมูล Hardware</h1>
            <p class="subtitle">Hardware Information - ข้อมูลฮาร์ดแวร์และระบบ</p>
        </div>
    </div>

    <div class="hardware-container">
        <a href="index.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            กลับสู่หน้าหลัก
        </a>
        
        <button class="refresh-btn" onclick="location.reload()">
            <i class="fas fa-sync-alt"></i>
            รีเฟรชข้อมูล
        </button>

        <div class="hardware-grid">
            <!-- ข้อมูลระบบ -->
            <div class="hardware-card">
                <h3>
                    <i class="fas fa-desktop"></i>
                    ข้อมูลระบบ
                </h3>
                <div class="hardware-item">
                    <span class="hardware-label">ระบบปฏิบัติการ:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['os']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">เวอร์ชัน PHP:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['php_version']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Web Server:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['server_software']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">เวลาเซิร์ฟเวอร์:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['server_time']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Timezone:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['timezone']); ?></span>
                </div>
            </div>

            <!-- ข้อมูลประสิทธิภาพ -->
            <div class="hardware-card">
                <h3>
                    <i class="fas fa-tachometer-alt"></i>
                    ประสิทธิภาพระบบ
                </h3>
                <div class="hardware-item">
                    <span class="hardware-label">CPU Load:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['cpu_load']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Memory Usage:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['memory_usage']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Peak Memory:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['memory_peak']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Disk Free:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['disk_free']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Disk Total:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['disk_total']); ?></span>
                </div>
            </div>

            <!-- ข้อมูลเครือข่าย -->
            <div class="hardware-card">
                <h3>
                    <i class="fas fa-network-wired"></i>
                    ข้อมูลเครือข่าย
                </h3>
                <div class="hardware-item">
                    <span class="hardware-label">Server IP:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['server_ip']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">Client IP:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($hardwareInfo['client_ip']); ?></span>
                </div>
                <div class="hardware-item">
                    <span class="hardware-label">User Agent:</span>
                    <span class="hardware-value" style="font-size: 0.8em;"><?php echo htmlspecialchars($hardwareInfo['user_agent']); ?></span>
                </div>
            </div>

            <!-- การตั้งค่า PHP -->
            <div class="hardware-card">
                <h3>
                    <i class="fab fa-php"></i>
                    การตั้งค่า PHP
                </h3>
                <?php foreach ($phpConfig as $key => $value): ?>
                <div class="hardware-item">
                    <span class="hardware-label"><?php echo htmlspecialchars($key); ?>:</span>
                    <span class="hardware-value"><?php echo htmlspecialchars($value); ?></span>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- PHP Extensions -->
        <div class="hardware-card" style="margin-top: 20px;">
            <h3>
                <i class="fas fa-puzzle-piece"></i>
                PHP Extensions (<?php echo count($phpExtensions); ?> รายการ)
            </h3>
            <div class="extensions-grid">
                <?php foreach ($phpExtensions as $extension): ?>
                    <div class="extension-item"><?php echo htmlspecialchars($extension); ?></div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <script>
        // Auto refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
        
        // Add loading animation to refresh button
        document.querySelector('.refresh-btn').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> กำลังรีเฟรช...';
        });
    </script>
</body>
</html>
