<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'vendor/autoload.php';

// ตรวจสอบการล็อกอิน
requireLogin();

// สร้าง AssetManager instance
$assetManager = new AssetManager($pdo);

// รับค่าการกรอง (ถ้ามี)
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';

// ดึงข้อมูล assets ทั้งหมดตามเงื่อนไขการกรอง
$assets = $assetManager->getAllAssets($search, $filter_type, $filter_brand, $filter_status, $filter_os, $filter_department);

// Create mPDF object with configuration for Thai language support (mPDF v8.0)
$mpdf = new \Mpdf\Mpdf([
    'mode' => 'utf-8',
    'format' => 'A4-L',
    'default_font_size' => 11,
    'default_font' => 'thsarabunnew',
    'margin_left' => 10,
    'margin_right' => 10,
    'margin_top' => 15,
    'margin_bottom' => 15,
    'margin_header' => 8,
    'margin_footer' => 8,
    'orientation' => 'L',
    'autoScriptToLang' => true,
    'autoLangToFont' => true,
]);

// Set document properties
$mpdf->SetTitle('รายงานระบบจัดการ Asset');
$mpdf->SetAuthor('Asset Management System');
$mpdf->SetCreator('Asset Management System');

// Set page numbering
$mpdf->SetFooter('หน้า {PAGENO} จาก {nb}');

// สร้างตัวแปรสำหรับข้อมูลรายงาน
$title = 'รายงานระบบจัดการ Asset';
$filters = [
    'search' => $search,
    'type' => $filter_type,
    'brand' => $filter_brand,
    'department' => $filter_department,
    'status' => $filter_status,
    'operating_system' => $filter_os
];
$totalAssets = count($assets);

// Get current date for report
$reportDate = date('d/m/Y H:i:s');

// สร้าง HTML content
$content = '
<style>
body {
    font-family: "thsarabunnew";
    font-size: 10pt;
    line-height: 1.3;
    margin: 0;
    padding: 0;
}
.header-section {
    border-bottom: 2px solid #333;
    padding-bottom: 10px;
    margin-bottom: 15px;
}
.title {
    font-size: 18pt;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin: 0 0 5px 0;
}
.subtitle {
    font-size: 10pt;
    color: #666;
    text-align: center;
    margin: 0 0 8px 0;
}
.info-section {
    font-size: 9pt;
    color: #666;
    text-align: right;
    margin: 0;
}
.filters-section {
    background-color: #f9f9f9;
    padding: 8px;
    margin: 10px 0;
    border-left: 3px solid #666;
    font-size: 9pt;
}
.filters-title {
    font-weight: bold;
    color: #333;
    margin-bottom: 3px;
}
.filters-content {
    color: #666;
}
table {
    border-collapse: collapse;
    width: 100%;
    font-size: 8pt;
    margin-top: 10px;
}
th {
    background-color: #333;
    color: white;
    font-weight: bold;
    padding: 6px 3px;
    text-align: center;
    border: 1px solid #ccc;
    font-size: 8pt;
}
td {
    padding: 4px 3px;
    border: 1px solid #ccc;
    text-align: left;
    vertical-align: top;
}
tr:nth-child(even) {
    background-color: #f9f9f9;
}
.status-active { color: #28a745; font-weight: bold; }
.status-damaged { color: #dc3545; font-weight: bold; }
.status-spare { color: #ffc107; font-weight: bold; }
.summary-section {
    margin-top: 20px;
    border-top: 1px solid #ccc;
    padding-top: 15px;
}
.summary-title {
    font-size: 12pt;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin-bottom: 10px;
}
.summary-columns {
    display: table;
    width: 100%;
}
.summary-column {
    display: table-cell;
    width: 33.33%;
    vertical-align: top;
    padding: 0 8px;
}
.summary-column-title {
    font-weight: bold;
    color: #333;
    font-size: 9pt;
    margin-bottom: 5px;
    border-bottom: 1px solid #ccc;
    padding-bottom: 2px;
}
.summary-item {
    font-size: 8pt;
    margin-bottom: 2px;
    line-height: 1.2;
}
.total-box {
    background-color: #f0f0f0;
    padding: 8px;
    text-align: center;
    margin-top: 10px;
    border: 1px solid #ccc;
}
.total-text {
    font-size: 11pt;
    font-weight: bold;
    color: #333;
}
.report-footer {
    font-size: 8pt;
    color: #666;
    margin-top: 3px;
}
</style>

<div class="header-section">
    <div class="title">' . $title . '</div>
    <div class="subtitle">Asset Management System Report</div>
    <div class="info-section">
        วันที่สร้าง: ' . $reportDate . ' | ผู้สร้าง: ' . getCurrentUserFullName() . ' | จำนวนรายการ: ' . $totalAssets . ' รายการ
    </div>
</div>';

// เพิ่มส่วนของ filters ถ้ามี
$hasFilters = false;
$filterText = '';
foreach ($filters as $key => $value) {
    if (!empty($value)) {
        $hasFilters = true;
        switch($key) {
            case 'search':
                $filterText .= 'ค้นหา: "' . $value . '"  ';
                break;
            case 'type':
                $filterText .= 'ประเภท: ' . $value . '  ';
                break;
            case 'brand':
                $filterText .= 'ยี่ห้อ: ' . $value . '  ';
                break;
            case 'department':
                $filterText .= 'แผนก: ' . $value . '  ';
                break;
            case 'status':
                $filterText .= 'สถานะ: ' . $value . '  ';
                break;
            case 'operating_system':
                $filterText .= 'OS: ' . $value . '  ';
                break;
        }
    }
}

if ($hasFilters) {
    $content .= '
    <div class="filters-section">
        <div class="filters-title">เงื่อนไขการกรอง:</div>
        <div class="filters-content">' . $filterText . '</div>
    </div>';
}

// เพิ่มตารางข้อมูล assets
$content .= '
<table>
<thead>
<tr>
    <th style="width: 8%;">ลำดับ</th>
    <th style="width: 10%;">Asset ID</th>
    <th style="width: 8%;">Type</th>
    <th style="width: 8%;">Brand</th>
    <th style="width: 12%;">Model</th>
    <th style="width: 6%;">Tag</th>
    <th style="width: 12%;">Department</th>
    <th style="width: 7%;">Status</th>
    <th style="width: 8%;">Hostname</th>
    <th style="width: 6%;">OS</th>
    <th style="width: 10%;">Serial Number</th>
    <th style="width: 5%;">Warranty</th>
</tr>
</thead>
<tbody>';

// Helper function to truncate text
function truncateText($text, $maxLength) {
    if (strlen($text) > $maxLength) {
        return substr($text, 0, $maxLength - 3) . '...';
    }
    return $text;
}

if (!empty($assets)) {
    $sequenceNumber = 1;
    foreach ($assets as $asset) {
        $status = $asset['status'] ?? '';
        $statusClass = '';
        if ($status == 'ใช้งาน') {
            $statusClass = 'status-active';
        } elseif ($status == 'ชำรุด') {
            $statusClass = 'status-damaged';
        } elseif ($status == 'สำรอง') {
            $statusClass = 'status-spare';
        }

        // Format warranty date
        $warranty_exp = '';
        if (!empty($asset['warranty_expire'])) {
            $warranty_exp = date('d/m/y', strtotime($asset['warranty_expire']));
        }

        $content .= '
        <tr>
            <td style="text-align: center;">' . $sequenceNumber . '</td>
            <td>' . htmlspecialchars(truncateText($asset['asset_id'] ?? '', 12)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['type'] ?? '', 10)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['brand'] ?? '', 10)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['model'] ?? '', 15)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['tag'] ?? '', 8)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['department'] ?? '', 15)) . '</td>
            <td class="' . $statusClass . '" style="text-align: center;">' . htmlspecialchars($status) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['hostname'] ?? '', 10)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['operating_system'] ?? '', 8)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['serial_number'] ?? '', 12)) . '</td>
            <td style="text-align: center;">' . htmlspecialchars($warranty_exp) . '</td>
        </tr>';
        $sequenceNumber++;
    }
} else {
    $content .= '
    <tr>
        <td colspan="12" style="text-align: center; font-style: italic; color: #999; padding: 20px;">
            ไม่พบข้อมูล Asset ที่ตรงกับเงื่อนไขการค้นหา
        </td>
    </tr>';
}

$content .= '
</tbody>
</table>';

// เพิ่มส่วนสรุปรายงาน
if (!empty($assets)) {
    // Count by status, type, brand
    $statusCount = [];
    $typeCount = [];
    $brandCount = [];

    foreach ($assets as $asset) {
        $status = $asset['status'] ?? 'ไม่ระบุ';
        $type = $asset['type'] ?? 'ไม่ระบุ';
        $brand = $asset['brand'] ?? 'ไม่ระบุ';

        $statusCount[$status] = ($statusCount[$status] ?? 0) + 1;
        $typeCount[$type] = ($typeCount[$type] ?? 0) + 1;
        $brandCount[$brand] = ($brandCount[$brand] ?? 0) + 1;
    }

    $content .= '
    <div class="summary-section">
        <div class="summary-title">สรุปรายงาน</div>

        <div class="summary-columns">
            <div class="summary-column">
                <div class="summary-column-title">สถานะ Asset</div>';

    foreach ($statusCount as $status => $count) {
        $percentage = round(($count / count($assets)) * 100, 1);
        $statusClass = '';
        if ($status == 'ใช้งาน') {
            $statusClass = 'status-active';
        } elseif ($status == 'ชำรุด') {
            $statusClass = 'status-damaged';
        } elseif ($status == 'สำรอง') {
            $statusClass = 'status-spare';
        }

        $content .= '<div class="summary-item"><span class="' . $statusClass . '">' . htmlspecialchars($status) . ': ' . $count . ' (' . $percentage . '%)</span></div>';
    }

    $content .= '
            </div>
            <div class="summary-column">
                <div class="summary-column-title">ประเภท Asset</div>';

    arsort($typeCount);
    $topTypes = array_slice($typeCount, 0, 5, true);
    foreach ($topTypes as $type => $count) {
        $percentage = round(($count / count($assets)) * 100, 1);
        $content .= '<div class="summary-item">' . htmlspecialchars($type) . ': ' . $count . ' (' . $percentage . '%)</div>';
    }

    $content .= '
            </div>
            <div class="summary-column">
                <div class="summary-column-title">ยี่ห้อ</div>';

    arsort($brandCount);
    $topBrands = array_slice($brandCount, 0, 5, true);
    foreach ($topBrands as $brand => $count) {
        $percentage = round(($count / count($assets)) * 100, 1);
        $content .= '<div class="summary-item">' . htmlspecialchars($brand) . ': ' . $count . ' (' . $percentage . '%)</div>';
    }

    $content .= '
            </div>
        </div>

        <div class="total-box">
            <div class="total-text">รวมทั้งหมด: ' . count($assets) . ' รายการ</div>
            <div class="report-footer">รายงานนี้สร้างขึ้นเมื่อ ' . date('d/m/Y เวลา H:i:s น.') . '</div>
        </div>
    </div>';
}

// Write HTML to PDF
$mpdf->WriteHTML($content);

// Output PDF
$filename = 'asset_report_' . date('Y-m-d_H-i-s') . '.pdf';
$mpdf->Output($filename, 'D');
exit;
?>
