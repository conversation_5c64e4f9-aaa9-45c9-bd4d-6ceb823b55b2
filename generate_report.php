<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// ตรวจสอบการล็อกอิน
requireLogin();

// ตรวจสอบว่ามี FPDF หรือไม่
if (!class_exists('FPDF')) {
    // ใช้ FPDF จาก folder fpdf
    if (file_exists('fpdf/fpdf.php')) {
        require_once 'fpdf/fpdf.php';
    } elseif (file_exists('fpdf.php')) {
        require_once 'fpdf.php';
    } else {
        die('Error: FPDF library not found. Please install FPDF in fpdf/ folder.');
    }
}

// สร้าง AssetManager instance
$assetManager = new AssetManager($pdo);

// รับค่าการกรอง (ถ้ามี)
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';

// ดึงข้อมูล assets ทั้งหมดตามเงื่อนไขการกรอง
$assets = $assetManager->getAllAssets($search, $filter_type, $filter_brand, $filter_status, $filter_os, $filter_department);

// สร้าง class สำหรับ PDF Report
class AssetReport extends FPDF
{
    private $title = 'รายงานระบบจัดการ Asset';
    private $filters = [];
    private $totalAssets = 0;
    
    public function setReportTitle($title) {
        $this->title = $title;
    }
    
    public function setFilters($filters) {
        $this->filters = $filters;
    }

    public function setTotalAssets($total) {
        $this->totalAssets = $total;
    }
    
    // Header
    function Header()
    {
        // Add TH Sarabun font
        $this->AddFont('THSarabunNew', '', 'THSarabunNew.php');
        $this->AddFont('THSarabunNew', 'B', 'THSarabunNew_b.php');
        $this->AddFont('THSarabunNew', 'I', 'THSarabunNew_i.php');
        $this->AddFont('THSarabunNew', 'BI', 'THSarabunNew_bi.php');

        // Logo area (สีฟ้าอ่อน)
        $this->SetFillColor(240, 248, 255);
        $this->Rect(10, 10, 190, 25, 'F');

        // Title
        $this->SetFont('THSarabunNew', 'B', 20);
        $this->SetTextColor(25, 25, 112);
        $this->SetXY(15, 18);
        $this->Cell(0, 10, $this->title, 0, 1, 'L');

        // Subtitle
        $this->SetFont('THSarabunNew', '', 12);
        $this->SetTextColor(70, 70, 70);
        $this->SetX(15);
        $this->Cell(0, 5, 'Asset Management System Report', 0, 1, 'L');

        // Date and info
        $this->SetFont('THSarabunNew', '', 10);
        $this->SetTextColor(100, 100, 100);
        $this->SetXY(140, 18);
        $this->Cell(0, 4, 'วันที่สร้าง: ' . date('d/m/Y H:i:s'), 0, 1, 'L');
        $this->SetX(140);
        $this->Cell(0, 4, 'ผู้สร้าง: ' . getCurrentUserFullName(), 0, 1, 'L');
        $this->SetX(140);
        $this->Cell(0, 4, 'จำนวนรายการ: ' . $this->totalAssets . ' รายการ', 0, 1, 'L');

        $this->Ln(8);

        // Filters information
        if (!empty($this->filters)) {
            $hasFilters = false;
            foreach ($this->filters as $value) {
                if (!empty($value)) {
                    $hasFilters = true;
                    break;
                }
            }

            if ($hasFilters) {
                $this->SetFillColor(255, 255, 240);
                $this->Rect(10, $this->GetY(), 190, 15, 'F');

                $this->SetFont('Arial', 'B', 10);
                $this->SetTextColor(139, 69, 19);
                $this->SetX(15);
                $this->Cell(0, 6, 'เงื่อนไขการกรอง:', 0, 1, 'L');

                $this->SetFont('Arial', '', 9);
                $this->SetTextColor(101, 67, 33);
                $filterText = '';

                if (!empty($this->filters['search'])) {
                    $filterText .= 'ค้นหา: "' . $this->filters['search'] . '"  ';
                }
                if (!empty($this->filters['type'])) {
                    $filterText .= 'ประเภท: ' . $this->filters['type'] . '  ';
                }
                if (!empty($this->filters['brand'])) {
                    $filterText .= 'ยี่ห้อ: ' . $this->filters['brand'] . '  ';
                }
                if (!empty($this->filters['department'])) {
                    $filterText .= 'แผนก: ' . $this->filters['department'] . '  ';
                }
                if (!empty($this->filters['status'])) {
                    $filterText .= 'สถานะ: ' . $this->filters['status'] . '  ';
                }
                if (!empty($this->filters['operating_system'])) {
                    $filterText .= 'OS: ' . $this->filters['operating_system'];
                }

                $this->SetX(15);
                $this->Cell(0, 6, $filterText, 0, 1, 'L');
                $this->Ln(5);
            }
        }

        // Table header with gradient effect
        $this->SetFillColor(70, 130, 180);
        $this->SetTextColor(255, 255, 255);
        $this->SetFont('Arial', 'B', 8);

        // Column headers with better spacing
        $this->Cell(28, 10, 'Asset ID', 1, 0, 'C', true);
        $this->Cell(25, 10, 'Type', 1, 0, 'C', true);
        $this->Cell(22, 10, 'Brand', 1, 0, 'C', true);
        $this->Cell(30, 10, 'Model', 1, 0, 'C', true);
        $this->Cell(18, 10, 'Tag', 1, 0, 'C', true);
        $this->Cell(35, 10, 'Department', 1, 0, 'C', true);
        $this->Cell(20, 10, 'Status', 1, 0, 'C', true);
        $this->Cell(12, 10, 'OS', 1, 1, 'C', true);

        // Reset text color
        $this->SetTextColor(0, 0, 0);
    }
    
    // Footer
    function Footer()
    {
        // Position at 1.5 cm from bottom
        $this->SetY(-15);
        // Arial italic 8
        $this->SetFont('Arial', 'I', 8);
        // Page number
        $this->Cell(0, 10, 'Page ' . $this->PageNo() . '/{nb}', 0, 0, 'C');
    }
    
    // Add asset data
    function AddAssetData($assets)
    {
        $this->SetFont('Arial', '', 7);
        $fill = false;
        $rowCount = 0;

        foreach ($assets as $asset) {
            // Check if we need a new page
            if ($this->GetY() > 250) {
                $this->AddPage();
            }

            // Alternate row colors
            if ($fill) {
                $this->SetFillColor(248, 248, 255);
            } else {
                $this->SetFillColor(255, 255, 255);
            }

            // Status color coding
            $status = $asset['status'] ?? '';
            if ($status == 'ใช้งาน') {
                $statusColor = array(40, 167, 69);
            } elseif ($status == 'ชำรุด') {
                $statusColor = array(220, 53, 69);
            } elseif ($status == 'สำรอง') {
                $statusColor = array(255, 193, 7);
            } else {
                $statusColor = array(108, 117, 125);
            }

            // Data cells with proper alignment and truncation
            $this->Cell(28, 8, $this->truncateText($asset['asset_id'] ?? '', 18), 1, 0, 'L', $fill);
            $this->Cell(25, 8, $this->truncateText($asset['type'] ?? '', 20), 1, 0, 'L', $fill);
            $this->Cell(22, 8, $this->truncateText($asset['brand'] ?? '', 18), 1, 0, 'L', $fill);
            $this->Cell(30, 8, $this->truncateText($asset['model'] ?? '', 25), 1, 0, 'L', $fill);
            $this->Cell(18, 8, $this->truncateText($asset['tag'] ?? '', 15), 1, 0, 'L', $fill);
            $this->Cell(35, 8, $this->truncateText($asset['department'] ?? '', 30), 1, 0, 'L', $fill);

            // Status cell with color
            $this->SetTextColor($statusColor[0], $statusColor[1], $statusColor[2]);
            $this->Cell(20, 8, $this->truncateText($status, 15), 1, 0, 'C', $fill);
            $this->SetTextColor(0, 0, 0);

            $this->Cell(12, 8, $this->truncateText($asset['operating_system'] ?? '', 10), 1, 1, 'C', $fill);

            $fill = !$fill;
            $rowCount++;
        }
    }

    // Helper function to truncate text
    private function truncateText($text, $maxLength) {
        if (strlen($text) > $maxLength) {
            return substr($text, 0, $maxLength - 3) . '...';
        }
        return $text;
    }
    
    // Add summary
    function AddSummary($assets)
    {
        $this->Ln(15);

        // Summary header with background
        $this->SetFillColor(245, 245, 245);
        $this->Rect(10, $this->GetY(), 190, 8, 'F');
        $this->SetFont('Arial', 'B', 14);
        $this->SetTextColor(25, 25, 112);
        $this->Cell(0, 8, 'สรุปรายงาน (Summary)', 0, 1, 'C');
        $this->SetTextColor(0, 0, 0);
        $this->Ln(5);

        // Count by status
        $statusCount = [];
        $typeCount = [];
        $brandCount = [];
        $departmentCount = [];

        foreach ($assets as $asset) {
            $status = $asset['status'] ?? 'ไม่ระบุ';
            $type = $asset['type'] ?? 'ไม่ระบุ';
            $brand = $asset['brand'] ?? 'ไม่ระบุ';
            $department = $asset['department'] ?? 'ไม่ระบุ';

            $statusCount[$status] = ($statusCount[$status] ?? 0) + 1;
            $typeCount[$type] = ($typeCount[$type] ?? 0) + 1;
            $brandCount[$brand] = ($brandCount[$brand] ?? 0) + 1;
            $departmentCount[$department] = ($departmentCount[$department] ?? 0) + 1;
        }

        // Summary in columns
        $startY = $this->GetY();

        // Column 1: Status Summary
        $this->SetXY(15, $startY);
        $this->SetFont('Arial', 'B', 11);
        $this->SetTextColor(70, 130, 180);
        $this->Cell(0, 6, 'สถานะ Asset', 0, 1);
        $this->SetX(15);
        $this->SetFont('Arial', '', 9);
        $this->SetTextColor(0, 0, 0);

        foreach ($statusCount as $status => $count) {
            $this->SetX(15);
            $percentage = round(($count / count($assets)) * 100, 1);

            // Status color
            if ($status == 'ใช้งาน') {
                $this->SetTextColor(40, 167, 69);
            } elseif ($status == 'ชำรุด') {
                $this->SetTextColor(220, 53, 69);
            } elseif ($status == 'สำรอง') {
                $this->SetTextColor(255, 193, 7);
            } else {
                $this->SetTextColor(108, 117, 125);
            }

            $this->Cell(0, 5, '• ' . $status . ': ' . $count . ' (' . $percentage . '%)', 0, 1);
        }
        $this->SetTextColor(0, 0, 0);

        // Column 2: Top Types
        $this->SetXY(70, $startY);
        $this->SetFont('Arial', 'B', 11);
        $this->SetTextColor(70, 130, 180);
        $this->Cell(0, 6, 'ประเภท Asset ยอดนิยม', 0, 1);
        $this->SetX(70);
        $this->SetFont('Arial', '', 9);
        $this->SetTextColor(0, 0, 0);

        arsort($typeCount);
        $topTypes = array_slice($typeCount, 0, 5, true);
        foreach ($topTypes as $type => $count) {
            $this->SetX(70);
            $percentage = round(($count / count($assets)) * 100, 1);
            $this->Cell(0, 5, '• ' . $type . ': ' . $count . ' (' . $percentage . '%)', 0, 1);
        }

        // Column 3: Top Brands
        $this->SetXY(125, $startY);
        $this->SetFont('Arial', 'B', 11);
        $this->SetTextColor(70, 130, 180);
        $this->Cell(0, 6, 'ยี่ห้อยอดนิยม', 0, 1);
        $this->SetX(125);
        $this->SetFont('Arial', '', 9);
        $this->SetTextColor(0, 0, 0);

        arsort($brandCount);
        $topBrands = array_slice($brandCount, 0, 5, true);
        foreach ($topBrands as $brand => $count) {
            $this->SetX(125);
            $percentage = round(($count / count($assets)) * 100, 1);
            $this->Cell(0, 5, '• ' . $brand . ': ' . $count . ' (' . $percentage . '%)', 0, 1);
        }

        $this->Ln(10);

        // Total summary box
        $this->SetFillColor(240, 248, 255);
        $this->Rect(10, $this->GetY(), 190, 15, 'F');
        $this->SetFont('Arial', 'B', 12);
        $this->SetTextColor(25, 25, 112);
        $this->Cell(0, 8, 'รวมทั้งหมด: ' . count($assets) . ' รายการ', 0, 1, 'C');
        $this->SetFont('Arial', '', 9);
        $this->SetTextColor(70, 70, 70);
        $this->Cell(0, 5, 'รายงานนี้สร้างขึ้นเมื่อ ' . date('d/m/Y เวลา H:i:s น.'), 0, 1, 'C');
        $this->SetTextColor(0, 0, 0);
    }
}

// สร้าง PDF
$pdf = new AssetReport();
$pdf->AliasNbPages();

// ตั้งค่า title และ filters
$pdf->setReportTitle('รายงานระบบจัดการ Asset');
$filters = [
    'search' => $search,
    'type' => $filter_type,
    'brand' => $filter_brand,
    'department' => $filter_department,
    'status' => $filter_status,
    'operating_system' => $filter_os
];
$pdf->setFilters($filters);
$pdf->setTotalAssets(count($assets));

// เพิ่มหน้าแรก
$pdf->AddPage();

// เพิ่มข้อมูล assets
if (!empty($assets)) {
    $pdf->AddAssetData($assets);
} else {
    $pdf->SetFont('Arial', 'I', 12);
    $pdf->SetTextColor(150, 150, 150);
    $pdf->Cell(0, 20, 'ไม่พบข้อมูล Asset ที่ตรงกับเงื่อนไขการค้นหา', 0, 1, 'C');
    $pdf->SetTextColor(0, 0, 0);
}

// เพิ่มสรุป
$pdf->AddSummary($assets);

// ส่งออก PDF
$filename = 'asset_report_' . date('Y-m-d_H-i-s') . '.pdf';
$pdf->Output('D', $filename);
exit;
?>
