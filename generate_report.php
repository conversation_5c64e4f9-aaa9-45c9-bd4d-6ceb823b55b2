<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'vendor/autoload.php';

// ตรวจสอบการล็อกอิน
requireLogin();

// สร้าง AssetManager instance
$assetManager = new AssetManager($pdo);

// รับค่าการกรอง (ถ้ามี)
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';

// ดึงข้อมูล assets ทั้งหมดตามเงื่อนไขการกรอง
$assets = $assetManager->getAllAssets($search, $filter_type, $filter_brand, $filter_status, $filter_os, $filter_department);

// Create mPDF object with configuration for Thai language support
$mpdf = new \Mpdf\Mpdf([
    'mode' => 'utf-8',
    'format' => 'A4',
    'default_font_size' => 12,
    'default_font' => 'dejavusans',
    'margin_left' => 15,
    'margin_right' => 15,
    'margin_top' => 16,
    'margin_bottom' => 16,
    'margin_header' => 9,
    'margin_footer' => 9,
    'orientation' => 'P', // Portrait orientation
    'autoScriptToLang' => true,
    'autoLangToFont' => true,
]);

// Set document properties
$mpdf->SetTitle('รายงานระบบจัดการ Asset');
$mpdf->SetAuthor('Asset Management System');
$mpdf->SetCreator('Asset Management System');

// Set page numbering
$mpdf->SetFooter('หน้า {PAGENO} จาก {nb}');

// สร้างตัวแปรสำหรับข้อมูลรายงาน
$title = 'รายงานระบบจัดการ Asset';
$filters = [
    'search' => $search,
    'type' => $filter_type,
    'brand' => $filter_brand,
    'department' => $filter_department,
    'status' => $filter_status,
    'operating_system' => $filter_os
];
$totalAssets = count($assets);

// Get current date for report
$reportDate = date('d/m/Y H:i:s');

// สร้าง HTML content
$content = '
<style>
body {
    font-family: "dejavusans";
    font-size: 12pt;
    line-height: 1.4;
}
.header-section {
    background-color: #f0f8ff;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
}
.title {
    font-size: 24pt;
    font-weight: bold;
    color: #191970;
    text-align: center;
    margin-bottom: 5px;
}
.subtitle {
    font-size: 12pt;
    color: #464646;
    text-align: center;
    margin-bottom: 10px;
}
.info-section {
    font-size: 10pt;
    color: #646464;
    text-align: right;
}
.filters-section {
    background-color: #fffff0;
    padding: 10px;
    margin: 15px 0;
    border-left: 4px solid #8b4513;
}
.filters-title {
    font-weight: bold;
    color: #8b4513;
    font-size: 12pt;
    margin-bottom: 5px;
}
.filters-content {
    font-size: 11pt;
    color: #654321;
}
table {
    border-collapse: collapse;
    width: 100%;
    font-size: 10pt;
    margin-top: 10px;
}
th {
    background-color: #4682b4;
    color: white;
    font-weight: bold;
    padding: 8px 4px;
    text-align: center;
    border: 1px solid #ddd;
}
td {
    padding: 6px 4px;
    border: 1px solid #ddd;
    text-align: left;
}
tr:nth-child(even) {
    background-color: #f8f8ff;
}
.status-active { color: #28a745; font-weight: bold; }
.status-damaged { color: #dc3545; font-weight: bold; }
.status-spare { color: #ffc107; font-weight: bold; }
.summary-section {
    margin-top: 30px;
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 5px;
}
.summary-title {
    font-size: 16pt;
    font-weight: bold;
    color: #191970;
    text-align: center;
    margin-bottom: 15px;
}
.summary-columns {
    display: table;
    width: 100%;
}
.summary-column {
    display: table-cell;
    width: 33.33%;
    vertical-align: top;
    padding: 0 10px;
}
.summary-column-title {
    font-weight: bold;
    color: #4682b4;
    font-size: 11pt;
    margin-bottom: 8px;
}
.summary-item {
    font-size: 9pt;
    margin-bottom: 3px;
}
.total-box {
    background-color: #f0f8ff;
    padding: 10px;
    text-align: center;
    margin-top: 15px;
    border-radius: 5px;
}
.total-text {
    font-size: 14pt;
    font-weight: bold;
    color: #191970;
}
.report-footer {
    font-size: 9pt;
    color: #464646;
    margin-top: 5px;
}
</style>

<div class="header-section">
    <div class="title">' . $title . '</div>
    <div class="subtitle">Asset Management System Report</div>
    <div class="info-section">
        วันที่สร้าง: ' . $reportDate . '<br>
        ผู้สร้าง: ' . getCurrentUserFullName() . '<br>
        จำนวนรายการ: ' . $totalAssets . ' รายการ
    </div>
</div>';

// เพิ่มส่วนของ filters ถ้ามี
$hasFilters = false;
$filterText = '';
foreach ($filters as $key => $value) {
    if (!empty($value)) {
        $hasFilters = true;
        switch($key) {
            case 'search':
                $filterText .= 'ค้นหา: "' . $value . '"  ';
                break;
            case 'type':
                $filterText .= 'ประเภท: ' . $value . '  ';
                break;
            case 'brand':
                $filterText .= 'ยี่ห้อ: ' . $value . '  ';
                break;
            case 'department':
                $filterText .= 'แผนก: ' . $value . '  ';
                break;
            case 'status':
                $filterText .= 'สถานะ: ' . $value . '  ';
                break;
            case 'operating_system':
                $filterText .= 'OS: ' . $value . '  ';
                break;
        }
    }
}

if ($hasFilters) {
    $content .= '
    <div class="filters-section">
        <div class="filters-title">เงื่อนไขการกรอง:</div>
        <div class="filters-content">' . $filterText . '</div>
    </div>';
}

// เพิ่มตารางข้อมูล assets
$content .= '
<table>
<thead>
<tr>
    <th style="width: 12%;">Asset ID</th>
    <th style="width: 10%;">Type</th>
    <th style="width: 10%;">Brand</th>
    <th style="width: 12%;">Model</th>
    <th style="width: 8%;">Tag</th>
    <th style="width: 15%;">Department</th>
    <th style="width: 10%;">Status</th>
    <th style="width: 8%;">OS</th>
    <th style="width: 8%;">SN</th>
    <th style="width: 7%;">Warranty</th>
</tr>
</thead>
<tbody>';

// Helper function to truncate text
function truncateText($text, $maxLength) {
    if (strlen($text) > $maxLength) {
        return substr($text, 0, $maxLength - 3) . '...';
    }
    return $text;
}

if (!empty($assets)) {
    foreach ($assets as $asset) {
        $status = $asset['status'] ?? '';
        $statusClass = '';
        if ($status == 'ใช้งาน') {
            $statusClass = 'status-active';
        } elseif ($status == 'ชำรุด') {
            $statusClass = 'status-damaged';
        } elseif ($status == 'สำรอง') {
            $statusClass = 'status-spare';
        }

        // Format warranty date
        $warranty_exp = '';
        if (!empty($asset['warranty_expire'])) {
            $warranty_exp = date('d/m/Y', strtotime($asset['warranty_expire']));
        }

        $content .= '
        <tr>
            <td>' . htmlspecialchars(truncateText($asset['asset_id'] ?? '', 15)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['type'] ?? '', 12)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['brand'] ?? '', 12)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['model'] ?? '', 15)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['tag'] ?? '', 10)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['department'] ?? '', 18)) . '</td>
            <td class="' . $statusClass . '">' . htmlspecialchars($status) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['operating_system'] ?? '', 8)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['serial_number'] ?? '', 12)) . '</td>
            <td>' . htmlspecialchars($warranty_exp) . '</td>
        </tr>';
    }
} else {
    $content .= '
    <tr>
        <td colspan="10" style="text-align: center; font-style: italic; color: #999; padding: 20px;">
            ไม่พบข้อมูล Asset ที่ตรงกับเงื่อนไขการค้นหา
        </td>
    </tr>';
}

$content .= '
</tbody>
</table>';

// เพิ่มส่วนสรุปรายงาน
if (!empty($assets)) {
    // Count by status, type, brand
    $statusCount = [];
    $typeCount = [];
    $brandCount = [];

    foreach ($assets as $asset) {
        $status = $asset['status'] ?? 'ไม่ระบุ';
        $type = $asset['type'] ?? 'ไม่ระบุ';
        $brand = $asset['brand'] ?? 'ไม่ระบุ';

        $statusCount[$status] = ($statusCount[$status] ?? 0) + 1;
        $typeCount[$type] = ($typeCount[$type] ?? 0) + 1;
        $brandCount[$brand] = ($brandCount[$brand] ?? 0) + 1;
    }

    $content .= '
    <div class="summary-section">
        <div class="summary-title">สรุปรายงาน (Summary)</div>

        <div class="summary-columns">
            <div class="summary-column">
                <div class="summary-column-title">สถานะ Asset</div>';

    foreach ($statusCount as $status => $count) {
        $percentage = round(($count / count($assets)) * 100, 1);
        $statusClass = '';
        if ($status == 'ใช้งาน') {
            $statusClass = 'status-active';
        } elseif ($status == 'ชำรุด') {
            $statusClass = 'status-damaged';
        } elseif ($status == 'สำรอง') {
            $statusClass = 'status-spare';
        }

        $content .= '<div class="summary-item"><span class="' . $statusClass . '">• ' . htmlspecialchars($status) . ': ' . $count . ' (' . $percentage . '%)</span></div>';
    }

    $content .= '
            </div>
            <div class="summary-column">
                <div class="summary-column-title">ประเภท Asset ยอดนิยม</div>';

    arsort($typeCount);
    $topTypes = array_slice($typeCount, 0, 5, true);
    foreach ($topTypes as $type => $count) {
        $percentage = round(($count / count($assets)) * 100, 1);
        $content .= '<div class="summary-item">• ' . htmlspecialchars($type) . ': ' . $count . ' (' . $percentage . '%)</div>';
    }

    $content .= '
            </div>
            <div class="summary-column">
                <div class="summary-column-title">ยี่ห้อยอดนิยม</div>';

    arsort($brandCount);
    $topBrands = array_slice($brandCount, 0, 5, true);
    foreach ($topBrands as $brand => $count) {
        $percentage = round(($count / count($assets)) * 100, 1);
        $content .= '<div class="summary-item">• ' . htmlspecialchars($brand) . ': ' . $count . ' (' . $percentage . '%)</div>';
    }

    $content .= '
            </div>
        </div>

        <div class="total-box">
            <div class="total-text">รวมทั้งหมด: ' . count($assets) . ' รายการ</div>
            <div class="report-footer">รายงานนี้สร้างขึ้นเมื่อ ' . date('d/m/Y เวลา H:i:s น.') . '</div>
        </div>
    </div>';
}

// Write HTML to PDF
$mpdf->WriteHTML($content);

// Output PDF
$filename = 'asset_report_' . date('Y-m-d_H-i-s') . '.pdf';
$mpdf->Output($filename, 'D');
exit;
?>
