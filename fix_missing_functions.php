<?php
/**
 * Fix Missing Functions - แก้ไขฟังก์ชันที่ขาดหายไป
 * Asset Management System
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 แก้ไขฟังก์ชันที่ขาดหายไป</h1>";

// ตรวจสอบและเพิ่มฟังก์ชันที่จำเป็น
if (!function_exists('formatDateTime')) {
    function formatDateTime($datetime) {
        if (empty($datetime) || $datetime === '0000-00-00 00:00:00') {
            return '-';
        }
        
        try {
            $date = new DateTime($datetime);
            return $date->format('d/m/Y H:i:s');
        } catch (Exception $e) {
            return $datetime;
        }
    }
    echo "<p style='color: green;'>✅ เพิ่มฟังก์ชัน formatDateTime</p>";
}

if (!function_exists('formatDate')) {
    function formatDate($date) {
        if (empty($date) || $date === '0000-00-00') {
            return '-';
        }
        
        try {
            $dateObj = new DateTime($date);
            return $dateObj->format('d/m/Y');
        } catch (Exception $e) {
            return $date;
        }
    }
    echo "<p style='color: green;'>✅ เพิ่มฟังก์ชัน formatDate</p>";
}

if (!function_exists('getCurrentUserFullName')) {
    function getCurrentUserFullName() {
        return $_SESSION['full_name'] ?? $_SESSION['username'] ?? 'Unknown';
    }
    echo "<p style='color: green;'>✅ เพิ่มฟังก์ชัน getCurrentUserFullName</p>";
}

if (!function_exists('isAdmin')) {
    function isAdmin() {
        return isset($_SESSION['role']) && $_SESSION['role'] === 'Admin';
    }
    echo "<p style='color: green;'>✅ เพิ่มฟังก์ชัน isAdmin</p>";
}

if (!function_exists('requireLogin')) {
    function requireLogin() {
        if (!isset($_SESSION['user_id'])) {
            header('Location: login.php');
            exit;
        }
    }
    echo "<p style='color: green;'>✅ เพิ่มฟังก์ชัน requireLogin</p>";
}

if (!function_exists('logAssetCreation')) {
    function logAssetCreation($pdo, $assetId, $username) {
        try {
            $stmt = $pdo->prepare("INSERT INTO asset_logs (asset_id, action, new_values, changed_by, changed_date) VALUES (?, 'create', ?, ?, NOW())");
            $stmt->execute([$assetId, json_encode(['action' => 'Asset created']), $username]);
        } catch (Exception $e) {
            error_log("Log creation error: " . $e->getMessage());
        }
    }
    echo "<p style='color: green;'>✅ เพิ่มฟังก์ชัน logAssetCreation</p>";
}

if (!function_exists('logAssetChanges')) {
    function logAssetChanges($pdo, $assetId, $oldData, $newData, $username) {
        try {
            $stmt = $pdo->prepare("INSERT INTO asset_logs (asset_id, action, old_values, new_values, changed_by, changed_date) VALUES (?, 'update', ?, ?, ?, NOW())");
            $stmt->execute([$assetId, json_encode($oldData), json_encode($newData), $username]);
        } catch (Exception $e) {
            error_log("Log changes error: " . $e->getMessage());
        }
    }
    echo "<p style='color: green;'>✅ เพิ่มฟังก์ชัน logAssetChanges</p>";
}

if (!function_exists('getStatusBadge')) {
    function getStatusBadge($status) {
        $badges = [
            'ใช้งาน' => 'success',
            'ชำรุด' => 'danger',
            'สำรอง' => 'warning'
        ];
        $class = $badges[$status] ?? 'secondary';
        return "<span class='badge badge-{$class}'>{$status}</span>";
    }
    echo "<p style='color: green;'>✅ เพิ่มฟังก์ชัน getStatusBadge</p>";
}

// ตรวจสอบว่ามีการ include auto backup functions หรือไม่
if (file_exists('includes/auto_backup.php')) {
    require_once 'includes/auto_backup.php';
    echo "<p style='color: green;'>✅ โหลด auto backup functions</p>";
} else {
    // สร้างฟังก์ชัน backup triggers แบบ dummy
    if (!function_exists('triggerAssetAddBackup')) {
        function triggerAssetAddBackup($assetId, $assetData) {
            // Dummy function - ไม่ทำอะไร
            return true;
        }
        echo "<p style='color: orange;'>⚠️ เพิ่มฟังก์ชัน triggerAssetAddBackup (dummy)</p>";
    }
    
    if (!function_exists('triggerAssetEditBackup')) {
        function triggerAssetEditBackup($assetId, $oldData, $newData) {
            // Dummy function - ไม่ทำอะไร
            return true;
        }
        echo "<p style='color: orange;'>⚠️ เพิ่มฟังก์ชัน triggerAssetEditBackup (dummy)</p>";
    }
    
    if (!function_exists('triggerAssetDeleteBackup')) {
        function triggerAssetDeleteBackup($assetId, $assetData) {
            // Dummy function - ไม่ทำอะไร
            return true;
        }
        echo "<p style='color: orange;'>⚠️ เพิ่มฟังก์ชัน triggerAssetDeleteBackup (dummy)</p>";
    }
    
    if (!function_exists('triggerUserAddBackup')) {
        function triggerUserAddBackup($userId, $userData) {
            // Dummy function - ไม่ทำอะไร
            return true;
        }
        echo "<p style='color: orange;'>⚠️ เพิ่มฟังก์ชัน triggerUserAddBackup (dummy)</p>";
    }
    
    if (!function_exists('triggerUserEditBackup')) {
        function triggerUserEditBackup($userId, $oldData, $newData) {
            // Dummy function - ไม่ทำอะไร
            return true;
        }
        echo "<p style='color: orange;'>⚠️ เพิ่มฟังก์ชัน triggerUserEditBackup (dummy)</p>";
    }
    
    if (!function_exists('triggerUserDeleteBackup')) {
        function triggerUserDeleteBackup($userId, $userData) {
            // Dummy function - ไม่ทำอะไร
            return true;
        }
        echo "<p style='color: orange;'>⚠️ เพิ่มฟังก์ชัน triggerUserDeleteBackup (dummy)</p>";
    }
}

// ตรวจสอบ AssetManager class
if (!class_exists('AssetManager')) {
    class AssetManager {
        private $pdo;
        
        public function __construct($pdo) {
            $this->pdo = $pdo;
        }
        
        public function getAssetById($id) {
            $stmt = $this->pdo->prepare("SELECT * FROM assets WHERE id = ?");
            $stmt->execute([$id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        public function updateAsset($id, $data) {
            $fields = [];
            $values = [];
            
            foreach ($data as $key => $value) {
                $fields[] = "$key = ?";
                $values[] = $value;
            }
            
            $values[] = $id;
            $sql = "UPDATE assets SET " . implode(', ', $fields) . " WHERE id = ?";
            
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute($values);
        }
        
        public function deleteAsset($id) {
            $stmt = $this->pdo->prepare("DELETE FROM assets WHERE id = ?");
            return $stmt->execute([$id]);
        }
        
        public function createAsset($data) {
            $fields = array_keys($data);
            $placeholders = array_fill(0, count($fields), '?');
            
            $sql = "INSERT INTO assets (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
            
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute(array_values($data));
        }
    }
    echo "<p style='color: green;'>✅ เพิ่มคลาส AssetManager</p>";
}

// ตรวจสอบ Auth class
if (!class_exists('Auth')) {
    class Auth {
        private $pdo;
        
        public function __construct($pdo) {
            $this->pdo = $pdo;
        }
        
        public function isLoggedIn() {
            return isset($_SESSION['user_id']);
        }
        
        public function login($username, $password) {
            $stmt = $this->pdo->prepare("SELECT * FROM users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && password_verify($password, $user['password'])) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['role'] = $user['role'];
                return true;
            }
            
            return false;
        }
        
        public function logout() {
            session_destroy();
        }
        
        public function createUser($userData) {
            $hashedPassword = password_hash($userData['password'], PASSWORD_DEFAULT);
            
            $stmt = $this->pdo->prepare("INSERT INTO users (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)");
            return $stmt->execute([
                $userData['username'],
                $hashedPassword,
                $userData['full_name'],
                $userData['email'],
                $userData['role']
            ]);
        }
    }
    echo "<p style='color: green;'>✅ เพิ่มคลาส Auth</p>";
}

// สร้างไฟล์ functions.php ถ้าไม่มี
if (!file_exists('includes/functions.php')) {
    $functionsContent = '<?php
// Asset Management System - Functions
// Auto-generated by fix_missing_functions.php

// Format functions
function formatDateTime($datetime) {
    if (empty($datetime) || $datetime === "0000-00-00 00:00:00") {
        return "-";
    }
    
    try {
        $date = new DateTime($datetime);
        return $date->format("d/m/Y H:i:s");
    } catch (Exception $e) {
        return $datetime;
    }
}

function formatDate($date) {
    if (empty($date) || $date === "0000-00-00") {
        return "-";
    }
    
    try {
        $dateObj = new DateTime($date);
        return $dateObj->format("d/m/Y");
    } catch (Exception $e) {
        return $date;
    }
}

// User functions
function getCurrentUserFullName() {
    return $_SESSION["full_name"] ?? $_SESSION["username"] ?? "Unknown";
}

function isAdmin() {
    return isset($_SESSION["role"]) && $_SESSION["role"] === "Admin";
}

function requireLogin() {
    if (!isset($_SESSION["user_id"])) {
        header("Location: login.php");
        exit;
    }
}

// Status badge function
function getStatusBadge($status) {
    $badges = [
        "ใช้งาน" => "success",
        "ชำรุด" => "danger", 
        "สำรอง" => "warning"
    ];
    $class = $badges[$status] ?? "secondary";
    return "<span class=\"badge badge-{$class}\">{$status}</span>";
}

// Logging functions
function logAssetCreation($pdo, $assetId, $username) {
    try {
        $stmt = $pdo->prepare("INSERT INTO asset_logs (asset_id, action, new_values, changed_by, changed_date) VALUES (?, \"create\", ?, ?, NOW())");
        $stmt->execute([$assetId, json_encode([\"action\" => \"Asset created\"]), $username]);
    } catch (Exception $e) {
        error_log("Log creation error: " . $e->getMessage());
    }
}

function logAssetChanges($pdo, $assetId, $oldData, $newData, $username) {
    try {
        $stmt = $pdo->prepare("INSERT INTO asset_logs (asset_id, action, old_values, new_values, changed_by, changed_date) VALUES (?, \"update\", ?, ?, ?, NOW())");
        $stmt->execute([$assetId, json_encode($oldData), json_encode($newData), $username]);
    } catch (Exception $e) {
        error_log("Log changes error: " . $e->getMessage());
    }
}

// Backup trigger functions (dummy implementations)
function triggerAssetAddBackup($assetId, $assetData) { return true; }
function triggerAssetEditBackup($assetId, $oldData, $newData) { return true; }
function triggerAssetDeleteBackup($assetId, $assetData) { return true; }
function triggerUserAddBackup($userId, $userData) { return true; }
function triggerUserEditBackup($userId, $oldData, $newData) { return true; }
function triggerUserDeleteBackup($userId, $userData) { return true; }
?>';

    if (file_put_contents('includes/functions.php', $functionsContent)) {
        echo "<p style='color: green;'>✅ สร้างไฟล์ includes/functions.php</p>";
    } else {
        echo "<p style='color: red;'>❌ ไม่สามารถสร้างไฟล์ includes/functions.php</p>";
    }
}

echo "<h2>✅ การแก้ไขเสร็จสิ้น</h2>";
echo "<p>ฟังก์ชันที่จำเป็นได้ถูกเพิ่มแล้ว ระบบควรทำงานได้ปกติ</p>";
echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ทดสอบระบบ</a></p>";
echo "<p><a href='fix_errors.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ตรวจสอบข้อผิดพลาดอื่น</a></p>";
?>
