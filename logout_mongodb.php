<?php
/**
 * MongoDB Logout
 * Asset Management System - MongoDB Version
 */

require_once 'includes/mongodb_auth.php';

// Perform logout
$result = $mongoAuth->logout();

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ออกจากระบบ - Asset Management (MongoDB)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'TH Sarabun New', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .logout-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            text-align: center;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logout-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 40px 20px;
        }

        .logout-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .logout-header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .logout-content {
            padding: 40px 30px;
        }

        .logout-icon {
            font-size: 64px;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .logout-message {
            font-size: 18px;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #28a745;
            color: #28a745;
        }

        .btn-outline:hover {
            background: #28a745;
            color: white;
        }

        .countdown {
            font-size: 14px;
            color: #6c757d;
            margin-top: 20px;
        }

        .mongodb-info {
            background: #f8f9fa;
            padding: 20px;
            border-top: 1px solid #e9ecef;
            font-size: 14px;
            color: #6c757d;
        }

        .mongodb-info h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #28a745;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
            }
        }

        @media (max-width: 480px) {
            .logout-container {
                margin: 10px;
            }
            
            .logout-content {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="logout-container">
        <div class="logout-header">
            <h1>👋 ออกจากระบบแล้ว</h1>
            <p>Asset Management System</p>
        </div>

        <div class="logout-content">
            <div class="logout-icon">🔐</div>
            
            <?php if ($result['success']): ?>
                <div class="logout-message">
                    <strong>✅ ออกจากระบบสำเร็จ</strong><br>
                    ขอบคุณที่ใช้งานระบบ Asset Management<br>
                    เซสชันของคุณได้ถูกล้างออกจาก MongoDB แล้ว
                </div>
            <?php else: ?>
                <div class="logout-message">
                    <strong>⚠️ เกิดข้อผิดพลาด</strong><br>
                    <?= htmlspecialchars($result['message']) ?><br>
                    กรุณาปิดเบราว์เซอร์เพื่อความปลอดภัย
                </div>
            <?php endif; ?>

            <div>
                <a href="login_mongodb.php" class="btn">🔑 เข้าสู่ระบบอีกครั้ง</a>
                <a href="mongodb_test.php" class="btn btn-outline">🧪 ทดสอบระบบ</a>
            </div>

            <div class="countdown">
                <span class="status-indicator"></span>
                จะเปลี่ยนเส้นทางไปหน้าล็อกอินใน <span id="countdown">10</span> วินาที
            </div>
        </div>

        <div class="mongodb-info">
            <h4>🍃 MongoDB Session Management</h4>
            <p>✅ Session ถูกลบออกจาก MongoDB</p>
            <p>✅ Remember Me Cookie ถูกล้าง</p>
            <p>✅ PHP Session ถูกทำลาย</p>
            <p>🔒 ข้อมูลของคุณปลอดภัย</p>
        </div>
    </div>

    <script>
        // Countdown timer
        let countdown = 10;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(function() {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = 'login_mongodb.php';
            }
        }, 1000);

        // Allow user to cancel auto-redirect
        document.addEventListener('click', function() {
            clearInterval(timer);
            countdownElement.parentElement.innerHTML = '<span class="status-indicator"></span>การเปลี่ยนเส้นทางอัตโนมัติถูกยกเลิก';
        });

        // Clear any remaining session data
        if (typeof(Storage) !== "undefined") {
            localStorage.clear();
            sessionStorage.clear();
        }

        // Clear any cached data
        if ('caches' in window) {
            caches.keys().then(function(names) {
                for (let name of names) {
                    caches.delete(name);
                }
            });
        }

        // Prevent back button
        history.pushState(null, null, location.href);
        window.onpopstate = function() {
            history.go(1);
        };

        // Add some visual feedback
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px) scale(1.05)';
            });
            
            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Show logout success animation
        setTimeout(function() {
            const icon = document.querySelector('.logout-icon');
            icon.style.transform = 'scale(1.2)';
            icon.style.color = '#28a745';
            
            setTimeout(function() {
                icon.style.transform = 'scale(1)';
            }, 300);
        }, 500);

        // Log logout event (for analytics)
        console.log('User logged out successfully from MongoDB system');
        
        // Optional: Send logout event to server for logging
        fetch('api/log_event.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                event: 'user_logout',
                timestamp: new Date().toISOString(),
                system: 'mongodb'
            })
        }).catch(error => {
            // Ignore errors for logging
            console.log('Logout event logging failed:', error);
        });
    </script>
</body>
</html>
