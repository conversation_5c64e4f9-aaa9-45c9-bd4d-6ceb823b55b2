<?php
/**
 * Database Configuration for MySQL 8.0
 * Asset Management System
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_PORT', '3305');
define('DB_NAME', 'asset_management');
define('DB_USER', 'root');
define('DB_PASS', 'Wxmujwsofu@1234'); // ใส่รหัสผ่าน MySQL ของคุณที่นี่
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// Timezone setting
define('DB_TIMEZONE', '+07:00');

// MySQL 8.0 specific settings
define('MYSQL_SSL_VERIFY', false);
define('MYSQL_SQL_MODE', 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO');

try {
    // PDO options for MySQL 8.0
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::ATTR_PERSISTENT => false,
        
        // MySQL 8.0 specific options
        PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => MYSQL_SSL_VERIFY,
        PDO::MYSQL_ATTR_SSL_CA => null,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET . " COLLATE " . DB_COLLATE,
        
        // Connection timeout
        PDO::ATTR_TIMEOUT => 30,
    ];

    // Create DSN
    $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    
    // Create PDO connection
    $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
    
    // Set additional MySQL 8.0 settings
    $pdo->exec("SET time_zone = '" . DB_TIMEZONE . "'");
    $pdo->exec("SET sql_mode = '" . MYSQL_SQL_MODE . "'");
    $pdo->exec("SET NAMES " . DB_CHARSET . " COLLATE " . DB_COLLATE);
    
    // Set session variables for better compatibility
    $pdo->exec("SET SESSION innodb_strict_mode = 1");
    $pdo->exec("SET SESSION foreign_key_checks = 1");
    
} catch (PDOException $e) {
    // If database doesn't exist, try to create it
    if ($e->getCode() == 1049) {
        try {
            // Connect without database name
            $dsn_create = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";charset=" . DB_CHARSET;
            $pdo_create = new PDO($dsn_create, DB_USER, DB_PASS, $options);
            
            // Create database
            $pdo_create->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " 
                              CHARACTER SET " . DB_CHARSET . " 
                              COLLATE " . DB_COLLATE);
            
            // Reconnect to the new database
            $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
            
            // Set MySQL 8.0 settings
            $pdo->exec("SET time_zone = '" . DB_TIMEZONE . "'");
            $pdo->exec("SET sql_mode = '" . MYSQL_SQL_MODE . "'");
            $pdo->exec("SET NAMES " . DB_CHARSET . " COLLATE " . DB_COLLATE);
            $pdo->exec("SET SESSION innodb_strict_mode = 1");
            $pdo->exec("SET SESSION foreign_key_checks = 1");
            
            // Create tables if they don't exist
            createTables($pdo);
            
        } catch (PDOException $e2) {
            die("Database connection failed: " . $e2->getMessage() . " (Error Code: " . $e2->getCode() . ")");
        }
    } else {
        die("Database connection failed: " . $e->getMessage() . " (Error Code: " . $e->getCode() . ")");
    }
}

/**
 * Create database tables
 */
function createTables($pdo) {
    // Check if setup.sql exists
    $sqlFile = __DIR__ . '/../sql/setup.sql';
    
    if (file_exists($sqlFile)) {
        $sql = file_get_contents($sqlFile);
        
        // Split SQL statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^(CREATE DATABASE|USE)/i', $statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // Skip if table already exists
                    if ($e->getCode() != '42S01') {
                        error_log("SQL Error: " . $e->getMessage() . " - Statement: " . $statement);
                    }
                }
            }
        }
    } else {
        // Create basic tables if setup.sql doesn't exist
        createBasicTables($pdo);
    }
}

/**
 * Create basic tables for MySQL 8.0
 */
function createBasicTables($pdo) {
    $tables = [
        // Users table
        "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100),
            email VARCHAR(100),
            role ENUM('Admin', 'User') DEFAULT 'User',
            status ENUM('Active', 'Inactive') DEFAULT 'Active',
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_username (username),
            INDEX idx_role (role),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // Assets table
        "CREATE TABLE IF NOT EXISTS assets (
            id INT AUTO_INCREMENT PRIMARY KEY,
            asset_id VARCHAR(50),
            tag VARCHAR(50),
            type VARCHAR(100),
            brand VARCHAR(100),
            model VARCHAR(100),
            hostname VARCHAR(100),
            operating_system VARCHAR(100),
            serial_number VARCHAR(100),
            status VARCHAR(50),
            department VARCHAR(100),
            warranty_expire DATE,
            description TEXT,
            set_name VARCHAR(100),
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by VARCHAR(100),
            updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            updated_by VARCHAR(100),
            INDEX idx_asset_id (asset_id),
            INDEX idx_tag (tag),
            INDEX idx_type (type),
            INDEX idx_brand (brand),
            INDEX idx_status (status),
            INDEX idx_department (department),
            INDEX idx_created_date (created_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // Asset logs table
        "CREATE TABLE IF NOT EXISTS asset_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            asset_id INT,
            action VARCHAR(50),
            old_values JSON,
            new_values JSON,
            changed_by VARCHAR(100),
            changed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address VARCHAR(45),
            user_agent TEXT,
            INDEX idx_asset_id (asset_id),
            INDEX idx_action (action),
            INDEX idx_changed_date (changed_date),
            FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ];
    
    foreach ($tables as $sql) {
        try {
            $pdo->exec($sql);
        } catch (PDOException $e) {
            error_log("Table creation error: " . $e->getMessage());
        }
    }
    
    // Create default admin user
    createDefaultUser($pdo);
}

/**
 * Create default admin user
 */
function createDefaultUser($pdo) {
    try {
        // Check if admin user exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
        $stmt->execute(['admin']);
        
        if ($stmt->fetchColumn() == 0) {
            // Create admin user
            $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute(['admin', $hashedPassword, 'Administrator', '<EMAIL>', 'Admin']);
        }
    } catch (PDOException $e) {
        error_log("Default user creation error: " . $e->getMessage());
    }
}

/**
 * Get database connection
 */
function getConnection() {
    global $pdo;
    return $pdo;
}

/**
 * Test database connection
 */
function testConnection() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT 1");
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Get MySQL version
 */
function getMySQLVersion() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT VERSION() as version");
        $result = $stmt->fetch();
        return $result['version'];
    } catch (PDOException $e) {
        return 'Unknown';
    }
}
?>
