<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// ตรวจสอบการล็อกอิน
requireLogin();

$assetManager = new AssetManager($pdo);

// รับค่าการค้นหาและกรอง
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';

// รับข้อความแจ้งเตือน
$message = $_GET['message'] ?? '';
$messageType = $_GET['type'] ?? 'success';

// Pagination settings
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 25; // จำนวนแถวต่อหน้า
$offset = ($page - 1) * $limit;

// ดึงข้อมูล assets ทั้งหมดเพื่อนับจำนวน
$allAssets = $assetManager->getAllAssets($search, $filter_type, $filter_brand, $filter_status, $filter_os, $filter_department);
$totalAssets = count($allAssets);
$totalPages = ceil($totalAssets / $limit);

// ดึงข้อมูล assets สำหรับหน้าปัจจุบัน
$assets = array_slice($allAssets, $offset, $limit);

$assetTypes = $assetManager->getAssetTypes();
$assetBrands = $assetManager->getAssetBrands();
$assetDepartments = $assetManager->getAssetDepartments();
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asset Management System</title>

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ระบบจัดการ Asset</h1>
            <p class="subtitle">Asset Management System - จัดการทรัพย์สินองค์กรอย่างมีประสิทธิภาพ</p>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="index.php" class="active"><i class="fas fa-list"></i> รายการ Assets</a></li>
                <?php if (isAdmin()): ?>
                    <li><a href="users.php"><i class="fas fa-users"></i> จัดการผู้ใช้</a></li>
                <?php endif; ?>
                <li><a href="#" onclick="openToolsModal()"><i class="fas fa-tools"></i> เครื่องมือ</a></li>
                <li><a href="#" onclick="openProfileModal()"><i class="fas fa-user"></i> โปรไฟล์</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> ออกจากระบบ</a></li>
            </ul>
        </nav>



        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- Dashboard Stats -->
        <?php
        $totalAssetsCount = count($assetManager->getAllAssets());
        $activeAssets = count($assetManager->getAllAssets('', '', '', 'ใช้งาน'));
        $brokenAssets = count($assetManager->getAllAssets('', '', '', 'ชำรุด'));
        $spareAssets = count($assetManager->getAllAssets('', '', '', 'สำรอง'));
        ?>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= $totalAssetsCount ?></div>
                    <div class="stat-label">รวมทั้งหมด</div>
                </div>
            </div>
            <div class="stat-card success">
                <div class="stat-icon success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= $activeAssets ?></div>
                    <div class="stat-label">ใช้งาน</div>
                </div>
            </div>
            <div class="stat-card danger">
                <div class="stat-icon danger">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= $brokenAssets ?></div>
                    <div class="stat-label">ชำรุด</div>
                </div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon warning">
                    <i class="fas fa-box"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= $spareAssets ?></div>
                    <div class="stat-label">สำรอง</div>
                </div>
            </div>
        </div>



        <!-- Search and Filter -->
        <div class="search-filter">
            <h3>ค้นหาและกรองข้อมูล</h3>
            <form method="GET" action="">
                <div class="search-row">
                    <div class="form-group">
                        <label for="search">ค้นหา</label>
                        <input type="text" id="search" name="search" class="form-control"
                               placeholder="ค้นหาจาก Type, Brand, Model, Tag, Department, Serial Number, Hostname, Asset ID"
                               value="<?= htmlspecialchars($search) ?>">
                    </div>
                    <div class="form-group">
                        <label for="filter_type">ประเภท</label>
                        <select id="filter_type" name="filter_type" class="form-control">
                            <option value="">ทั้งหมด</option>
                            <?php foreach ($assetTypes as $type): ?>
                                <option value="<?= htmlspecialchars($type) ?>"
                                        <?= $filter_type === $type ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($type) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filter_brand">Brand</label>
                        <select id="filter_brand" name="filter_brand" class="form-control">
                            <option value="">ทั้งหมด</option>
                            <?php foreach ($assetBrands as $brand): ?>
                                <option value="<?= htmlspecialchars($brand) ?>"
                                        <?= $filter_brand === $brand ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($brand) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filter_department">แผนก</label>
                        <select id="filter_department" name="filter_department" class="form-control">
                            <option value="">ทั้งหมด</option>
                            <?php foreach ($assetDepartments as $department): ?>
                                <option value="<?= htmlspecialchars($department) ?>"
                                        <?= $filter_department === $department ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($department) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filter_status">สถานะ</label>
                        <select id="filter_status" name="filter_status" class="form-control">
                            <option value="">ทั้งหมด</option>
                            <option value="ใช้งาน" <?= $filter_status === 'ใช้งาน' ? 'selected' : '' ?>>ใช้งาน</option>
                            <option value="ชำรุด" <?= $filter_status === 'ชำรุด' ? 'selected' : '' ?>>ชำรุด</option>
                            <option value="สำรอง" <?= $filter_status === 'สำรอง' ? 'selected' : '' ?>>สำรอง</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="filter_os">Operating System</label>
                        <select id="filter_os" name="filter_os" class="form-control">
                            <option value="">ทั้งหมด</option>
                            <option value="-" <?= $filter_os === '-' ? 'selected' : '' ?>>-</option>
                            <option value="Windows 7" <?= $filter_os === 'Windows 7' ? 'selected' : '' ?>>Windows 7</option>
                            <option value="Windows 10" <?= $filter_os === 'Windows 10' ? 'selected' : '' ?>>Windows 10</option>
                            <option value="Windows 11" <?= $filter_os === 'Windows 11' ? 'selected' : '' ?>>Windows 11</option>
                            <option value="MacOS" <?= $filter_os === 'MacOS' ? 'selected' : '' ?>>MacOS</option>
                        </select>
                    </div>

                    <div class="search-actions">
                        <button type="submit" class="btn btn-primary">🔍 ค้นหา</button>
                        <a href="index.php" class="btn btn-secondary">🔄 รีเซ็ต</a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Assets Table -->
        <div class="card">
            <div class="card-header">
                <div class="header-content">
                    <h2>รายการ Assets (<?= count($assets) ?> รายการ)</h2>
                    <div class="header-actions">
                        <?php if (isAdmin()): ?>
                            <button onclick="openAddAssetModal()" class="btn btn-success">
                                <i class="fas fa-plus"></i> 
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($assets)): ?>
                    <div class="empty-state">
                        <div class="icon">📦</div>
                        <h3>ไม่พบข้อมูล Asset</h3>
                        <p>ไม่มี Asset ที่ตรงกับเงื่อนไขการค้นหา หรือยังไม่มีข้อมูล Asset ในระบบ</p>
                        <?php if (isAdmin()): ?>
                            <button onclick="openAddAssetModal()" class="btn btn-primary">➕ เพิ่ม Asset แรก</button>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Asset ID</th>
                                    <th>Type</th>
                                    <th>Brand</th>
                                    <th>Model</th>
                                    <th>Tag</th>
                                    <th>Department</th>
                                    <th>Status</th>
                                    <th>Serial Number</th>
                                    <th>Hostname</th>
                                    <th>วันที่เพิ่ม</th>
                                    <th>การจัดการ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($assets as $asset): ?>
                                    <tr>
                                        <td><span class="asset-id"><?= htmlspecialchars($asset['asset_id']) ?></span></td>
                                        <td><?= htmlspecialchars($asset['type']) ?></td>
                                        <td><?= htmlspecialchars($asset['brand']) ?></td>
                                        <td><?= htmlspecialchars($asset['model']) ?></td>
                                        <td><?= htmlspecialchars($asset['tag']) ?></td>
                                        <td><?= htmlspecialchars($asset['department']) ?></td>
                                        <td><?= getStatusBadge($asset['status']) ?></td>
                                        <td><?= htmlspecialchars($asset['serial_number']) ?></td>
                                        <td><?= htmlspecialchars($asset['hostname']) ?></td>
                                        <td><?= formatDateTime($asset['created_date']) ?></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button onclick="viewAssetDetails(<?= $asset['id'] ?>)" class="btn btn-primary" title="ดูรายละเอียด">👁️</button>
                                                <button onclick="openEditAssetModal(<?= $asset['id'] ?>)" class="btn btn-warning" title="แก้ไข">✏️</button>
                                                <a href="delete_asset.php?id=<?= $asset['id'] ?>" class="btn btn-danger" title="ลบ"
                                                   onclick="return confirm('คุณแน่ใจหรือไม่ที่จะลบ Asset นี้?')">🗑️</a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination-container">
                            <div class="pagination-info">
                                <span>แสดง <?= $offset + 1 ?> - <?= min($offset + $limit, $totalAssets) ?> จาก <?= $totalAssets ?> รายการ</span>
                            </div>
                            <div class="pagination">
                                <?php if ($page > 1): ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => 1])) ?>" class="pagination-btn">«</a>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>" class="pagination-btn">‹</a>
                                <?php endif; ?>

                                <?php
                                $start = max(1, $page - 2);
                                $end = min($totalPages, $page + 2);

                                for ($i = $start; $i <= $end; $i++):
                                ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"
                                       class="pagination-btn <?= $i == $page ? 'active' : '' ?>"><?= $i ?></a>
                                <?php endfor; ?>

                                <?php if ($page < $totalPages): ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>" class="pagination-btn">›</a>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $totalPages])) ?>" class="pagination-btn">»</a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // Auto submit form when filter changes
        document.getElementById('filter_type').addEventListener('change', function() {
            this.form.submit();
        });

        document.getElementById('filter_status').addEventListener('change', function() {
            this.form.submit();
        });

        document.getElementById('filter_os').addEventListener('change', function() {
            this.form.submit();
        });

        document.getElementById('filter_brand').addEventListener('change', function() {
            this.form.submit();
        });

        document.getElementById('filter_department').addEventListener('change', function() {
            this.form.submit();
        });



        // Print report function
        function printReport() {
            // สร้าง URL สำหรับ generate report พร้อมกับ filter parameters
            const urlParams = new URLSearchParams(window.location.search);
            const reportUrl = 'generate_report.php?' + urlParams.toString();

            // เปิด URL ใน tab ใหม่เพื่อดาวน์โหลดไฟล์ PDF
            window.open(reportUrl, '_blank');
        }

        // Add loading animation for stats
        document.addEventListener('DOMContentLoaded', function() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = parseInt(stat.textContent);
                let currentValue = 0;
                const increment = Math.ceil(finalValue / 20);

                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    stat.textContent = currentValue;
                }, 50);
            });
        });

        // Add search highlight
        function highlightSearch() {
            const searchTerm = document.getElementById('search').value.toLowerCase();
            if (!searchTerm) return;

            const tableRows = document.querySelectorAll('.table tbody tr');
            tableRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                cells.forEach(cell => {
                    const text = cell.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        cell.style.backgroundColor = '#fff3cd';
                    }
                });
            });
        }

        // Call highlight on page load if search term exists
        document.addEventListener('DOMContentLoaded', highlightSearch);

        // Export data function
        function exportData() {
            // สร้าง URL สำหรับ export พร้อมกับ filter parameters
            const urlParams = new URLSearchParams(window.location.search);
            const exportUrl = 'export_assets.php?' + urlParams.toString();

            // เปิด URL ใน tab ใหม่เพื่อดาวน์โหลดไฟล์
            window.open(exportUrl, '_blank');
        }



        // Backup data function
        function backupData() {
            if (confirm('คุณต้องการสำรองข้อมูลหรือไม่?')) {
                alert('ฟีเจอร์สำรองข้อมูลจะพัฒนาในอนาคต');
            }
        }

        // Tools Modal functions
        function openToolsModal() {
            document.getElementById('toolsModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeToolsModal() {
            document.getElementById('toolsModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Department Report Modal functions
        function openDepartmentReportModal() {
            document.getElementById('departmentReportModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeDepartmentReportModal() {
            document.getElementById('departmentReportModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function generateDepartmentReport() {
            const department = document.getElementById('department_select').value;
            if (!department) {
                alert('กรุณาเลือกแผนก');
                return;
            }

            // สร้าง URL สำหรับรายงานแผนก PDF
            const url = 'department_report.php?department=' + encodeURIComponent(department);
            window.open(url, '_blank');
            closeDepartmentReportModal();
        }

        function generateDepartmentExcel() {
            const department = document.getElementById('department_select').value;
            if (!department) {
                alert('กรุณาเลือกแผนก');
                return;
            }

            // สร้าง URL สำหรับรายงานแผนก Excel
            const url = 'department_excel.php?department=' + encodeURIComponent(department);
            window.open(url, '_blank');
            closeDepartmentReportModal();
        }

        // Export Excel function
        function exportExcel() {
            // รวบรวมข้อมูลการกรองปัจจุบัน
            const search = document.getElementById('search').value;
            const filterType = document.getElementById('filter_type').value;
            const filterBrand = document.getElementById('filter_brand').value;
            const filterDepartment = document.getElementById('filter_department').value;
            const filterStatus = document.getElementById('filter_status').value;
            const filterOs = document.getElementById('filter_os').value;

            // สร้าง URL พร้อม parameters
            let url = 'export_excel.php?';
            const params = [];

            if (search) params.push('search=' + encodeURIComponent(search));
            if (filterType) params.push('filter_type=' + encodeURIComponent(filterType));
            if (filterBrand) params.push('filter_brand=' + encodeURIComponent(filterBrand));
            if (filterDepartment) params.push('filter_department=' + encodeURIComponent(filterDepartment));
            if (filterStatus) params.push('filter_status=' + encodeURIComponent(filterStatus));
            if (filterOs) params.push('filter_os=' + encodeURIComponent(filterOs));

            url += params.join('&');

            // เปิดหน้าต่างใหม่เพื่อดาวน์โหลด
            window.open(url, '_blank');
        }



        // Check Font Awesome loading
        function checkFontAwesome() {
            const closeBtn = document.querySelector('.close-btn');
            if (closeBtn) {
                // Check if Font Awesome is loaded
                const testElement = document.createElement('i');
                testElement.className = 'fas fa-times';
                testElement.style.position = 'absolute';
                testElement.style.left = '-9999px';
                document.body.appendChild(testElement);

                const computedStyle = window.getComputedStyle(testElement, '::before');
                const content = computedStyle.getPropertyValue('content');

                document.body.removeChild(testElement);

                // If Font Awesome loaded, content should not be 'none' or empty
                if (content && content !== 'none' && content !== '""') {
                    closeBtn.classList.add('fa-loaded');
                } else {
                    // Show fallback
                    const span = closeBtn.querySelector('span');
                    if (span) {
                        span.style.display = 'inline';
                    }
                    const icon = closeBtn.querySelector('i');
                    if (icon) {
                        icon.style.display = 'none';
                    }
                }
            }
        }

        // Resizable columns functionality
        function initResizableColumns() {
            const table = document.querySelector('.table');
            if (!table) return;

            const headers = table.querySelectorAll('th');
            let isResizing = false;
            let currentColumn = null;
            let startX = 0;
            let startWidth = 0;

            headers.forEach((header, index) => {
                // Skip last column (actions)
                if (index === headers.length - 1) return;

                const resizeHandle = document.createElement('div');
                resizeHandle.className = 'resize-handle';
                resizeHandle.style.cssText = `
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 5px;
                    height: 100%;
                    cursor: col-resize;
                    background: transparent;
                    z-index: 20;
                `;

                header.style.position = 'relative';
                header.appendChild(resizeHandle);

                resizeHandle.addEventListener('mousedown', function(e) {
                    isResizing = true;
                    currentColumn = header;
                    startX = e.pageX;
                    startWidth = parseInt(window.getComputedStyle(header).width, 10);

                    document.addEventListener('mousemove', handleMouseMove);
                    document.addEventListener('mouseup', handleMouseUp);

                    e.preventDefault();
                });

                resizeHandle.addEventListener('mouseenter', function() {
                    this.style.background = 'rgba(0, 123, 255, 0.3)';
                });

                resizeHandle.addEventListener('mouseleave', function() {
                    if (!isResizing) {
                        this.style.background = 'transparent';
                    }
                });
            });

            function handleMouseMove(e) {
                if (!isResizing || !currentColumn) return;

                const diff = e.pageX - startX;
                const newWidth = Math.max(50, startWidth + diff); // Minimum width 50px

                currentColumn.style.width = newWidth + 'px';

                // Update corresponding td elements
                const columnIndex = Array.from(currentColumn.parentNode.children).indexOf(currentColumn);
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    const cell = row.children[columnIndex];
                    if (cell) {
                        cell.style.width = newWidth + 'px';
                    }
                });
            }

            function handleMouseUp() {
                if (isResizing) {
                    isResizing = false;
                    currentColumn = null;

                    // Remove highlight from resize handle
                    const activeHandle = document.querySelector('.resize-handle[style*="rgba(0, 123, 255, 0.3)"]');
                    if (activeHandle) {
                        activeHandle.style.background = 'transparent';
                    }
                }

                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
            }
        }

        // Initialize resizable columns when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initResizableColumns();

            // Check Font Awesome after a short delay
            setTimeout(checkFontAwesome, 100);
        });
    </script>

    <!-- Add Asset Modal -->
    <div id="addAssetModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="header-text">
                    <h2>➕ เพิ่ม Asset ใหม่</h2>
                </div>
                <div class="header-actions">
                    <button type="submit" form="addAssetForm" class="btn btn-success" title="บันทึก">
                        บันทึก
                    </button>
                    <span class="close" onclick="closeAddAssetModal()" title="ปิด">&times;</span>
                </div>
            </div>
            <div class="modal-body">
                <form id="addAssetForm" method="POST" action="add_asset.php">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="modal_type">Type <span class="required">*</span></label>
                            <select id="modal_type" name="type" class="form-control" required>
                                <option value="">เลือก Type</option>
                                <option value="Desktop">Desktop</option>
                                <option value="Laptop">Laptop</option>
                                <option value="Monitor">Monitor</option>
                                <option value="All-in-one">All-in-one</option>
                                <option value="Multifunction Laser Printer">Multifunction Laser Printer</option>
                                <option value="Barcode Printer">Barcode Printer</option>
                                <option value="Barcode Scanner">Barcode Scanner</option>
                                <option value="Tablet">Tablet</option>
                                <option value="UPS">UPS</option>
                                <option value="Queue">Queue</option>
                                <option value="IP Phone">IP Phone</option>
                                <option value="Teleconference">Teleconference</option>
                                <option value="Switch">Switch</option>
                                <option value="Access Point">Access Point</option>
                                <option value="Peripheral">Peripheral</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="modal_brand">Brand</label>
                            <select id="modal_brand" name="brand" class="form-control">
                                <option value="">เลือก Brand</option>
                                <option value="-">-</option>
                                <option value="Dell">Dell</option>
                                <option value="Lenovo">Lenovo</option>
                                <option value="Microsoft">Microsoft</option>
                                <option value="Apple">Apple</option>
                                <option value="Zebra">Zebra</option>
                                <option value="HP">HP</option>
                                <option value="Philips">Philips</option>
                                <option value="Acer">Acer</option>
                                <option value="LG">LG</option>
                                <option value="Cisco">Cisco</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="modal_model">Model</label>
                            <input type="text" id="modal_model" name="model" class="form-control" placeholder="ระบุรุ่น">
                        </div>

                        <div class="form-group">
                            <label for="modal_tag">Tag</label>
                            <input type="text" id="modal_tag" name="tag" class="form-control" placeholder="ระบุ Tag">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="modal_department">Department</label>
                            <select id="modal_department" name="department" class="form-control">
                                <option value="">เลือก Department</option>
                                <option value="Accounting">Accounting</option>
                                <option value="Admission">Admission</option>
                                <option value="Anesthetic">Anesthetic</option>
                                <option value="BME">BME</option>
                                <option value="Cath Lab">Cath Lab</option>
                                <option value="Cardiac Care Unit">Cardiac Care Unit</option>
                                <option value="CEO">CEO</option>
                                <option value="Chivawattana">Chivawattana</option>
                                <option value="Cardiology & Neurological Clinic">Cardiology & Neurological Clinic</option>
                                <option value="Collection">Collection</option>
                                <option value="Contact Center">Contact Center</option>
                                <option value="Cashier IPD">Cashier IPD</option>
                                <option value="Cashier OPD">Cashier OPD</option>
                                <option value="Customer Service">Customer Service</option>
                                <option value="Dental">Dental</option>
                                <option value="Doctor Room">Doctor Room</option>
                                <option value="EMT">EMT</option>
                                <option value="ENT">ENT</option>
                                <option value="Endoscopy Center">Endoscopy Center</option>
                                <option value="Eye Center">Eye Center</option>
                                <option value="Emergency Room">Emergency Room</option>
                                <option value="Financial">Financial</option>
                                <option value="General Support">General Support</option>
                                <option value="Hemodialysis">Hemodialysis</option>
                                <option value="Hemodialysis VIP">Hemodialysis VIP</option>
                                <option value="Health Promotion Center">Health Promotion Center</option>
                                <option value="Human Resources">Human Resources</option>
                                <option value="Intensive Care Unit">Intensive Care Unit</option>
                                <option value="IT">IT</option>
                                <option value="Laboratory">Laboratory</option>
                                <option value="Labour Room">Labour Room</option>
                                <option value="Linen Service">Linen Service</option>
                                <option value="MAO">MAO</option>
                                <option value="Medicine Unit">Medicine Unit</option>
                                <option value="Marketing">Marketing</option>
                                <option value="Medical Record">Medical Record</option>
                                <option value="N-Health">N-Health</option>
                                <option value="Nursery">Nursery</option>
                                <option value="NSO">NSO</option>
                                <option value="Obstetrics & Gynecology">Obstetrics & Gynecology</option>
                                <option value="Operating Room">Operating Room</option>
                                <option value="Orthopedic">Orthopedic</option>
                                <option value="Pharmacy OPD">Pharmacy OPD</option>
                                <option value="Pharmacy OPD (Floor 2)">Pharmacy OPD (Floor 2)</option>
                                <option value="Pharmacy IPD">Pharmacy IPD</option>
                                <option value="Pediatrics">Pediatrics</option>
                                <option value="PUR">PUR</option>
                                <option value="Referral">Referral</option>
                                <option value="Registration">Registration</option>
                                <option value="Repair & Maintenance">Repair & Maintenance</option>
                                <option value="Rehabilitation">Rehabilitation</option>
                                <option value="Secretary">Secretary</option>
                                <option value="Sterlie">Sterlie</option>
                                <option value="Supervisor">Supervisor</option>
                                <option value="Supply">Supply</option>
                                <option value="Surgery Unit">Surgery Unit</option>
                                <option value="Quality Center">Quality Center</option>
                                <option value="Utilization Management">Utilization Management</option>
                                <option value="W5A">W5A</option>
                                <option value="W5B">W5B</option>
                                <option value="W6A">W6A</option>
                                <option value="W6B">W6B</option>
                                <option value="W7A">W7A</option>
                                <option value="W7B">W7B</option>
                                <option value="W8A">W8A</option>
                                <option value="W8B">W8B</option>
                                <option value="W9A">W9A</option>
                                <option value="W9B">W9B</option>
                                <option value="Wound Care Unit">Wound Care Unit</option>
                                <option value="Wellness Center">Wellness Center</option>
                                <option value="X-ray">X-ray</option>
                                <option value="X-ray NP">X-ray NP</option>
                                <option value="เวรเปล">เวรเปล</option>
                                <option value="Food House">Food House</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="modal_status">Status</label>
                            <select id="modal_status" name="status" class="form-control">
                                <option value="ใช้งาน" selected>ใช้งาน</option>
                                <option value="ชำรุด">ชำรุด</option>
                                <option value="สำรอง">สำรอง</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="modal_hostname">Hostname</label>
                            <input type="text" id="modal_hostname" name="hostname" class="form-control" placeholder="ระบุ Hostname">
                        </div>

                        <div class="form-group">
                            <label for="modal_operating_system">Operating System</label>
                            <select id="modal_operating_system" name="operating_system" class="form-control">
                                <option value="">เลือก Operating System</option>
                                <option value="-">-</option>
                                <option value="Windows 7">Windows 7</option>
                                <option value="Windows 10">Windows 10</option>
                                <option value="Windows 11">Windows 11</option>
                                <option value="MacOS">MacOS</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="modal_serial_number">Serial Number</label>
                            <input type="text" id="modal_serial_number" name="serial_number" class="form-control" placeholder="ระบุ Serial Number">
                        </div>

                        <div class="form-group">
                            <label for="modal_asset_id">Asset ID</label>
                            <input type="text" id="modal_asset_id" name="asset_id" class="form-control" placeholder="ระบุ Asset ID">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="modal_warranty_expire">วันหมดประกัน</label>
                            <input type="date" id="modal_warranty_expire" name="warranty_expire" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="modal_set_name">Set</label>
                            <input type="text" id="modal_set_name" name="set_name" class="form-control" placeholder="ระบุ Set">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="modal_description">รายละเอียด</label>
                        <textarea id="modal_description" name="description" class="form-control" rows="3" placeholder="ระบุรายละเอียดเพิ่มเติม"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="modal_created_by">👤 ผู้เพิ่ม</label>
                        <input type="text" id="modal_created_by" name="created_by" class="form-control" readonly value="<?= htmlspecialchars(getCurrentUserFullName()) ?>" style="background-color: #f8f9fa; cursor: not-allowed;">
                    </div>


                </form>
            </div>
        </div>
    </div>

    <script>
        // Modal functions

        // Add Asset Modal functions
        function openAddAssetModal() {
            document.getElementById('addAssetModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeAddAssetModal() {
            document.getElementById('addAssetModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            document.getElementById('addAssetForm').reset();
            // Restore the created_by field value after reset
            document.getElementById('modal_created_by').value = '<?= htmlspecialchars(getCurrentUserFullName()) ?>';
        }

        // Handle Add Asset Form Submit
        document.getElementById('addAssetForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            // หาปุ่ม submit ใน header หรือในฟอร์ม
            const submitBtn = document.querySelector('button[form="addAssetForm"]') || this.querySelector('button[type="submit"]');
            const originalText = submitBtn ? submitBtn.innerHTML : 'บันทึก';

            // แสดง loading
            if (submitBtn) {
                submitBtn.innerHTML = '⏳ กำลังบันทึก...';
                submitBtn.disabled = true;
            }

            fetch('add_asset.php', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ ' + data.message + ' (ID: ' + data.asset_id + ')');
                    closeAddAssetModal();
                    location.reload(); // รีโหลดหน้าเพื่อแสดงข้อมูลใหม่
                } else {
                    alert('❌ ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('❌ เกิดข้อผิดพลาดในการเชื่อมต่อ');
            })
            .finally(() => {
                // คืนค่าปุ่ม
                if (submitBtn) {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            });
        });

        function openProfileModal() {
            document.getElementById('profileModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
            loadProfileData();
        }

        function closeProfileModal() {
            document.getElementById('profileModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function loadProfileData() {
            fetch('get_profile_data.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // อัพเดทข้อมูลในส่วน header
                        document.getElementById('profile_username_display').textContent = data.user.username;
                        document.getElementById('profile_role_text').textContent = data.user.role || 'User';

                        // อัพเดทข้อมูลในตาราง
                        document.getElementById('profile_username').textContent = data.user.username;
                        document.getElementById('profile_full_name').textContent = data.user.full_name || '-';
                        document.getElementById('profile_email').textContent = data.user.email || '-';

                        // อัพเดท role badge
                        const roleBadge = document.getElementById('profile_role');
                        roleBadge.textContent = data.user.role || '-';
                        roleBadge.className = 'role-badge' + (data.user.role === 'Admin' ? ' admin' : '');

                        // อัพเดท status badge
                        const statusBadge = document.getElementById('profile_status');
                        statusBadge.textContent = data.user.status || '-';
                        statusBadge.className = 'status-badge' + (data.user.status === 'Inactive' ? ' inactive' : '');

                        document.getElementById('profile_last_login').textContent = data.user.last_login || '-';
                        document.getElementById('profile_created_date').textContent = data.user.created_date || '-';

                        // Set form values for editing
                        document.getElementById('edit_username').value = data.user.username;
                        document.getElementById('edit_full_name').value = data.user.full_name || '';
                        document.getElementById('edit_email').value = data.user.email || '';

                        // อัพเดทไอคอน role ใน header
                        const roleDisplay = document.getElementById('profile_role_display');
                        const roleIcon = data.user.role === 'Admin' ? '👑' : '👤';
                        roleDisplay.querySelector('span:first-child').textContent = roleIcon;
                    }
                })
                .catch(error => {
                    console.error('Error loading profile:', error);
                    alert('เกิดข้อผิดพลาดในการโหลดข้อมูลโปรไฟล์');
                });
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const addModal = document.getElementById('addAssetModal');
            const profileModal = document.getElementById('profileModal');

            if (event.target == addModal) {
                closeAddAssetModal();
            }
            if (event.target == profileModal) {
                closeProfileModal();
            }
        }

        function toggleEditMode() {
            const viewMode = document.getElementById('profileViewMode');
            const editMode = document.getElementById('profileEditMode');

            if (viewMode.style.display === 'none') {
                viewMode.style.display = 'block';
                editMode.style.display = 'none';
            } else {
                viewMode.style.display = 'none';
                editMode.style.display = 'block';
            }
        }

        function saveProfile() {
            const formData = new FormData(document.getElementById('editProfileForm'));

            fetch('update_profile.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('อัพเดทโปรไฟล์สำเร็จ');
                    loadProfileData();
                    toggleEditMode();
                } else {
                    alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถอัพเดทได้'));
                }
            })
            .catch(error => {
                console.error('Error updating profile:', error);
                alert('เกิดข้อผิดพลาดในการอัพเดทโปรไฟล์');
            });
        }

        // Asset Modal functions
        function viewAssetDetails(assetId) {
            console.log('viewAssetDetails called with ID:', assetId);

            // เปิด modal
            document.getElementById('viewAssetModal').style.display = 'block';
            document.body.style.overflow = 'hidden';

            // แสดง loading
            const modalBody = document.getElementById('viewAssetModal').querySelector('.modal-body');
            modalBody.innerHTML = '<div style="text-align: center; padding: 40px;"><div style="font-size: 1.2rem;">🔄 กำลังโหลดข้อมูล...</div></div>';

            // โหลดข้อมูล Asset
            fetch('get_asset_data.php?id=' + assetId)
                .then(function(response) {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error('HTTP error! status: ' + response.status);
                    }
                    return response.json();
                })
                .then(function(data) {
                    console.log('Data received:', data);
                    if (data.success) {
                        // กู้คืน modal structure ก่อน
                        restoreViewModalStructure();
                        displayAssetDetails(data.asset);
                    } else {
                        alert('เกิดข้อผิดพลาดในการโหลดข้อมูล: ' + data.message);
                        closeViewAssetModal();
                    }
                })
                .catch(function(error) {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการเชื่อมต่อกับเซิร์ฟเวอร์: ' + error.message);
                    closeViewAssetModal();
                });
        }

        function closeViewAssetModal() {
            document.getElementById('viewAssetModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function restoreViewModalStructure() {
            const modalBody = document.getElementById('viewAssetModal').querySelector('.modal-body');
            modalBody.innerHTML = `
                <div class="asset-view-container">
                    <div class="asset-form-grid">
                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-desktop"></i> ประเภท *</label>
                                <div class="form-control-static" id="view_type">-</div>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-tag"></i> ยี่ห้อ *</label>
                                <div class="form-control-static" id="view_brand">-</div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-cube"></i> รุ่น</label>
                                <div class="form-control-static" id="view_model">-</div>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-tag"></i> Tag</label>
                                <div class="form-control-static" id="view_tag">-</div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-building"></i> แผนก</label>
                                <div class="form-control-static" id="view_department">-</div>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-circle"></i> สถานะ *</label>
                                <div class="form-control-static">
                                    <center><span class="status-badge" id="view_status">-</span></center>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-server"></i> Hostname</label>
                                <div class="form-control-static" id="view_hostname">-</div>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-compact-disc"></i> Operating System</label>
                                <div class="form-control-static" id="view_operating_system">-</div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-barcode"></i> Serial Number</label>
                                <div class="form-control-static" id="view_serial_number">-</div>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-id-card"></i> Asset ID</label>
                                <div class="form-control-static" id="view_asset_id_display">-</div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-calendar-alt"></i> Warranty Expire</label>
                                <div class="form-control-static" id="view_warranty_expire">-</div>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-layer-group"></i> ชุด</label>
                                <div class="form-control-static" id="view_set_name">-</div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group full-width">
                                <label><i class="fas fa-file-text"></i> คำอธิบาย</label>
                                <div class="form-control-static" id="view_description">-</div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-calendar-plus"></i> วันที่สร้างข้อมูล</label>
                                <div class="form-control-static" id="view_created_date">-</div>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-user-plus"></i> ผู้สร้างข้อมูล</label>
                                <div class="form-control-static" id="view_created_by">-</div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label><i class="fas fa-calendar-edit"></i> วันที่แก้ไขล่าสุด</label>
                                <div class="form-control-static" id="view_updated_date">-</div>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-user-edit"></i> ผู้แก้ไขล่าสุด</label>
                                <div class="form-control-static" id="view_updated_by">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function displayAssetDetails(asset) {
            console.log('displayAssetDetails called with:', asset);

            // อัพเดท header info
            const assetTitleInfo = document.getElementById('asset-title-info');
            if (assetTitleInfo) {
                assetTitleInfo.textContent = `${asset.type || 'Asset'} - ${asset.brand || ''} ${asset.model || ''}`.trim();
            }

            // ฟังก์ชันช่วยในการกำหนดค่าอย่างปลอดภัย
            function safeSetText(elementId, value) {
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = value || '-';
                } else {
                    console.warn(`Element with ID '${elementId}' not found`);
                }
            }

            // ใช้ฟังก์ชันช่วยเพื่อกำหนดค่าให้แต่ละ element
            safeSetText('view_asset_id', asset.id);
            safeSetText('view_type', asset.type);
            safeSetText('view_brand', asset.brand);
            safeSetText('view_model', asset.model);
            safeSetText('view_tag', asset.tag);
            safeSetText('view_department', asset.department);
            safeSetText('view_hostname', asset.hostname);
            safeSetText('view_operating_system', asset.operating_system);
            safeSetText('view_serial_number', asset.serial_number);
            safeSetText('view_asset_id_display', asset.asset_id);
            safeSetText('view_warranty_expire', asset.warranty_expire);
            safeSetText('view_description', asset.description || 'ไม่มีคำอธิบาย');
            safeSetText('view_set_name', asset.set_name);
            safeSetText('view_created_date', asset.created_date);
            safeSetText('view_created_by', asset.created_by);
            safeSetText('view_updated_date', asset.updated_date);
            safeSetText('view_updated_by', asset.updated_by);

            // อัพเดท status badge
            const statusElement = document.getElementById('view_status');
            if (statusElement) {
                statusElement.textContent = asset.status || '-';
                statusElement.className = 'status-badge';

                // เพิ่ม class ตามสถานะ
                if (asset.status === 'ใช้งาน') {
                    statusElement.classList.add('active');
                } else if (asset.status === 'ชำรุด') {
                    statusElement.classList.add('damaged');
                } else if (asset.status === 'สำรอง') {
                    statusElement.classList.add('spare');
                }
            } else {
                console.warn('Status element not found');
            }

            // เก็บ asset ID สำหรับใช้ในการดู log ทั้งหมด
            window.currentViewAssetId = asset.id;

            // อัพเดทปุ่มดู log ทั้งหมด
            const viewFullLogsBtn = document.getElementById('view-full-logs-btn');
            if (viewFullLogsBtn) {
                viewFullLogsBtn.onclick = function() {
                    viewAssetLogs(asset.id);
                };
            }

            // โหลด Log ล่าสุด
            loadRecentLogs(asset.id);

            console.log('Asset details populated successfully');
        }

        function saveAsset() {
            // ตัวอย่าง function สำหรับบันทึกข้อมูล
            // สามารถเปิด edit modal หรือทำการบันทึกข้อมูลได้
            if (window.currentViewAssetId) {
                openEditAssetModal(window.currentViewAssetId);
                closeViewAssetModal();
            } else {
                alert('ไม่พบข้อมูล Asset ID');
            }
        }

        function saveEditAsset() {
            // ตัวอย่าง function สำหรับบันทึกการแก้ไข asset
            const form = document.getElementById('editAssetForm');
            if (form) {
                // ตรวจสอบข้อมูลในฟอร์ม
                const formData = new FormData(form);

                // ส่งข้อมูลไปยังเซิร์ฟเวอร์
                fetch('update_asset.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('บันทึกข้อมูลสำเร็จ');
                        closeEditAssetModal();
                        // รีเฟรชตาราง
                        location.reload();
                    } else {
                        alert('เกิดข้อผิดพลาด: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการเชื่อมต่อ');
                });
            }
        }

        function updateStatusIndicator(status) {
            const statusDot = document.getElementById('status-dot');
            const statusText = document.getElementById('status-text');

            if (statusDot && statusText) {
                // ลบ class เก่า
                statusDot.className = 'status-dot';

                // เพิ่ม class ใหม่ตามสถานะ
                switch(status) {
                    case 'ใช้งาน':
                        statusDot.classList.add('active');
                        statusText.textContent = 'ใช้งาน';
                        statusText.style.color = '#28a745';
                        break;
                    case 'ชำรุด':
                        statusDot.classList.add('damaged');
                        statusText.textContent = 'ชำรุด';
                        statusText.style.color = '#dc3545';
                        break;
                    case 'สำรอง':
                        statusDot.classList.add('spare');
                        statusText.textContent = 'สำรอง';
                        statusText.style.color = '#ffc107';
                        break;
                    default:
                        statusText.textContent = status || '-';
                        statusText.style.color = '#6c757d';
                }
            }
        }

        function loadRecentLogs(assetId) {
            console.log('Loading recent logs for asset ID:', assetId);

            const logsPreview = document.getElementById('asset-logs-preview');
            if (!logsPreview) {
                console.error('Logs preview container not found');
                return;
            }

            // แสดง loading
            logsPreview.innerHTML = `
                <div class="loading-logs">
                    <div class="spinner-small"></div>
                    <span>กำลังโหลด Log...</span>
                </div>
            `;

            // โหลดข้อมูล logs ล่าสุด (5 รายการ)
            fetch(`get_asset_logs.php?asset_id=${assetId}&limit=5`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.logs && data.logs.length > 0) {
                        displayRecentLogs(data.logs, assetId);
                    } else {
                        logsPreview.innerHTML = `
                            <div class="no-logs">
                                <div class="no-logs-icon">📝</div>
                                <div class="no-logs-text">ยังไม่มีประวัติการเปลี่ยนแปลง</div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error loading recent logs:', error);
                    logsPreview.innerHTML = `
                        <div class="error-logs">
                            <div class="error-icon">⚠️</div>
                            <div class="error-text">เกิดข้อผิดพลาดในการโหลด Log</div>
                        </div>
                    `;
                });
        }

        function displayRecentLogs(logs, assetId) {
            const logsPreview = document.getElementById('asset-logs-preview');
            if (!logsPreview) return;

            let logsHtml = '<div class="recent-logs-list">';

            logs.forEach(log => {
                const actionIcon = getLogActionIcon(log.action);
                const actionClass = getLogActionClass(log.action);
                const timeAgo = getTimeAgo(log.created_at);

                logsHtml += `
                    <div class="recent-log-item ${actionClass}">
                        <div class="log-icon">${actionIcon}</div>
                        <div class="log-content">
                            <div class="log-action">${log.action}</div>
                            <div class="log-user">โดย ${log.user_name || 'ไม่ระบุ'}</div>
                            <div class="log-time">${timeAgo}</div>
                        </div>
                    </div>
                `;
            });

            logsHtml += '</div>';

            // เพิ่มลิงก์ดู log ทั้งหมด
            logsHtml += `
                <div class="view-all-logs">
                    <button onclick="viewAssetLogs(${assetId})" class="btn btn-outline btn-sm">
                        📋 ดู Log ทั้งหมด (${logs.length > 0 ? 'มีข้อมูลเพิ่มเติม' : ''})
                    </button>
                </div>
            `;

            logsPreview.innerHTML = logsHtml;
        }

        function getLogActionIcon(action) {
            switch(action) {
                case 'CREATE': return '➕';
                case 'Update': return '✏️';
                case 'UPDATE': return '✏️';
                case 'Delete': return '🗑️';
                case 'DELETE': return '🗑️';
                case 'View': return '👁️';
                case 'VIEW': return '👁️';
                default: return '📝';
            }
        }

        function getLogActionClass(action) {
            switch(action) {
                case 'CREATE': return 'log-create';
                case 'Update': return 'log-update';
                case 'UPDATE': return 'log-update';
                case 'Delete': return 'log-delete';
                case 'DELETE': return 'log-delete';
                case 'View': return 'log-view';
                case 'VIEW': return 'log-view';
                default: return 'log-other';
            }
        }

        function getTimeAgo(dateString) {
            const now = new Date();
            const logDate = new Date(dateString);
            const diffInSeconds = Math.floor((now - logDate) / 1000);

            if (diffInSeconds < 60) {
                return 'เมื่อสักครู่';
            } else if (diffInSeconds < 3600) {
                const minutes = Math.floor(diffInSeconds / 60);
                return `${minutes} นาทีที่แล้ว`;
            } else if (diffInSeconds < 86400) {
                const hours = Math.floor(diffInSeconds / 3600);
                return `${hours} ชั่วโมงที่แล้ว`;
            } else {
                const days = Math.floor(diffInSeconds / 86400);
                return `${days} วันที่แล้ว`;
            }
        }

        function openEditAssetModal(assetId) {
            document.getElementById('editAssetModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
            loadAssetData(assetId, 'edit');
        }

        function closeEditAssetModal() {
            document.getElementById('editAssetModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function loadAssetData(assetId, mode) {
            // แสดง loading indicator
            if (mode === 'edit') {
                document.getElementById('editAssetModal').querySelector('.modal-body').innerHTML = '<div style="text-align: center; padding: 40px;"><div style="font-size: 1.2rem;">🔄 กำลังโหลดข้อมูล...</div></div>';
            }

            fetch(`get_asset_data.php?id=${assetId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Asset data received:', data);

                    if (data.success) {
                        if (mode === 'edit') {
                            // กู้คืน HTML structure สำหรับ edit modal
                            restoreEditModalStructure();
                            console.log('About to populate edit modal with:', data.asset);
                            populateEditModal(data.asset);
                        }
                    } else {
                        console.error('API returned error:', data);
                        alert('เกิดข้อผิดพลาดในการโหลดข้อมูล Asset: ' + (data.message || 'ไม่ทราบสาเหตุ'));
                        if (mode === 'edit') {
                            closeEditAssetModal();
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading asset data:', error);
                    alert('เกิดข้อผิดพลาดในการเชื่อมต่อกับเซิร์ฟเวอร์');
                    if (mode === 'edit') {
                        closeEditAssetModal();
                    }
                });
        }

        function restoreEditModalStructure() {
            const editModalBody = document.getElementById('editAssetModal').querySelector('.modal-body');
            editModalBody.innerHTML = `
                <form id="editAssetForm">
                    <input type="hidden" id="edit_asset_id_hidden" name="id">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_type">Type <span class="required">*</span></label>
                            <select id="edit_type" name="type" class="form-control" required>
                                <option value="">เลือก Type</option>
                                <option value="Desktop">Desktop</option>
                                <option value="Laptop">Laptop</option>
                                <option value="Monitor">Monitor</option>
                                <option value="All-in-one">All-in-one</option>
                                <option value="Multifunction Laser Printer">Multifunction Laser Printer</option>
                                <option value="Barcode Printer">Barcode Printer</option>
                                <option value="Barcode Scanner">Barcode Scanner</option>
                                <option value="Tablet">Tablet</option>
                                <option value="UPS">UPS</option>
                                <option value="Queue">Queue</option>
                                <option value="IP Phone">IP Phone</option>
                                <option value="Teleconference">Teleconference</option>
                                <option value="Switch">Switch</option>
                                <option value="Access Point">Access Point</option>
                                <option value="Peripheral">Peripheral</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_brand">Brand</label>
                            <select id="edit_brand" name="brand" class="form-control">
                                <option value="">เลือก Brand</option>
                                <option value="-">-</option>
                                <option value="Dell">Dell</option>
                                <option value="Lenovo">Lenovo</option>
                                <option value="Microsoft">Microsoft</option>
                                <option value="Apple">Apple</option>
                                <option value="Zebra">Zebra</option>
                                <option value="HP">HP</option>
                                <option value="Philips">Philips</option>
                                <option value="Acer">Acer</option>
                                <option value="LG">LG</option>
                                <option value="Cisco">Cisco</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_model">Model</label>
                            <input type="text" id="edit_model" name="model" class="form-control" placeholder="กรอก Model">
                        </div>

                        <div class="form-group">
                            <label for="edit_tag">Tag</label>
                            <input type="text" id="edit_tag" name="tag" class="form-control" placeholder="กรอก Tag">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_department">Department</label>
                            <select id="edit_department" name="department" class="form-control">
                                <option value="">เลือก Department</option>
                                <option value="Accounting">Accounting</option>
                                <option value="Admission">Admission</option>
                                <option value="Anesthetic">Anesthetic</option>
                                <option value="BME">BME</option>
                                <option value="Cath Lab">Cath Lab</option>
                                <option value="Cardiac Care Unit">Cardiac Care Unit</option>
                                <option value="CEO">CEO</option>
                                <option value="Chivawattana">Chivawattana</option>
                                <option value="Cardiology & Neurological Clinic">Cardiology & Neurological Clinic</option>
                                <option value="Collection">Collection</option>
                                <option value="Contact Center">Contact Center</option>
                                <option value="Cashier IPD">Cashier IPD</option>
                                <option value="Cashier OPD">Cashier OPD</option>
                                <option value="Customer Service">Customer Service</option>
                                <option value="Dental">Dental</option>
                                <option value="Doctor Room">Doctor Room</option>
                                <option value="EMT">EMT</option>
                                <option value="ENT">ENT</option>
                                <option value="Endoscopy Center">Endoscopy Center</option>
                                <option value="Eye Center">Eye Center</option>
                                <option value="Emergency Room">Emergency Room</option>
                                <option value="Financial">Financial</option>
                                <option value="General Support">General Support</option>
                                <option value="Hemodialysis">Hemodialysis</option>
                                <option value="Hemodialysis VIP">Hemodialysis VIP</option>
                                <option value="Health Promotion Center">Health Promotion Center</option>
                                <option value="Human Resources">Human Resources</option>
                                <option value="Intensive Care Unit">Intensive Care Unit</option>
                                <option value="IT">IT</option>
                                <option value="Laboratory">Laboratory</option>
                                <option value="Labour Room">Labour Room</option>
                                <option value="Linen Service">Linen Service</option>
                                <option value="MAO">MAO</option>
                                <option value="Medicine Unit">Medicine Unit</option>
                                <option value="Marketing">Marketing</option>
                                <option value="Medical Record">Medical Record</option>
                                <option value="N-Health">N-Health</option>
                                <option value="Nursery">Nursery</option>
                                <option value="NSO">NSO</option>
                                <option value="Obstetrics & Gynecology">Obstetrics & Gynecology</option>
                                <option value="Operating Room">Operating Room</option>
                                <option value="Orthopedic">Orthopedic</option>
                                <option value="Pharmacy OPD">Pharmacy OPD</option>
                                <option value="Pharmacy OPD (Floor 2)">Pharmacy OPD (Floor 2)</option>
                                <option value="Pharmacy IPD">Pharmacy IPD</option>
                                <option value="Pediatrics">Pediatrics</option>
                                <option value="PUR">PUR</option>
                                <option value="Referral">Referral</option>
                                <option value="Registration">Registration</option>
                                <option value="Repair & Maintenance">Repair & Maintenance</option>
                                <option value="Rehabilitation">Rehabilitation</option>
                                <option value="Secretary">Secretary</option>
                                <option value="Sterlie">Sterlie</option>
                                <option value="Supervisor">Supervisor</option>
                                <option value="Supply">Supply</option>
                                <option value="Surgery Unit">Surgery Unit</option>
                                <option value="Quality Center">Quality Center</option>
                                <option value="Utilization Management">Utilization Management</option>
                                <option value="W5A">W5A</option>
                                <option value="W5B">W5B</option>
                                <option value="W6A">W6A</option>
                                <option value="W6B">W6B</option>
                                <option value="W7A">W7A</option>
                                <option value="W7B">W7B</option>
                                <option value="W8A">W8A</option>
                                <option value="W8B">W8B</option>
                                <option value="W9A">W9A</option>
                                <option value="W9B">W9B</option>
                                <option value="Wound Care Unit">Wound Care Unit</option>
                                <option value="Wellness Center">Wellness Center</option>
                                <option value="X-ray">X-ray</option>
                                <option value="X-ray NP">X-ray NP</option>
                                <option value="เวรเปล">เวรเปล</option>
                                <option value="Food House">Food House</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_status">Status <span class="required">*</span></label>
                            <select id="edit_status" name="status" class="form-control" required>
                                <option value="ใช้งาน">ใช้งาน</option>
                                <option value="ชำรุด">ชำรุด</option>
                                <option value="สำรอง">สำรอง</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_hostname">Hostname</label>
                            <input type="text" id="edit_hostname" name="hostname" class="form-control" placeholder="กรอก Hostname">
                        </div>

                        <div class="form-group">
                            <label for="edit_operating_system">Operating System</label>
                            <select id="edit_operating_system" name="operating_system" class="form-control">
                                <option value="-">-</option>
                                <option value="Windows 7">Windows 7</option>
                                <option value="Windows 10">Windows 10</option>
                                <option value="Windows 11">Windows 11</option>
                                <option value="MacOS">MacOS</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_serial_number">Serial Number</label>
                            <input type="text" id="edit_serial_number" name="serial_number" class="form-control" placeholder="กรอก Serial Number">
                        </div>

                        <div class="form-group">
                            <label for="edit_asset_id">Asset ID</label>
                            <input type="text" id="edit_asset_id" name="asset_id" class="form-control" placeholder="กรอก Asset ID">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_warranty_expire">Warranty Expire</label>
                            <input type="date" id="edit_warranty_expire" name="warranty_expire" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="edit_set_name">Set</label>
                            <input type="text" id="edit_set_name" name="set_name" class="form-control" placeholder="กรอกชื่อ Set">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_description">Description</label>
                        <textarea id="edit_description" name="description" class="form-control" rows="3" placeholder="กรอก Description"></textarea>
                    </div>
                </form>
            `;
        }



        function populateEditModal(asset) {
            document.getElementById('edit_asset_id_hidden').value = asset.id || '';
            document.getElementById('edit_type').value = asset.type || '';
            document.getElementById('edit_brand').value = asset.brand || '';
            document.getElementById('edit_model').value = asset.model || '';
            document.getElementById('edit_tag').value = asset.tag || '';
            document.getElementById('edit_department').value = asset.department || '';
            document.getElementById('edit_status').value = asset.status || '';
            document.getElementById('edit_hostname').value = asset.hostname || '';
            document.getElementById('edit_operating_system').value = asset.operating_system || '';
            document.getElementById('edit_serial_number').value = asset.serial_number || '';
            document.getElementById('edit_asset_id').value = asset.asset_id || '';
            document.getElementById('edit_warranty_expire').value = asset.warranty_expire || '';
            document.getElementById('edit_description').value = asset.description || '';
            document.getElementById('edit_set_name').value = asset.set_name || '';
        }

        function saveAsset() {
            const formData = new FormData(document.getElementById('editAssetForm'));

            fetch('update_asset.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('อัพเดท Asset สำเร็จ');
                    closeEditAssetModal();
                    location.reload(); // รีโหลดหน้าเพื่อแสดงข้อมูลใหม่
                } else {
                    alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถอัพเดทได้'));
                }
            })
            .catch(error => {
                console.error('Error updating asset:', error);
                alert('เกิดข้อผิดพลาดในการอัพเดท Asset');
            });
        }


    </script>

    <!-- Profile Modal -->
    <div id="profileModal" class="modal">
        <div class="modal-content profile-modal" style="max-width: 650px;">
            <div class="modal-header">
                <h2>
                    <span style="font-size: 1.8rem;">👤</span>
                    โปรไฟล์ผู้ใช้
                </h2>
                <span class="close" onclick="closeProfileModal()">&times;</span>
            </div>

            <!-- View Mode -->
            <div id="profileViewMode" class="modal-body">
                <div class="profile-info">
                    <div class="profile-avatar">
                        <div class="avatar-circle">
                            <span style="font-size: 3.5rem;">👤</span>
                        </div>
                    </div>

                    <div class="profile-user-info">
                        <div class="profile-username" id="profile_username_display">-</div>
                        <div class="profile-role-display" id="profile_role_display">
                            <span>👑</span>
                            <span id="profile_role_text">-</span>
                        </div>
                    </div>

                    <div class="profile-details">
                        <div class="detail-row">
                            <label>🏷️ Username</label>
                            <span id="profile_username">-</span>
                        </div>

                        <div class="detail-row">
                            <label>👤 ชื่อ-นามสกุล</label>
                            <span id="profile_full_name">-</span>
                        </div>

                        <div class="detail-row">
                            <label>📧 Email</label>
                            <span id="profile_email">-</span>
                        </div>

                        <div class="detail-row">
                            <label>🎭 บทบาท</label>
                            <span class="badge-container">
                                <span id="profile_role" class="role-badge">-</span>
                            </span>
                        </div>

                        <div class="detail-row">
                            <label>🟢 สถานะ</label>
                            <span class="badge-container">
                                <span id="profile_status" class="status-badge">-</span>
                            </span>
                        </div>

                        <div class="detail-row">
                            <label>🕐 เข้าสู่ระบบล่าสุด</label>
                            <span id="profile_last_login">-</span>
                        </div>

                        <div class="detail-row">
                            <label>📅 วันที่สร้างบัญชี</label>
                            <span id="profile_created_date">-</span>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="toggleEditMode()">
                        ✏️ แก้ไขโปรไฟล์
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeProfileModal()">ปิด</button>
                </div>
            </div>

            <!-- Edit Mode -->
            <div id="profileEditMode" class="modal-body" style="display: none;">
                <div class="profile-edit-form">
                    <form id="editProfileForm">
                        <div class="form-group">
                            <label for="edit_username">🏷️ Username</label>
                            <input type="text" id="edit_username" name="username" class="form-control" readonly>
                            <small class="form-text">Username ไม่สามารถเปลี่ยนแปลงได้</small>
                        </div>

                        <div class="form-group">
                            <label for="edit_full_name">👤 ชื่อ-นามสกุล</label>
                            <input type="text" id="edit_full_name" name="full_name" class="form-control" required placeholder="กรอกชื่อ-นามสกุลของคุณ">
                        </div>

                        <div class="form-group">
                            <label for="edit_email">📧 Email</label>
                            <input type="email" id="edit_email" name="email" class="form-control" placeholder="กรอกอีเมลของคุณ">
                        </div>

                        <div class="form-group">
                            <label for="edit_current_password">🔐 รหัสผ่านปัจจุบัน</label>
                            <input type="password" id="edit_current_password" name="current_password" class="form-control" placeholder="กรอกรหัสผ่านปัจจุบัน">
                            <small class="form-text">กรอกเฉพาะเมื่อต้องการเปลี่ยนรหัสผ่าน</small>
                        </div>

                        <div class="form-group">
                            <label for="edit_new_password">🔑 รหัสผ่านใหม่</label>
                            <input type="password" id="edit_new_password" name="new_password" class="form-control" placeholder="กรอกรหัสผ่านใหม่">
                        </div>

                        <div class="form-group">
                            <label for="edit_confirm_password">✅ ยืนยันรหัสผ่านใหม่</label>
                            <input type="password" id="edit_confirm_password" name="confirm_password" class="form-control" placeholder="กรอกรหัสผ่านใหม่อีกครั้ง">
                        </div>
                    </form>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-success" onclick="saveProfile()">
                        💾 บันทึก
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="toggleEditMode()">ยกเลิก</button>
                </div>
            </div>
        </div>
    </div>



    <!-- View Asset Modal -->
    <div id="viewAssetModal" class="modal">
        <div class="modal-content view-asset-modal">
            <div class="modal-header">
                <div class="header-text">
                    <h2>รายละเอียด Asset</h2>
                    <p id="asset-title-info">กำลังโหลดข้อมูล...</p>
                </div>
                <span class="close" onclick="closeViewAssetModal()" title="ปิด">&times;</span>
            </div>

            <div class="modal-body">
                <div class="asset-view-container">
                    <!-- Asset Status Card -->
                    <div class="asset-status-card">
                        <div class="status-indicator">
                            <div class="status-dot" id="status-dot"></div>
                            <span class="status-text" id="status-text">-</span>
                        </div>
                        <div class="asset-id-display">
                            <span class="asset-id-label">Asset ID</span>
                            <span class="asset-id-value" id="view_asset_id_display">-</span>
                        </div>
                    </div>

                    <!-- Main Content Grid -->
                    <div class="content-grid">
                        <!-- Basic Information Card -->
                        <div class="info-card">
                            <div class="card-header">
                                <h3><i class="fas fa-info-circle"></i> ข้อมูลพื้นฐาน</h3>
                            </div>
                            <div class="card-body">
                                <div class="info-grid">
                                    <div class="info-item">
                                        <label><i class="fas fa-hashtag"></i> ID</label>
                                        <span id="view_asset_id">-</span>
                                    </div>
                                    <div class="info-item">
                                        <label><i class="fas fa-laptop"></i> ประเภท</label>
                                        <span id="view_type">-</span>
                                    </div>
                                    <div class="info-item">
                                        <label><i class="fas fa-building"></i> ยี่ห้อ</label>
                                        <span id="view_brand">-</span>
                                    </div>
                                    <div class="info-item">
                                        <label><i class="fas fa-cube"></i> รุ่น</label>
                                        <span id="view_model">-</span>
                                    </div>
                                    <div class="info-item">
                                        <label><i class="fas fa-tag"></i> Tag</label>
                                        <span id="view_tag">-</span>
                                    </div>
                                    <div class="info-item">
                                        <label><i class="fas fa-users"></i> แผนก</label>
                                        <span id="view_department">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Technical Information Card -->
                        <div class="info-card">
                            <div class="card-header">
                                <h3><i class="fas fa-cogs"></i> ข้อมูลเทคนิค</h3>
                            </div>
                            <div class="card-body">
                                <div class="info-grid">
                                    <div class="info-item">
                                        <label><i class="fas fa-server"></i> Hostname</label>
                                        <span id="view_hostname">-</span>
                                    </div>
                                    <div class="info-item">
                                        <label><i class="fas fa-desktop"></i> OS</label>
                                        <span id="view_operating_system">-</span>
                                    </div>
                                    <div class="info-item">
                                        <label><i class="fas fa-barcode"></i> Serial Number</label>
                                        <span id="view_serial_number">-</span>
                                    </div>
                                    <div class="info-item">
                                        <label><i class="fas fa-shield-alt"></i> วันหมดประกัน</label>
                                        <span id="view_warranty_expire">-</span>
                                    </div>
                                    <div class="info-item">
                                        <label><i class="fas fa-layer-group"></i> ชุด</label>
                                        <span id="view_set_name">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description Card -->
                        <div class="info-card full-width">
                            <div class="card-header">
                                <h3><i class="fas fa-file-alt"></i> คำอธิบาย</h3>
                            </div>
                            <div class="card-body">
                                <div class="description-content">
                                    <span id="view_description">-</span>
                                </div>
                            </div>
                        </div>

                        <!-- Audit Information Card -->
                        <div class="info-card full-width">
                            <div class="card-header">
                                <h3><i class="fas fa-history"></i> ข้อมูลการจัดการ</h3>
                            </div>
                            <div class="card-body">
                                <div class="audit-grid">
                                    <div class="audit-item">
                                        <div class="audit-icon create">
                                            <i class="fas fa-plus-circle"></i>
                                        </div>
                                        <div class="audit-content">
                                            <label>สร้างเมื่อ</label>
                                            <span id="view_created_date">-</span>
                                            <small>โดย <span id="view_created_by">-</span></small>
                                        </div>
                                    </div>
                                    <div class="audit-item">
                                        <div class="audit-icon update">
                                            <i class="fas fa-edit"></i>
                                        </div>
                                        <div class="audit-content">
                                            <label>แก้ไขล่าสุด</label>
                                            <span id="view_updated_date">-</span>
                                            <small>โดย <span id="view_updated_by">-</span></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Log History Card -->
                        <div class="info-card full-width">
                            <div class="card-header">
                                <h3><i class="fas fa-clipboard-list"></i> ประวัติการเปลี่ยนแปลง</h3>
                                <button onclick="viewFullAssetLogs()" id="view-full-logs-btn" class="btn btn-outline btn-sm">
                                    <i class="fas fa-external-link-alt"></i> ดูทั้งหมด
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="asset-logs-preview" class="logs-preview">
                                    <div class="loading-logs">
                                        <div class="spinner-small"></div>
                                        <span>กำลังโหลด Log...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>

    <!-- Edit Asset Modal -->
    <div id="editAssetModal" class="modal">
        <div class="modal-content asset-modal">
            <div class="modal-header">
                <div class="header-text">
                    <h2>✏️ แก้ไข Asset</h2>
                </div>
                <div class="header-actions">
                    <button class="btn btn-success" onclick="saveEditAsset()" title="บันทึก">
                        บันทึก
                    </button>
                    <span class="close" onclick="closeEditAssetModal()" title="ปิด">&times;</span>
                </div>
            </div>
            <div class="modal-body">
                <form id="editAssetForm">
                    <input type="hidden" id="edit_asset_id_hidden" name="id">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_type">Type <span class="required">*</span></label>
                            <select id="edit_type" name="type" class="form-control" required>
                                <option value="">เลือก Type</option>
                                <option value="Desktop">Desktop</option>
                                <option value="Laptop">Laptop</option>
                                <option value="Monitor">Monitor</option>
                                <option value="All-in-one">All-in-one</option>
                                <option value="Multifunction Laser Printer">Multifunction Laser Printer</option>
                                <option value="Barcode Printer">Barcode Printer</option>
                                <option value="Barcode Scanner">Barcode Scanner</option>
                                <option value="Tablet">Tablet</option>
                                <option value="UPS">UPS</option>
                                <option value="Queue">Queue</option>
                                <option value="IP Phone">IP Phone</option>
                                <option value="Teleconference">Teleconference</option>
                                <option value="Switch">Switch</option>
                                <option value="Access Point">Access Point</option>
                                <option value="Peripheral">Peripheral</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_brand">Brand</label>
                            <select id="edit_brand" name="brand" class="form-control">
                                <option value="">เลือก Brand</option>
                                <option value="-">-</option>
                                <option value="Dell">Dell</option>
                                <option value="Lenovo">Lenovo</option>
                                <option value="Microsoft">Microsoft</option>
                                <option value="Apple">Apple</option>
                                <option value="Zebra">Zebra</option>
                                <option value="HP">HP</option>
                                <option value="Philips">Philips</option>
                                <option value="Acer">Acer</option>
                                <option value="LG">LG</option>
                                <option value="Cisco">Cisco</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_model">Model</label>
                            <input type="text" id="edit_model" name="model" class="form-control" placeholder="กรอก Model">
                        </div>

                        <div class="form-group">
                            <label for="edit_tag">Tag</label>
                            <input type="text" id="edit_tag" name="tag" class="form-control" placeholder="กรอก Tag">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_department">Department</label>
                            <select id="edit_department" name="department" class="form-control">
                                <option value="">เลือก Department</option>
                                <option value="Accounting">Accounting</option>
                                <option value="Admission">Admission</option>
                                <option value="Anesthetic">Anesthetic</option>
                                <option value="BME">BME</option>
                                <option value="Cath Lab">Cath Lab</option>
                                <option value="Cardiac Care Unit">Cardiac Care Unit</option>
                                <option value="CEO">CEO</option>
                                <option value="Chivawattana">Chivawattana</option>
                                <option value="Cardiology & Neurological Clinic">Cardiology & Neurological Clinic</option>
                                <option value="Collection">Collection</option>
                                <option value="Contact Center">Contact Center</option>
                                <option value="Cashier IPD">Cashier IPD</option>
                                <option value="Cashier OPD">Cashier OPD</option>
                                <option value="Customer Service">Customer Service</option>
                                <option value="Dental">Dental</option>
                                <option value="Doctor Room">Doctor Room</option>
                                <option value="EMT">EMT</option>
                                <option value="ENT">ENT</option>
                                <option value="Endoscopy Center">Endoscopy Center</option>
                                <option value="Eye Center">Eye Center</option>
                                <option value="Emergency Room">Emergency Room</option>
                                <option value="Financial">Financial</option>
                                <option value="General Support">General Support</option>
                                <option value="Hemodialysis">Hemodialysis</option>
                                <option value="Hemodialysis VIP">Hemodialysis VIP</option>
                                <option value="Health Promotion Center">Health Promotion Center</option>
                                <option value="Human Resources">Human Resources</option>
                                <option value="Intensive Care Unit">Intensive Care Unit</option>
                                <option value="IT">IT</option>
                                <option value="Laboratory">Laboratory</option>
                                <option value="Labour Room">Labour Room</option>
                                <option value="Linen Service">Linen Service</option>
                                <option value="MAO">MAO</option>
                                <option value="Medicine Unit">Medicine Unit</option>
                                <option value="Marketing">Marketing</option>
                                <option value="Medical Record">Medical Record</option>
                                <option value="N-Health">N-Health</option>
                                <option value="Nursery">Nursery</option>
                                <option value="NSO">NSO</option>
                                <option value="Obstetrics & Gynecology">Obstetrics & Gynecology</option>
                                <option value="Operating Room">Operating Room</option>
                                <option value="Orthopedic">Orthopedic</option>
                                <option value="Pharmacy OPD">Pharmacy OPD</option>
                                <option value="Pharmacy OPD (Floor 2)">Pharmacy OPD (Floor 2)</option>
                                <option value="Pharmacy IPD">Pharmacy IPD</option>
                                <option value="Pediatrics">Pediatrics</option>
                                <option value="PUR">PUR</option>
                                <option value="Referral">Referral</option>
                                <option value="Registration">Registration</option>
                                <option value="Repair & Maintenance">Repair & Maintenance</option>
                                <option value="Rehabilitation">Rehabilitation</option>
                                <option value="Secretary">Secretary</option>
                                <option value="Sterlie">Sterlie</option>
                                <option value="Supervisor">Supervisor</option>
                                <option value="Supply">Supply</option>
                                <option value="Surgery Unit">Surgery Unit</option>
                                <option value="Quality Center">Quality Center</option>
                                <option value="Utilization Management">Utilization Management</option>
                                <option value="W5A">W5A</option>
                                <option value="W5B">W5B</option>
                                <option value="W6A">W6A</option>
                                <option value="W6B">W6B</option>
                                <option value="W7A">W7A</option>
                                <option value="W7B">W7B</option>
                                <option value="W8A">W8A</option>
                                <option value="W8B">W8B</option>
                                <option value="W9A">W9A</option>
                                <option value="W9B">W9B</option>
                                <option value="Wound Care Unit">Wound Care Unit</option>
                                <option value="Wellness Center">Wellness Center</option>
                                <option value="X-ray">X-ray</option>
                                <option value="X-ray NP">X-ray NP</option>
                                <option value="เวรเปล">เวรเปล</option>
                                <option value="Food House">Food House</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_status">Status <span class="required">*</span></label>
                            <select id="edit_status" name="status" class="form-control" required>
                                <option value="ใช้งาน">ใช้งาน</option>
                                <option value="ชำรุด">ชำรุด</option>
                                <option value="สำรอง">สำรอง</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_hostname">Hostname</label>
                            <input type="text" id="edit_hostname" name="hostname" class="form-control" placeholder="กรอก Hostname">
                        </div>

                        <div class="form-group">
                            <label for="edit_operating_system">Operating System</label>
                            <select id="edit_operating_system" name="operating_system" class="form-control">
                                <option value="-">-</option>
                                <option value="Windows 7">Windows 7</option>
                                <option value="Windows 10">Windows 10</option>
                                <option value="Windows 11">Windows 11</option>
                                <option value="MacOS">MacOS</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_serial_number">Serial Number</label>
                            <input type="text" id="edit_serial_number" name="serial_number" class="form-control" placeholder="กรอก Serial Number">
                        </div>

                        <div class="form-group">
                            <label for="edit_asset_id">Asset ID</label>
                            <input type="text" id="edit_asset_id" name="asset_id" class="form-control" placeholder="กรอก Asset ID">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_warranty_expire">Warranty Expire</label>
                            <input type="date" id="edit_warranty_expire" name="warranty_expire" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="edit_set_name">Set</label>
                            <input type="text" id="edit_set_name" name="set_name" class="form-control" placeholder="กรอกชื่อ Set">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_description">Description</label>
                        <textarea id="edit_description" name="description" class="form-control" rows="3" placeholder="กรอก Description"></textarea>
                    </div>
                </form>
            </div>

        </div>
    </div>

    <!-- Tools Modal -->
    <div id="toolsModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-tools"></i> เครื่องมือ</h2>
                <span class="close" onclick="closeToolsModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="tools-grid">
                    <?php if (isAdmin()): ?>
                        <div class="tool-item">
                            <a href="import_sample.php" class="tool-link">
                                <div class="tool-icon">
                                    <i class="fas fa-file-import"></i>
                                </div>
                                <div class="tool-info">
                                    <h3>Import Sample CSV</h3>
                                    <p>นำเข้าข้อมูล Asset จากไฟล์ CSV</p>
                                </div>
                            </a>
                        </div>
                    <?php endif; ?>

                    <div class="tool-item">
                        <a href="#" onclick="exportData(); closeToolsModal();" class="tool-link">
                            <div class="tool-icon">
                                <i class="fas fa-file-export"></i>
                            </div>
                            <div class="tool-info">
                                <h3>Export ข้อมูล</h3>
                                <p>ส่งออกข้อมูล Asset เป็นไฟล์ CSV</p>
                            </div>
                        </a>
                    </div>

                    <div class="tool-item">
                        <a href="#" onclick="printReport(); closeToolsModal();" class="tool-link">
                            <div class="tool-icon">
                                <i class="fas fa-print"></i>
                            </div>
                            <div class="tool-info">
                                <h3>พิมพ์รายงาน</h3>
                                <p>พิมพ์รายงาน Asset ทั้งหมด</p>
                            </div>
                        </a>
                    </div>

                    <div class="tool-item">
                        <a href="#" onclick="openDepartmentReportModal(); closeToolsModal();" class="tool-link">
                            <div class="tool-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="tool-info">
                                <h3>รายงานแผนก</h3>
                                <p>สร้างรายงาน Asset ตามแผนก</p>
                            </div>
                        </a>
                    </div>

                    <div class="tool-item">
                        <a href="#" onclick="exportExcel(); closeToolsModal();" class="tool-link">
                            <div class="tool-icon">
                                <i class="fas fa-file-excel"></i>
                            </div>
                            <div class="tool-info">
                                <h3>Export Excel</h3>
                                <p>ส่งออกข้อมูล Asset เป็นไฟล์ Excel</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Report Modal -->
    <div id="departmentReportModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-building"></i> รายงานแผนก</h2>
                <span class="close" onclick="closeDepartmentReportModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="departmentReportForm">
                    <div class="form-group">
                        <label for="department_select">เลือกแผนก:</label>
                        <select id="department_select" name="department" required>
                            <option value="">-- เลือกแผนก --</option>
                            <option value="Accounting">Accounting</option>
                            <option value="Admission">Admission</option>
                            <option value="Anesthetic">Anesthetic</option>
                            <option value="BME">BME</option>
                            <option value="IT">IT</option>
                            <option value="Laboratory">Laboratory</option>
                            <option value="Emergency Room">Emergency Room</option>
                            <option value="Pharmacy">Pharmacy</option>
                            <option value="Radiology">Radiology</option>
                            <option value="Surgery">Surgery</option>
                            <option value="Cardiology">Cardiology</option>
                            <option value="Neurology">Neurology</option>
                            <option value="Orthopedics">Orthopedics</option>
                            <option value="Pediatrics">Pediatrics</option>
                            <option value="Obstetrics">Obstetrics</option>
                            <option value="Psychiatry">Psychiatry</option>
                            <option value="Dermatology">Dermatology</option>
                            <option value="Ophthalmology">Ophthalmology</option>
                            <option value="ENT">ENT</option>
                            <option value="Urology">Urology</option>
                            <option value="Oncology">Oncology</option>
                            <option value="Endocrinology">Endocrinology</option>
                            <option value="Gastroenterology">Gastroenterology</option>
                            <option value="Pulmonology">Pulmonology</option>
                            <option value="Nephrology">Nephrology</option>
                            <option value="Rheumatology">Rheumatology</option>
                            <option value="Infectious Disease">Infectious Disease</option>
                            <option value="Physical Medicine">Physical Medicine</option>
                            <option value="Nuclear Medicine">Nuclear Medicine</option>
                            <option value="Pathology">Pathology</option>
                            <option value="Blood Bank">Blood Bank</option>
                            <option value="Microbiology">Microbiology</option>
                            <option value="Clinical Chemistry">Clinical Chemistry</option>
                            <option value="Hematology">Hematology</option>
                            <option value="Immunology">Immunology</option>
                            <option value="Cytology">Cytology</option>
                            <option value="Histopathology">Histopathology</option>
                            <option value="Medical Records">Medical Records</option>
                            <option value="Quality Assurance">Quality Assurance</option>
                            <option value="Risk Management">Risk Management</option>
                            <option value="Infection Control">Infection Control</option>
                            <option value="Safety">Safety</option>
                            <option value="Security">Security</option>
                            <option value="Maintenance">Maintenance</option>
                            <option value="Housekeeping">Housekeeping</option>
                            <option value="Laundry">Laundry</option>
                            <option value="Kitchen">Kitchen</option>
                            <option value="Nutrition">Nutrition</option>
                            <option value="Social Work">Social Work</option>
                            <option value="Chaplain">Chaplain</option>
                            <option value="Volunteer">Volunteer</option>
                            <option value="Education">Education</option>
                            <option value="Research">Research</option>
                            <option value="Library">Library</option>
                            <option value="Public Relations">Public Relations</option>
                            <option value="Marketing">Marketing</option>
                            <option value="Legal">Legal</option>
                            <option value="Compliance">Compliance</option>
                            <option value="Audit">Audit</option>
                            <option value="Finance">Finance</option>
                            <option value="Purchasing">Purchasing</option>
                            <option value="Supply Chain">Supply Chain</option>
                            <option value="Warehouse">Warehouse</option>
                            <option value="Transportation">Transportation</option>
                            <option value="Facilities">Facilities</option>
                            <option value="Engineering">Engineering</option>
                            <option value="Environmental">Environmental</option>
                            <option value="Waste Management">Waste Management</option>
                            <option value="Energy Management">Energy Management</option>
                            <option value="Telecommunications">Telecommunications</option>
                            <option value="Network">Network</option>
                            <option value="Database">Database</option>
                            <option value="Application Support">Application Support</option>
                            <option value="Help Desk">Help Desk</option>
                            <option value="Training">Training</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="button" onclick="generateDepartmentReport()" class="btn btn-primary">
                            <i class="fas fa-file-pdf"></i> รายงาน PDF
                        </button>
                        <button type="button" onclick="generateDepartmentExcel()" class="btn btn-success">
                            <i class="fas fa-file-excel"></i> รายงาน Excel
                        </button>
                        <button type="button" onclick="closeDepartmentReportModal()" class="btn btn-secondary">
                            ยกเลิก
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>



</body>
</html>
