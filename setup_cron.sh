#!/bin/bash

# Asset Management System - Auto Backup Setup Script
# สคริปต์สำหรับตั้งค่า Cron Job อัตโนมัติ

echo "=== Asset Management System - Auto Backup Setup ==="
echo ""

# ตรวจสอบว่าเป็น root หรือไม่
if [ "$EUID" -ne 0 ]; then
    echo "กรุณารันสคริปต์นี้ด้วยสิทธิ์ root หรือใช้ sudo"
    exit 1
fi

# ตรวจสอบ path ปัจจุบัน
CURRENT_DIR=$(pwd)
echo "ไดเรกทอรีปัจจุบัน: $CURRENT_DIR"

# ตรวจสอบว่ามีไฟล์ auto_backup.php หรือไม่
if [ ! -f "$CURRENT_DIR/auto_backup.php" ]; then
    echo "ข้อผิดพลาด: ไม่พบไฟล์ auto_backup.php ในไดเรกทอรีนี้"
    echo "กรุณาเรียกใช้สคริปต์นี้ในโฟลเดอร์ที่มีไฟล์ระบบ Asset Management"
    exit 1
fi

# ตรวจสอบ PHP
PHP_PATH=$(which php)
if [ -z "$PHP_PATH" ]; then
    echo "ข้อผิดพลาด: ไม่พบ PHP ในระบบ"
    echo "กรุณาติดตั้ง PHP ก่อนใช้งาน"
    exit 1
fi

echo "พบ PHP ที่: $PHP_PATH"

# สร้างโฟลเดอร์ที่จำเป็น
echo "สร้างโฟลเดอร์ที่จำเป็น..."
mkdir -p "$CURRENT_DIR/backups"
mkdir -p "$CURRENT_DIR/logs"

# ตั้งค่าสิทธิ์
chmod 755 "$CURRENT_DIR/backups"
chmod 755 "$CURRENT_DIR/logs"
chmod 644 "$CURRENT_DIR/auto_backup.php"

echo "ตั้งค่าสิทธิ์เรียบร้อย"

# สร้าง cron job
CRON_JOB="0 2 * * * cd $CURRENT_DIR && $PHP_PATH auto_backup.php >> logs/cron.log 2>&1"

echo ""
echo "=== การตั้งค่า Cron Job ==="
echo "คำสั่งที่จะเพิ่ม:"
echo "$CRON_JOB"
echo ""

# ตรวจสอบว่ามี cron job นี้อยู่แล้วหรือไม่
if crontab -l 2>/dev/null | grep -q "auto_backup.php"; then
    echo "พบ cron job สำหรับ auto backup อยู่แล้ว"
    echo "ต้องการแทนที่หรือไม่? (y/n)"
    read -r response
    if [ "$response" != "y" ] && [ "$response" != "Y" ]; then
        echo "ยกเลิกการตั้งค่า"
        exit 0
    fi
    
    # ลบ cron job เก่า
    crontab -l 2>/dev/null | grep -v "auto_backup.php" | crontab -
    echo "ลบ cron job เก่าแล้ว"
fi

# เพิ่ม cron job ใหม่
(crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -

if [ $? -eq 0 ]; then
    echo "✓ ตั้งค่า cron job สำเร็จ!"
    echo ""
    echo "=== ข้อมูลการตั้งค่า ==="
    echo "เวลาทำ backup: ทุกวันเวลา 02:00 น."
    echo "ไฟล์ backup จะถูกเก็บที่: $CURRENT_DIR/backups/"
    echo "ไฟล์ log จะถูกเก็บที่: $CURRENT_DIR/logs/"
    echo ""
    echo "=== คำสั่งที่มีประโยชน์ ==="
    echo "ดู cron jobs ทั้งหมด: crontab -l"
    echo "แก้ไข cron jobs: crontab -e"
    echo "ลบ cron job นี้: crontab -l | grep -v 'auto_backup.php' | crontab -"
    echo "ทดสอบ backup ด้วยตนเอง: cd $CURRENT_DIR && php auto_backup.php"
    echo ""
    echo "=== การตรวจสอบ ==="
    echo "ตรวจสอบ log: tail -f $CURRENT_DIR/logs/backup_$(date +%Y-%m-%d).log"
    echo "ตรวจสอบ cron log: tail -f $CURRENT_DIR/logs/cron.log"
    echo ""
else
    echo "✗ เกิดข้อผิดพลาดในการตั้งค่า cron job"
    exit 1
fi

# ทดสอบการทำงาน
echo "ต้องการทดสอบการทำ backup ทันทีหรือไม่? (y/n)"
read -r test_response
if [ "$test_response" = "y" ] || [ "$test_response" = "Y" ]; then
    echo ""
    echo "=== ทดสอบการทำ backup ==="
    cd "$CURRENT_DIR"
    php auto_backup.php
    
    if [ $? -eq 0 ]; then
        echo "✓ ทดสอบ backup สำเร็จ!"
        echo "ตรวจสอบไฟล์ backup ในโฟลเดอร์: $CURRENT_DIR/backups/"
    else
        echo "✗ เกิดข้อผิดพลาดในการทดสอบ backup"
        echo "กรุณาตรวจสอบการตั้งค่าฐานข้อมูลและสิทธิ์ไฟล์"
    fi
fi

echo ""
echo "=== การตั้งค่าเสร็จสิ้น ==="
echo "ระบบ Auto Backup พร้อมใช้งานแล้ว!"
echo ""

# แสดงข้อมูลเพิ่มเติม
echo "=== ข้อมูลเพิ่มเติม ==="
echo "1. ระบบจะทำ backup อัตโนมัติทุกวันเวลา 02:00 น."
echo "2. ไฟล์ backup เก่าจะถูกลบอัตโนมัติหลังจาก 30 วัน"
echo "3. สามารถจัดการ backup ผ่านเว็บได้ที่หน้า backup_system.php"
echo "4. ตรวจสอบ log เป็นประจำเพื่อให้แน่ใจว่าระบบทำงานปกติ"
echo ""
echo "หากมีปัญหา กรุณาติดต่อผู้ดูแลระบบ"
