<?php
/**
 * Auto Install MongoDB Extension
 * Asset Management System - Automatic Installation Helper
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='th'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Auto Install MongoDB Extension</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .header { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; padding: 30px; border-radius: 15px; text-align: center; margin-bottom: 30px; }
        .install-card { background: white; border-radius: 10px; padding: 25px; margin: 20px 0; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .status { padding: 10px 15px; border-radius: 8px; margin: 10px 0; font-weight: bold; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 8px; margin: 5px; transition: all 0.3s; border: none; cursor: pointer; font-size: 16px; }
        .btn:hover { background: #0056b3; transform: translateY(-2px); }
        .btn.success { background: #28a745; }
        .btn.danger { background: #dc3545; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; margin: 10px 0; border-left: 4px solid #007bff; overflow-x: auto; }
        .progress { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 15px 0; }
        .progress-bar { height: 100%; background: linear-gradient(90deg, #007bff, #0056b3); transition: width 0.5s; }
        .step { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #28a745; }
    </style>
</head>
<body>";

echo "<div class='header'>
    <h1>🚀 Auto Install MongoDB Extension</h1>
    <p>ติดตั้ง MongoDB PHP Extension อัตโนมัติ</p>
</div>";

// System detection
$os = php_uname('s');
$phpVersion = phpversion();
$phpArch = (PHP_INT_SIZE === 8) ? 'x64' : 'x86';
$isThreadSafe = (defined('ZEND_THREAD_SAFE') && ZEND_THREAD_SAFE) ? 'TS' : 'NTS';

echo "<div class='install-card'>
<h2>📊 ข้อมูลระบบ</h2>
<div class='status info'>
<p><strong>Operating System:</strong> $os</p>
<p><strong>PHP Version:</strong> $phpVersion</p>
<p><strong>Architecture:</strong> $phpArch</p>
<p><strong>Thread Safety:</strong> $isThreadSafe</p>
<p><strong>PHP Extension Dir:</strong> " . ini_get('extension_dir') . "</p>
<p><strong>PHP Config File:</strong> " . php_ini_loaded_file() . "</p>
</div>
</div>";

// Check if already installed
if (extension_loaded('mongodb')) {
    echo "<div class='install-card'>
    <div class='status success'>
    <h3>✅ MongoDB Extension Already Installed</h3>
    <p>Version: " . phpversion('mongodb') . "</p>
    <p>Extension is working properly!</p>
    </div>
    <p><a href='mongodb_test.php' class='btn success'>🧪 Test MongoDB System</a></p>
    </div>";
} else {
    // Installation process
    echo "<div class='install-card'>
    <h2>🔧 Installation Process</h2>";
    
    if (isset($_POST['install'])) {
        echo "<div class='progress'>
        <div class='progress-bar' style='width: 0%' id='progressBar'></div>
        </div>
        <div id='installLog'>";
        
        // Start installation
        installMongoDBExtension();
        
        echo "</div>";
    } else {
        // Show installation options
        showInstallationOptions();
    }
    
    echo "</div>";
}

function showInstallationOptions() {
    global $os, $phpVersion;
    
    echo "<div class='status warning'>
    <h3>⚠️ MongoDB Extension Not Found</h3>
    <p>MongoDB PHP extension is not installed. Choose installation method:</p>
    </div>";
    
    if (stripos($os, 'Windows') !== false) {
        echo "<div class='step'>
        <h4>🪟 Windows Installation</h4>
        <p>For Windows systems, manual installation is recommended:</p>
        <ol>
        <li>Download the appropriate DLL file</li>
        <li>Copy to PHP extension directory</li>
        <li>Update php.ini file</li>
        <li>Restart web server</li>
        </ol>
        <p><a href='install_mongodb_extension.php' class='btn'>📖 Detailed Guide</a></p>
        </div>";
        
    } elseif (stripos($os, 'Linux') !== false) {
        echo "<div class='step'>
        <h4>🐧 Linux Installation</h4>
        <p>Automatic installation available for Linux systems.</p>
        <form method='post'>
        <button type='submit' name='install' value='linux' class='btn success'>🚀 Auto Install (Linux)</button>
        </form>
        </div>";
        
    } elseif (stripos($os, 'Darwin') !== false) {
        echo "<div class='step'>
        <h4>🍎 macOS Installation</h4>
        <p>Automatic installation available for macOS systems.</p>
        <form method='post'>
        <button type='submit' name='install' value='macos' class='btn success'>🚀 Auto Install (macOS)</button>
        </form>
        </div>";
    }
    
    echo "<div class='step'>
    <h4>🐳 Alternative: Use Docker</h4>
    <p>For consistent environment across all platforms:</p>
    <div class='code'>
docker run -d --name mongodb-asset \\
  -p 80:80 -p 27017:27017 \\
  -v \$(pwd):/var/www/html \\
  php:8.2-apache
    </div>
    </div>";
}

function installMongoDBExtension() {
    global $os;
    
    updateProgress(10, "Starting installation...");
    
    if (stripos($os, 'Linux') !== false) {
        installLinux();
    } elseif (stripos($os, 'Darwin') !== false) {
        installMacOS();
    } else {
        echo "<div class='status error'>❌ Automatic installation not supported for this OS</div>";
        return false;
    }
}

function installLinux() {
    updateProgress(20, "Detecting Linux distribution...");
    
    // Detect Linux distribution
    $distro = '';
    if (file_exists('/etc/os-release')) {
        $osRelease = file_get_contents('/etc/os-release');
        if (strpos($osRelease, 'Ubuntu') !== false || strpos($osRelease, 'Debian') !== false) {
            $distro = 'debian';
        } elseif (strpos($osRelease, 'CentOS') !== false || strpos($osRelease, 'RHEL') !== false || strpos($osRelease, 'Fedora') !== false) {
            $distro = 'redhat';
        }
    }
    
    updateProgress(30, "Distribution detected: $distro");
    
    if ($distro === 'debian') {
        installDebianUbuntu();
    } elseif ($distro === 'redhat') {
        installRedHat();
    } else {
        installPECL();
    }
}

function installDebianUbuntu() {
    updateProgress(40, "Installing on Debian/Ubuntu...");
    
    $commands = [
        'sudo apt update',
        'sudo apt install -y php-mongodb',
        'sudo systemctl restart apache2'
    ];
    
    foreach ($commands as $i => $command) {
        updateProgress(40 + ($i * 15), "Running: $command");
        $output = shell_exec($command . ' 2>&1');
        echo "<div class='code'>$command\n$output</div>";
    }
    
    updateProgress(90, "Verifying installation...");
    verifyInstallation();
}

function installRedHat() {
    updateProgress(40, "Installing on RedHat/CentOS/Fedora...");
    
    $commands = [
        'sudo yum install -y php-mongodb || sudo dnf install -y php-mongodb',
        'sudo systemctl restart httpd'
    ];
    
    foreach ($commands as $i => $command) {
        updateProgress(40 + ($i * 20), "Running: $command");
        $output = shell_exec($command . ' 2>&1');
        echo "<div class='code'>$command\n$output</div>";
    }
    
    updateProgress(90, "Verifying installation...");
    verifyInstallation();
}

function installMacOS() {
    updateProgress(40, "Installing on macOS...");
    
    $commands = [
        'brew install php',
        'pecl install mongodb',
        'brew services restart httpd'
    ];
    
    foreach ($commands as $i => $command) {
        updateProgress(40 + ($i * 15), "Running: $command");
        $output = shell_exec($command . ' 2>&1');
        echo "<div class='code'>$command\n$output</div>";
    }
    
    updateProgress(90, "Verifying installation...");
    verifyInstallation();
}

function installPECL() {
    updateProgress(40, "Installing via PECL...");
    
    $commands = [
        'sudo pecl install mongodb',
        'echo "extension=mongodb" | sudo tee -a ' . php_ini_loaded_file()
    ];
    
    foreach ($commands as $i => $command) {
        updateProgress(40 + ($i * 20), "Running: $command");
        $output = shell_exec($command . ' 2>&1');
        echo "<div class='code'>$command\n$output</div>";
    }
    
    updateProgress(90, "Verifying installation...");
    verifyInstallation();
}

function verifyInstallation() {
    updateProgress(95, "Checking installation...");
    
    // Check if extension is loaded
    $output = shell_exec('php -m | grep mongodb 2>&1');
    
    if (strpos($output, 'mongodb') !== false) {
        updateProgress(100, "Installation completed successfully!");
        echo "<div class='status success'>
        <h3>✅ Installation Successful!</h3>
        <p>MongoDB PHP extension has been installed.</p>
        <p><strong>Note:</strong> You may need to restart your web server.</p>
        </div>";
        
        echo "<p><a href='mongodb_setup_check.php' class='btn success'>🔍 Verify Installation</a></p>";
        echo "<p><a href='mongodb_test.php' class='btn'>🧪 Test MongoDB System</a></p>";
    } else {
        echo "<div class='status error'>
        <h3>❌ Installation Failed</h3>
        <p>MongoDB extension was not installed successfully.</p>
        <p>Please try manual installation or check the error messages above.</p>
        </div>";
        
        echo "<p><a href='install_mongodb_extension.php' class='btn'>📖 Manual Installation Guide</a></p>";
    }
}

function updateProgress($percentage, $message) {
    echo "<script>
    document.getElementById('progressBar').style.width = '$percentage%';
    </script>";
    echo "<div class='status info'>$message</div>";
    flush();
    ob_flush();
    sleep(1); // Simulate processing time
}

// Additional tools
echo "<div class='install-card'>
<h2>🛠️ Additional Tools</h2>
<p><a href='install_mongodb_extension.php' class='btn'>📖 Manual Installation Guide</a></p>
<p><a href='mongodb_setup_check.php' class='btn'>🔍 Setup Check</a></p>
<p><a href='mongodb_test.php' class='btn'>🧪 Test System</a></p>
<p><a href='mongodb_migration.php' class='btn'>🔄 Migration Tool</a></p>
</div>";

echo "<div class='install-card'>
<h2>💡 Tips</h2>
<ul>
<li>Make sure you have administrator/sudo privileges</li>
<li>Restart your web server after installation</li>
<li>Check PHP error logs if installation fails</li>
<li>Use manual installation for Windows systems</li>
<li>Consider using Docker for consistent environment</li>
</ul>
</div>";

echo "</body></html>";
?>
