<?php
require_once 'backup_config.php';

/**
 * Auto Backup Class
 * คลาสสำหรับจัดการ backup อัตโนมัติ
 */
class AutoBackup {
    private $pdo;
    private $logFile;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->logFile = BackupConfig::getLogPath() . 'auto_backup_' . date('Y-m-d') . '.log';
    }
    
    /**
     * ทำ backup อัตโนมัติเมื่อมีการเปลี่ยนแปลงข้อมูล
     */
    public static function triggerBackup($action, $details = []) {
        global $pdo;
        
        // ตรวจสอบว่าเปิดใช้งาน backup หรือไม่
        if (!BackupConfig::isEnabled()) {
            return false;
        }
        
        // ตรวจสอบว่าควรทำ backup สำหรับ action นี้หรือไม่
        if (!BackupConfig::shouldBackupOnChange($action)) {
            return false;
        }
        
        try {
            $backup = new self($pdo);
            return $backup->createBackup($action, $details);
        } catch (Exception $e) {
            error_log("Auto Backup Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * สร้างไฟล์ backup
     */
    public function createBackup($action = 'manual', $details = []) {
        $startTime = microtime(true);
        $this->writeLog("=== Auto Backup Started ===");
        $this->writeLog("Action: $action");
        
        if (!empty($details)) {
            $this->writeLog("Details: " . json_encode($details));
        }
        
        try {
            // สร้างชื่อไฟล์ backup
            $prefix = BackupConfig::get('backup_prefix', 'auto_');
            $filename = $prefix . $action . '_' . date('Y-m-d_H-i-s') . '.sql';
            $filepath = BackupConfig::getBackupPath() . $filename;
            
            $this->writeLog("Creating backup: $filename");
            
            // สร้างเนื้อหา backup
            $backup = $this->generateBackupContent($action, $details);
            
            // บันทึกไฟล์
            if (file_put_contents($filepath, $backup)) {
                $fileSize = filesize($filepath);
                $duration = round(microtime(true) - $startTime, 2);
                
                $this->writeLog("Backup completed successfully");
                $this->writeLog("File: $filename");
                $this->writeLog("Size: " . $this->formatBytes($fileSize));
                $this->writeLog("Duration: {$duration}s");
                
                // ทำความสะอาดไฟล์เก่า
                $this->cleanOldBackups();
                
                // ส่งการแจ้งเตือน (ถ้าเปิดใช้งาน)
                if (BackupConfig::get('email_notifications', false)) {
                    $this->sendNotification($filename, $fileSize, true, $action, $details);
                }
                
                $this->writeLog("=== Auto Backup Completed ===");
                return ['success' => true, 'filename' => $filename, 'size' => $fileSize];
                
            } else {
                throw new Exception("Failed to write backup file: $filepath");
            }
            
        } catch (Exception $e) {
            $this->writeLog("ERROR: " . $e->getMessage());
            
            if (BackupConfig::get('email_notifications', false)) {
                $this->sendNotification('', 0, false, $action, $details, $e->getMessage());
            }
            
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * สร้างเนื้อหา backup
     */
    private function generateBackupContent($action, $details) {
        $backup = "-- Asset Management System Auto Backup\n";
        $backup .= "-- Created: " . date('Y-m-d H:i:s') . "\n";
        $backup .= "-- Trigger: $action\n";
        $backup .= "-- Details: " . json_encode($details) . "\n";
        $backup .= "-- Database: asset_management\n\n";
        
        $backup .= "SET FOREIGN_KEY_CHECKS = 0;\n";
        $backup .= "SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';\n";
        $backup .= "SET AUTOCOMMIT = 0;\n";
        $backup .= "START TRANSACTION;\n\n";
        
        // ดึงรายชื่อตาราง
        $tables = $this->pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            $this->writeLog("Backing up table: $table");
            
            // สร้าง DROP TABLE
            $backup .= "-- Table: $table\n";
            $backup .= "DROP TABLE IF EXISTS `$table`;\n";
            
            // สร้าง CREATE TABLE
            $createTable = $this->pdo->query("SHOW CREATE TABLE `$table`")->fetch(PDO::FETCH_ASSOC);
            $backup .= $createTable['Create Table'] . ";\n\n";
            
            // ดึงข้อมูลในตาราง
            $stmt = $this->pdo->query("SELECT * FROM `$table`");
            $rowCount = 0;
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if ($rowCount === 0) {
                    $columns = array_keys($row);
                    $backup .= "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES\n";
                }
                
                $values = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $values[] = 'NULL';
                    } else {
                        $values[] = "'" . addslashes($value) . "'";
                    }
                }
                
                if ($rowCount > 0) {
                    $backup .= ",\n";
                }
                $backup .= '(' . implode(', ', $values) . ')';
                $rowCount++;
            }
            
            if ($rowCount > 0) {
                $backup .= ";\n\n";
            }
        }
        
        $backup .= "COMMIT;\n";
        $backup .= "SET FOREIGN_KEY_CHECKS = 1;\n";
        $backup .= "-- Backup completed at " . date('Y-m-d H:i:s') . "\n";
        
        return $backup;
    }
    
    /**
     * ทำความสะอาดไฟล์ backup เก่า
     */
    private function cleanOldBackups() {
        $backupPath = BackupConfig::getBackupPath();
        $maxFiles = BackupConfig::get('max_backup_files', 50);
        $keepDays = BackupConfig::get('keep_days', 30);
        
        $files = glob($backupPath . '*.sql');
        
        // เรียงตามวันที่ (ใหม่ไปเก่า)
        usort($files, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        $deleted = 0;
        $cutoffTime = time() - ($keepDays * 24 * 60 * 60);
        
        // ลบไฟล์ที่เก่าเกินกำหนด
        foreach ($files as $index => $file) {
            $shouldDelete = false;
            
            // ลบถ้าเก่าเกินกำหนดวัน
            if (filemtime($file) < $cutoffTime) {
                $shouldDelete = true;
            }
            
            // ลบถ้าเกินจำนวนไฟล์ที่กำหนด
            if ($index >= $maxFiles) {
                $shouldDelete = true;
            }
            
            if ($shouldDelete && unlink($file)) {
                $deleted++;
                $this->writeLog("Deleted old backup: " . basename($file));
            }
        }
        
        if ($deleted > 0) {
            $this->writeLog("Cleaned up $deleted old backup files");
        }
    }
    
    /**
     * ส่งการแจ้งเตือน
     */
    private function sendNotification($filename, $fileSize, $success, $action, $details, $error = '') {
        $to = BackupConfig::get('admin_email', '<EMAIL>');
        $subject = $success ? 'Auto Backup สำเร็จ - Asset Management' : 'Auto Backup ล้มเหลว - Asset Management';
        
        if ($success) {
            $message = "การ backup อัตโนมัติสำเร็จ\n\n";
            $message .= "เหตุผล: $action\n";
            $message .= "ไฟล์: $filename\n";
            $message .= "ขนาด: " . $this->formatBytes($fileSize) . "\n";
            $message .= "รายละเอียด: " . json_encode($details) . "\n";
            $message .= "เวลา: " . date('Y-m-d H:i:s') . "\n";
        } else {
            $message = "การ backup อัตโนมัติล้มเหลว\n\n";
            $message .= "เหตุผล: $action\n";
            $message .= "ข้อผิดพลาด: $error\n";
            $message .= "รายละเอียด: " . json_encode($details) . "\n";
            $message .= "เวลา: " . date('Y-m-d H:i:s') . "\n";
        }
        
        $headers = 'From: <EMAIL>' . "\r\n" .
                   'Reply-To: <EMAIL>' . "\r\n" .
                   'X-Mailer: PHP/' . phpversion();
        
        // ส่งอีเมล (ยกเลิก comment ถ้าต้องการใช้งาน)
        // mail($to, $subject, $message, $headers);
        
        $this->writeLog("Notification prepared: " . ($success ? 'Success' : 'Failed'));
    }
    
    /**
     * เขียน log
     */
    private function writeLog($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * แปลงขนาดไฟล์
     */
    private function formatBytes($size, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        
        return round($size, $precision) . ' ' . $units[$i];
    }
}
?>
