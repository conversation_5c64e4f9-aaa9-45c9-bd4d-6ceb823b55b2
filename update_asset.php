<?php
require_once 'includes/auth.php';

// ตรวจสอบการล็อกอิน
if (!$auth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'ไม่ได้รับอนุญาต']);
    exit;
}

// ตรวจสอบ method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method ไม่ถูกต้อง']);
    exit;
}

// ตรวจสอบ ID
if (!isset($_POST['id']) || !is_numeric($_POST['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID ไม่ถูกต้อง']);
    exit;
}

$assetId = (int)$_POST['id'];

// รับข้อมูลจากฟอร์ม
$type = trim($_POST['type'] ?? '');
$brand = trim($_POST['brand'] ?? '');
$model = trim($_POST['model'] ?? '');
$tag = trim($_POST['tag'] ?? '');
$department = trim($_POST['department'] ?? '');
$status = trim($_POST['status'] ?? '');
$hostname = trim($_POST['hostname'] ?? '');
$operating_system = trim($_POST['operating_system'] ?? '');
$serial_number = trim($_POST['serial_number'] ?? '');
$asset_id = trim($_POST['asset_id'] ?? '');
$warranty_expire = trim($_POST['warranty_expire'] ?? '');
$description = trim($_POST['description'] ?? '');
$set_name = trim($_POST['set_name'] ?? '');

// ตรวจสอบข้อมูลที่จำเป็น
if (empty($type)) {
    echo json_encode(['success' => false, 'message' => 'กรุณาเลือก Type']);
    exit;
}

if (empty($status)) {
    echo json_encode(['success' => false, 'message' => 'กรุณาเลือกสถานะ']);
    exit;
}

try {
    // ตรวจสอบว่า Asset มีอยู่จริงและดึงข้อมูลเดิม
    $stmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
    $stmt->execute([$assetId]);
    $oldAsset = $stmt->fetch();
    if (!$oldAsset) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'ไม่พบ Asset']);
        exit;
    }
    
    // เตรียมข้อมูลสำหรับอัพเดท
    $updateData = [
        'type' => $type,
        'brand' => $brand,
        'model' => $model,
        'tag' => $tag,
        'department' => $department,
        'status' => $status,
        'hostname' => $hostname,
        'operating_system' => $operating_system,
        'serial_number' => $serial_number,
        'asset_id' => $asset_id,
        'description' => $description,
        'set_name' => $set_name,
        'updated_date' => date('Y-m-d H:i:s'),
        'updated_by' => $_SESSION['full_name']
    ];
    
    // จัดการวันที่ warranty
    if (!empty($warranty_expire)) {
        $updateData['warranty_expire'] = $warranty_expire;
    } else {
        $updateData['warranty_expire'] = null;
    }
    
    // สร้าง SQL query
    $setParts = [];
    $params = [];
    
    foreach ($updateData as $field => $value) {
        $setParts[] = "$field = ?";
        $params[] = $value;
    }
    
    $params[] = $assetId; // สำหรับ WHERE clause
    
    $sql = "UPDATE assets SET " . implode(', ', $setParts) . " WHERE id = ?";
    
    // ทำการอัพเดท
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute($params);

    if ($result) {
        // บันทึก log การเปลี่ยนแปลง
        logAssetChanges($pdo, $assetId, $oldAsset, $updateData, $_SESSION['full_name']);

        // Trigger auto backup
        triggerAssetEditBackup($assetId, $oldAsset, $updateData);

        echo json_encode([
            'success' => true,
            'message' => 'อัพเดท Asset สำเร็จ'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'เกิดข้อผิดพลาดในการอัพเดท'
        ]);
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดในฐานข้อมูล: ' . $e->getMessage()
    ]);
}

// Function สำหรับบันทึก log การเปลี่ยนแปลง
function logAssetChanges($pdo, $assetId, $oldData, $newData, $full_name) {
    try {
        // รายการ field ที่ต้องตรวจสอบการเปลี่ยนแปลง
        $fieldsToCheck = [
            'type' => 'ประเภท',
            'brand' => 'ยี่ห้อ',
            'model' => 'รุ่น',
            'tag' => 'Tag',
            'department' => 'แผนก',
            'status' => 'สถานะ',
            'hostname' => 'Hostname',
            'operating_system' => 'Operating System',
            'serial_number' => 'Serial Number',
            'asset_id' => 'Asset ID',
            'warranty_expire' => 'วันหมดประกัน',
            'description' => 'คำอธิบาย',
            'asset_set' => 'ชุด'
        ];

        $changedFields = [];

        foreach ($fieldsToCheck as $field => $fieldName) {
            $oldValue = $oldData[$field] ?? '';
            $newValue = $newData[$field] ?? '';

            // แปลงค่า null เป็น string ว่าง
            if ($oldValue === null) $oldValue = '';
            if ($newValue === null) $newValue = '';

            // ตรวจสอบการเปลี่ยนแปลง
            if ($oldValue != $newValue) {
                $changedFields[] = [
                    'field' => $field,
                    'field_name' => $fieldName,
                    'old_value' => $oldValue,
                    'new_value' => $newValue
                ];
            }
        }

        // บันทึก log สำหรับแต่ละ field ที่เปลี่ยนแปลง
        if (!empty($changedFields)) {
            foreach ($changedFields as $change) {
                $description = "เปลี่ยน{$change['field_name']} จาก '{$change['old_value']}' เป็น '{$change['new_value']}'";

                $stmt = $pdo->prepare("
                    INSERT INTO asset_logs (
                        asset_id,
                        action_type,
                        field_name,
                        old_value,
                        new_value,
                        changed_by,
                        changed_date,
                        description
                    ) VALUES (?, 'UPDATE', ?, ?, ?, ?, NOW(), ?)
                ");

                $stmt->execute([
                    $assetId,
                    $change['field'],
                    $change['old_value'],
                    $change['new_value'],
                    $full_name,
                    $description
                ]);
            }

            // บันทึก log รวม
            $totalChanges = count($changedFields);
            $fieldNames = array_column($changedFields, 'field_name');
            $summaryDescription = "อัพเดท Asset - เปลี่ยนแปลง {$totalChanges} ฟิลด์: " . implode(', ', $fieldNames);

            $stmt = $pdo->prepare("
                INSERT INTO asset_logs (
                    asset_id,
                    action_type,
                    field_name,
                    old_value,
                    new_value,
                    changed_by,
                    changed_date,
                    description
                ) VALUES (?, 'UPDATE', NULL, NULL, NULL, ?, NOW(), ?)
            ");

            $stmt->execute([
                $assetId,
                $full_name,
                $summaryDescription
            ]);
        }

    } catch (PDOException $e) {
        // Log error แต่ไม่หยุดการทำงาน
        error_log("Error logging asset changes: " . $e->getMessage());
    }
}
?>
