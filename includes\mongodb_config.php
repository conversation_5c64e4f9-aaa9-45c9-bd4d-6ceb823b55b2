<?php
/**
 * MongoDB Configuration
 * Asset Management System - MongoDB Version
 */

// MongoDB configuration
define('MONGODB_HOST', 'localhost');
define('MONGODB_PORT', '27017');
define('MONGODB_DATABASE', 'asset_management');
define('MONGODB_USERNAME', ''); // ใส่ username ถ้ามี authentication
define('MONGODB_PASSWORD', ''); // ใส่ password ถ้ามี authentication
define('MONGODB_AUTH_SOURCE', 'admin'); // database สำหรับ authentication

// Timezone setting
define('APP_TIMEZONE', 'Asia/Bangkok');
date_default_timezone_set(APP_TIMEZONE);

class MongoDBConnection {
    private static $instance = null;
    private $client;
    private $database;
    
    private function __construct() {
        $this->connect();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function connect() {
        try {
            // สร้าง connection string
            $connectionString = "mongodb://";
            
            // เพิ่ม authentication ถ้ามี
            if (!empty(MONGODB_USERNAME) && !empty(MONG<PERSON>B_PASSWORD)) {
                $connectionString .= MONGODB_USERNAME . ":" . MONGODB_PASSWORD . "@";
            }
            
            $connectionString .= MONGODB_HOST . ":" . MONGODB_PORT;
            
            // เพิ่ม auth source ถ้ามี authentication
            if (!empty(MONGODB_USERNAME)) {
                $connectionString .= "/?authSource=" . MONGODB_AUTH_SOURCE;
            }
            
            // สร้าง MongoDB client
            $this->client = new MongoDB\Client($connectionString);
            
            // เลือกฐานข้อมูล
            $this->database = $this->client->selectDatabase(MONGODB_DATABASE);
            
            // ทดสอบการเชื่อมต่อ
            $this->database->command(['ping' => 1]);
            
        } catch (Exception $e) {
            throw new Exception("MongoDB connection failed: " . $e->getMessage());
        }
    }
    
    public function getDatabase() {
        return $this->database;
    }
    
    public function getClient() {
        return $this->client;
    }
    
    public function testConnection() {
        try {
            $this->database->command(['ping' => 1]);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    public function getServerInfo() {
        try {
            $result = $this->database->command(['buildInfo' => 1]);
            return $result->toArray()[0];
        } catch (Exception $e) {
            return null;
        }
    }
}

// สร้าง global connection
try {
    $mongodb = MongoDBConnection::getInstance();
    $db = $mongodb->getDatabase();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Helper functions
function getMongoConnection() {
    global $mongodb;
    return $mongodb;
}

function getMongoDatabase() {
    global $db;
    return $db;
}

function testMongoConnection() {
    global $mongodb;
    return $mongodb->testConnection();
}

function getMongoServerInfo() {
    global $mongodb;
    return $mongodb->getServerInfo();
}

// Collection names
define('COLLECTION_USERS', 'users');
define('COLLECTION_ASSETS', 'assets');
define('COLLECTION_ASSET_LOGS', 'asset_logs');
define('COLLECTION_SESSIONS', 'sessions');

// MongoDB helper functions
function createMongoId($id = null) {
    if ($id === null) {
        return new MongoDB\BSON\ObjectId();
    }
    
    if ($id instanceof MongoDB\BSON\ObjectId) {
        return $id;
    }
    
    if (is_string($id) && MongoDB\BSON\ObjectId::isValid($id)) {
        return new MongoDB\BSON\ObjectId($id);
    }
    
    throw new InvalidArgumentException("Invalid ObjectId: $id");
}

function mongoIdToString($id) {
    if ($id instanceof MongoDB\BSON\ObjectId) {
        return (string) $id;
    }
    return $id;
}

function createMongoDate($date = null) {
    if ($date === null) {
        return new MongoDB\BSON\UTCDateTime();
    }
    
    if ($date instanceof DateTime) {
        return new MongoDB\BSON\UTCDateTime($date->getTimestamp() * 1000);
    }
    
    if (is_string($date)) {
        $dateTime = new DateTime($date);
        return new MongoDB\BSON\UTCDateTime($dateTime->getTimestamp() * 1000);
    }
    
    if (is_int($date)) {
        return new MongoDB\BSON\UTCDateTime($date * 1000);
    }
    
    return new MongoDB\BSON\UTCDateTime();
}

function mongoDateToString($mongoDate, $format = 'Y-m-d H:i:s') {
    if ($mongoDate instanceof MongoDB\BSON\UTCDateTime) {
        return $mongoDate->toDateTime()->setTimezone(new DateTimeZone(APP_TIMEZONE))->format($format);
    }
    return $mongoDate;
}

// Initialize collections and indexes
function initializeMongoCollections() {
    global $db;
    
    try {
        // Users collection indexes
        $usersCollection = $db->selectCollection(COLLECTION_USERS);
        $usersCollection->createIndex(['username' => 1], ['unique' => true]);
        $usersCollection->createIndex(['email' => 1]);
        $usersCollection->createIndex(['role' => 1]);
        $usersCollection->createIndex(['status' => 1]);
        $usersCollection->createIndex(['created_date' => -1]);
        
        // Assets collection indexes
        $assetsCollection = $db->selectCollection(COLLECTION_ASSETS);
        $assetsCollection->createIndex(['asset_id' => 1]);
        $assetsCollection->createIndex(['tag' => 1]);
        $assetsCollection->createIndex(['type' => 1]);
        $assetsCollection->createIndex(['brand' => 1]);
        $assetsCollection->createIndex(['model' => 1]);
        $assetsCollection->createIndex(['serial_number' => 1]);
        $assetsCollection->createIndex(['status' => 1]);
        $assetsCollection->createIndex(['department' => 1]);
        $assetsCollection->createIndex(['created_date' => -1]);
        $assetsCollection->createIndex(['warranty_expire' => 1]);
        
        // Text search index
        $assetsCollection->createIndex([
            'type' => 'text',
            'brand' => 'text',
            'model' => 'text',
            'hostname' => 'text',
            'description' => 'text'
        ]);
        
        // Asset logs collection indexes
        $logsCollection = $db->selectCollection(COLLECTION_ASSET_LOGS);
        $logsCollection->createIndex(['asset_id' => 1]);
        $logsCollection->createIndex(['action' => 1]);
        $logsCollection->createIndex(['changed_by' => 1]);
        $logsCollection->createIndex(['changed_date' => -1]);
        
        // Sessions collection indexes
        $sessionsCollection = $db->selectCollection(COLLECTION_SESSIONS);
        $sessionsCollection->createIndex(['session_id' => 1], ['unique' => true]);
        $sessionsCollection->createIndex(['user_id' => 1]);
        $sessionsCollection->createIndex(['expires_at' => 1], ['expireAfterSeconds' => 0]);
        
        return true;
        
    } catch (Exception $e) {
        error_log("MongoDB initialization error: " . $e->getMessage());
        return false;
    }
}

// Create default admin user
function createDefaultMongoUser() {
    global $db;
    
    try {
        $usersCollection = $db->selectCollection(COLLECTION_USERS);
        
        // ตรวจสอบว่ามี admin user อยู่แล้วหรือไม่
        $existingAdmin = $usersCollection->findOne(['username' => 'admin']);
        
        if (!$existingAdmin) {
            $adminUser = [
                '_id' => createMongoId(),
                'username' => 'admin',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'full_name' => 'Administrator',
                'email' => '<EMAIL>',
                'role' => 'Admin',
                'status' => 'Active',
                'created_date' => createMongoDate(),
                'updated_date' => createMongoDate()
            ];
            
            $result = $usersCollection->insertOne($adminUser);
            return $result->getInsertedCount() > 0;
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Create default user error: " . $e->getMessage());
        return false;
    }
}

// Pagination helper
function createMongoPagination($collection, $filter = [], $options = []) {
    $page = $options['page'] ?? 1;
    $limit = $options['limit'] ?? 20;
    $sort = $options['sort'] ?? ['_id' => -1];
    
    $skip = ($page - 1) * $limit;
    
    $totalCount = $collection->countDocuments($filter);
    $totalPages = ceil($totalCount / $limit);
    
    $cursor = $collection->find($filter, [
        'skip' => $skip,
        'limit' => $limit,
        'sort' => $sort
    ]);
    
    return [
        'data' => $cursor->toArray(),
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_count' => $totalCount,
            'limit' => $limit,
            'has_next' => $page < $totalPages,
            'has_prev' => $page > 1
        ]
    ];
}

// Search helper
function createMongoTextSearch($collection, $searchText, $filter = [], $options = []) {
    if (!empty($searchText)) {
        $filter['$text'] = ['$search' => $searchText];
        $options['sort'] = ['score' => ['$meta' => 'textScore']];
    }
    
    return createMongoPagination($collection, $filter, $options);
}

// Auto-initialize on include
try {
    initializeMongoCollections();
    createDefaultMongoUser();
} catch (Exception $e) {
    error_log("MongoDB auto-initialization error: " . $e->getMessage());
}
?>
