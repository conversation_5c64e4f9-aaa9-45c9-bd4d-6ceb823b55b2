<?php
/**
 * MongoDB Windows Installation Guide
 * Asset Management System - Windows Installation Helper
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='th'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>ติดตั้ง MongoDB Extension - Windows</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; background: #f5f5f5; line-height: 1.6; }
        .header { background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%); color: white; padding: 30px; border-radius: 15px; text-align: center; margin-bottom: 30px; }
        .install-step { background: white; border-radius: 10px; padding: 25px; margin: 20px 0; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border-left: 5px solid #0078d4; }
        .status { padding: 12px 20px; border-radius: 8px; margin: 15px 0; font-weight: bold; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn { display: inline-block; padding: 12px 24px; background: #0078d4; color: white; text-decoration: none; border-radius: 8px; margin: 5px; transition: all 0.3s; border: none; cursor: pointer; font-size: 16px; }
        .btn:hover { background: #106ebe; transform: translateY(-2px); }
        .btn.success { background: #28a745; }
        .btn.danger { background: #dc3545; }
        .code { background: #f8f9fa; padding: 20px; border-radius: 8px; font-family: 'Courier New', monospace; margin: 15px 0; border-left: 4px solid #0078d4; overflow-x: auto; white-space: pre-wrap; }
        .file-path { background: #e3f2fd; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; border: 1px solid #bbdefb; }
        .step-number { background: #0078d4; color: white; border-radius: 50%; width: 30px; height: 30px; display: inline-flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold; }
        .checklist { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0; }
        .checklist ul { list-style: none; padding: 0; }
        .checklist li { padding: 8px 0; }
        .checklist li:before { content: '☐'; margin-right: 10px; font-size: 18px; }
        .checklist li.checked:before { content: '✅'; }
    </style>
</head>
<body>";

echo "<div class='header'>
    <h1>🪟 ติดตั้ง MongoDB Extension - Windows</h1>
    <p>คู่มือการติดตั้งจากไฟล์ที่ดาวน์โหลดแล้ว</p>
    <p><strong>ไฟล์:</strong> C:\\Users\\<USER>\\Downloads\\mongodb-2.1.0</p>
</div>";

// System information
$phpVersion = phpversion();
$phpArch = (PHP_INT_SIZE === 8) ? 'x64' : 'x86';
$isThreadSafe = (defined('ZEND_THREAD_SAFE') && ZEND_THREAD_SAFE) ? 'TS' : 'NTS';
$extensionDir = ini_get('extension_dir');
$phpIniFile = php_ini_loaded_file();

echo "<div class='install-step'>
<h2>📊 ข้อมูลระบบปัจจุบัน</h2>
<div class='status info'>
<p><strong>PHP Version:</strong> $phpVersion</p>
<p><strong>Architecture:</strong> $phpArch</p>
<p><strong>Thread Safety:</strong> $isThreadSafe</p>
<p><strong>Extension Directory:</strong> $extensionDir</p>
<p><strong>PHP.ini File:</strong> $phpIniFile</p>
</div>";

// Check if already installed
if (extension_loaded('mongodb')) {
    echo "<div class='status success'>
    <h3>✅ MongoDB Extension Already Installed</h3>
    <p>Version: " . phpversion('mongodb') . "</p>
    </div>";
} else {
    echo "<div class='status warning'>
    <h3>⚠️ MongoDB Extension Not Found</h3>
    <p>ต้องติดตั้ง MongoDB PHP Extension</p>
    </div>";
}

echo "</div>";

// Installation steps
echo "<div class='install-step'>
<h2><span class='step-number'>1</span>แตกไฟล์ที่ดาวน์โหลด</h2>
<p>แตกไฟล์ <strong>mongodb-2.1.0</strong> ที่อยู่ใน Downloads folder</p>

<div class='file-path'>
C:\\Users\\<USER>\\Downloads\\mongodb-2.1.0
</div>

<div class='status info'>
<h4>📁 ไฟล์ที่ควรมีหลังแตก:</h4>
<ul>
<li>php_mongodb.dll (สำหรับ PHP extension)</li>
<li>README หรือ documentation files</li>
<li>อาจมีไฟล์เวอร์ชันต่างๆ สำหรับ PHP versions ต่างกัน</li>
</ul>
</div>

<div class='code'>
# ตรวจสอบไฟล์ที่แตกออกมา
# ควรมีไฟล์ .dll ที่ตรงกับระบบของคุณ:
# - PHP $phpVersion
# - $phpArch architecture  
# - $isThreadSafe (Thread Safe/Non Thread Safe)
</div>
</div>";

echo "<div class='install-step'>
<h2><span class='step-number'>2</span>เลือกไฟล์ DLL ที่ถูกต้อง</h2>
<p>เลือกไฟล์ <strong>php_mongodb.dll</strong> ที่ตรงกับระบบของคุณ</p>

<div class='status warning'>
<h4>⚠️ สำคัญ: ต้องเลือกไฟล์ที่ตรงกับ:</h4>
<ul>
<li><strong>PHP Version:</strong> $phpVersion</li>
<li><strong>Architecture:</strong> $phpArch</li>
<li><strong>Thread Safety:</strong> $isThreadSafe</li>
</ul>
</div>

<div class='code'>
# ตัวอย่างชื่อไฟล์ที่ถูกต้อง:
php_mongodb-2.1.0-8.2-$isThreadSafe-$phpArch.dll

# หรือ
php_mongodb.dll (ในโฟลเดอร์ที่ตรงกับเวอร์ชัน)
</div>
</div>";

echo "<div class='install-step'>
<h2><span class='step-number'>3</span>คัดลอกไฟล์ไปยัง Extension Directory</h2>
<p>คัดลอกไฟล์ <strong>php_mongodb.dll</strong> ไปยังโฟลเดอร์ extensions ของ PHP</p>

<div class='file-path'>
<strong>จาก:</strong> C:\\Users\\<USER>\\Downloads\\mongodb-2.1.0\\[เลือกไฟล์ที่ถูกต้อง]\\php_mongodb.dll
</div>

<div class='file-path'>
<strong>ไปยัง:</strong> $extensionDir\\php_mongodb.dll
</div>

<div class='status info'>
<h4>💡 วิธีคัดลอก:</h4>
<ol>
<li>เปิด File Explorer</li>
<li>ไปที่ C:\\Users\\<USER>\\Downloads\\mongodb-2.1.0</li>
<li>หาไฟล์ php_mongodb.dll ที่ตรงกับระบบ</li>
<li>คัดลอกไฟล์</li>
<li>ไปที่ $extensionDir</li>
<li>วางไฟล์</li>
</ol>
</div>
</div>";

echo "<div class='install-step'>
<h2><span class='step-number'>4</span>แก้ไขไฟล์ php.ini</h2>
<p>เพิ่มการโหลด MongoDB extension ในไฟล์ php.ini</p>

<div class='file-path'>
<strong>ไฟล์:</strong> $phpIniFile
</div>

<div class='code'>
# เปิดไฟล์ php.ini ด้วย Notepad หรือ Text Editor
# ค้นหาส่วน [Extensions] หรือบรรทัดที่มี extension=
# เพิ่มบรรทัดนี้:

extension=mongodb

# หรือถ้าต้องการระบุ path เต็ม:
extension=$extensionDir\\php_mongodb.dll
</div>

<div class='status warning'>
<h4>⚠️ หมายเหตุ:</h4>
<ul>
<li>ใช้ Notepad++ หรือ Text Editor ที่รองรับ UTF-8</li>
<li>อย่าใช้ Microsoft Word</li>
<li>บันทึกไฟล์เป็น UTF-8 encoding</li>
<li>ตรวจสอบว่าไม่มี ; หน้าบรรทัด extension=mongodb</li>
</ul>
</div>
</div>";

echo "<div class='install-step'>
<h2><span class='step-number'>5</span>รีสตาร์ท Web Server</h2>
<p>รีสตาร์ท Apache/Nginx เพื่อให้การเปลี่ยนแปลงมีผล</p>

<div class='status info'>
<h4>🔄 วิธีรีสตาร์ท:</h4>
</div>

<div class='code'>
# XAMPP:
1. เปิด XAMPP Control Panel
2. คลิก Stop ที่ Apache
3. รอสักครู่
4. คลิก Start ที่ Apache

# หรือใช้ Command Line:
net stop Apache2.4
net start Apache2.4

# หรือรีสตาร์ทเครื่องทั้งเครื่อง
</div>
</div>";

echo "<div class='install-step'>
<h2><span class='step-number'>6</span>ตรวจสอบการติดตั้ง</h2>
<p>ตรวจสอบว่า MongoDB extension ถูกโหลดแล้ว</p>";

// Check installation
if (isset($_GET['check'])) {
    echo "<div class='status info'>🔍 กำลังตรวจสอบ...</div>";
    
    if (extension_loaded('mongodb')) {
        echo "<div class='status success'>
        <h3>🎉 ติดตั้งสำเร็จ!</h3>
        <p><strong>MongoDB Extension Version:</strong> " . phpversion('mongodb') . "</p>
        <p>Extension พร้อมใช้งานแล้ว</p>
        </div>";
        
        echo "<p><a href='mongodb_test.php' class='btn success'>🧪 ทดสอบระบบ MongoDB</a></p>";
        echo "<p><a href='mongodb_migration.php' class='btn'>🔄 เริ่ม Migration</a></p>";
    } else {
        echo "<div class='status error'>
        <h3>❌ ติดตั้งไม่สำเร็จ</h3>
        <p>MongoDB extension ยังไม่ถูกโหลด</p>
        </div>";
        
        echo "<div class='status warning'>
        <h4>🔧 แก้ไขปัญหา:</h4>
        <ul>
        <li>ตรวจสอบว่าไฟล์ php_mongodb.dll อยู่ในโฟลเดอร์ที่ถูกต้อง</li>
        <li>ตรวจสอบว่าเพิ่ม extension=mongodb ใน php.ini แล้ว</li>
        <li>ตรวจสอบว่ารีสตาร์ท Apache แล้ว</li>
        <li>ตรวจสอบ PHP error log</li>
        </ul>
        </div>";
    }
} else {
    echo "<div class='code'>
# วิธีตรวจสอบ:

1. ผ่านเว็บไซต์นี้:
</div>
<p><a href='?check=1' class='btn'>🔍 ตรวจสอบการติดตั้ง</a></p>

<div class='code'>
2. ผ่าน Command Line:
cd C:\\xampp\\php
php -m | findstr mongodb

3. ผ่าน phpinfo():
สร้างไฟล์ test.php:
&lt;?php phpinfo(); ?&gt;
แล้วค้นหา \"mongodb\"
</div>";
}

echo "</div>";

// Troubleshooting
echo "<div class='install-step'>
<h2>🔧 แก้ไขปัญหาที่พบบ่อย</h2>

<div class='status error'>
<h4>❌ ปัญหา: Extension ไม่โหลด</h4>
<ul>
<li><strong>สาเหตุ:</strong> ไฟล์ DLL ไม่ตรงกับ PHP version</li>
<li><strong>แก้ไข:</strong> ดาวน์โหลดไฟล์ที่ตรงกับ PHP $phpVersion $phpArch $isThreadSafe</li>
</ul>
</div>

<div class='status error'>
<h4>❌ ปัญหา: Apache ไม่เริ่มต้น</h4>
<ul>
<li><strong>สาเหตุ:</strong> ไฟล์ DLL เสียหายหรือไม่ถูกต้อง</li>
<li><strong>แก้ไข:</strong> ลบไฟล์ php_mongodb.dll และลองใหม่</li>
</ul>
</div>

<div class='status error'>
<h4>❌ ปัญหา: ไม่พบไฟล์ php.ini</h4>
<ul>
<li><strong>แก้ไข:</strong> รันคำสั่ง <code>php --ini</code> เพื่อหา path</li>
<li><strong>หรือ:</strong> สร้างไฟล์ phpinfo() เพื่อดูข้อมูล</li>
</ul>
</div>

<div class='code'>
# ตรวจสอบ PHP Error Log:
# ดูที่ XAMPP\\apache\\logs\\error.log
# หรือ XAMPP\\php\\logs\\php_error_log

# คำสั่งที่มีประโยชน์:
php --version
php --ini
php -m
php -i | findstr mongodb
</div>
</div>";

// Checklist
echo "<div class='install-step'>
<h2>✅ Checklist การติดตั้ง</h2>
<div class='checklist'>
<ul>
<li>แตกไฟล์ mongodb-2.1.0 แล้ว</li>
<li>เลือกไฟล์ DLL ที่ตรงกับระบบ ($phpVersion $phpArch $isThreadSafe)</li>
<li>คัดลอกไฟล์ php_mongodb.dll ไปยัง $extensionDir</li>
<li>เพิ่ม extension=mongodb ใน php.ini</li>
<li>รีสตาร์ท Apache/Web Server</li>
<li>ตรวจสอบว่า extension โหลดแล้ว</li>
</ul>
</div>
</div>";

// Quick links
echo "<div class='install-step'>
<h2>🚀 ขั้นตอนถัดไป</h2>
<p><a href='?check=1' class='btn'>🔍 ตรวจสอบการติดตั้ง</a></p>
<p><a href='mongodb_setup_check.php' class='btn'>📋 Setup Check</a></p>
<p><a href='mongodb_test.php' class='btn'>🧪 ทดสอบระบบ</a></p>
<p><a href='mongodb_migration.php' class='btn'>🔄 Migration Tool</a></p>
<p><a href='login_mongodb.php' class='btn success'>🔑 เข้าสู่ระบบ</a></p>
</div>";

echo "</body></html>";
?>
