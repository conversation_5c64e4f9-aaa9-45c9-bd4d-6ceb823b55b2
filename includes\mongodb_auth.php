<?php
/**
 * MongoDB Authentication System
 * Asset Management System - MongoDB Version
 */

require_once 'mongodb_config.php';
require_once 'mongodb_manager.php';

class MongoAuth {
    private $userManager;
    private $sessionManager;
    
    public function __construct() {
        $this->userManager = new MongoUserManager();
        $this->sessionManager = new MongoSessionManager();
        
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Clean expired sessions
        $this->sessionManager->cleanExpiredSessions();
    }
    
    /**
     * Login user
     */
    public function login($username, $password, $rememberMe = false) {
        try {
            // Authenticate user
            $user = $this->userManager->authenticate($username, $password);
            
            if (!$user) {
                return ['success' => false, 'message' => 'Invalid username or password'];
            }
            
            // Check if user is active
            if ($user['status'] !== 'Active') {
                return ['success' => false, 'message' => 'Account is not active'];
            }
            
            // Create session
            $sessionData = [
                'username' => $user['username'],
                'full_name' => $user['full_name'],
                'email' => $user['email'],
                'role' => $user['role'],
                'login_time' => new DateTime(),
                'last_activity' => new DateTime()
            ];
            
            $sessionId = $this->sessionManager->createSession($user['_id'], $sessionData);
            
            // Set PHP session variables
            $_SESSION['user_id'] = mongoIdToString($user['_id']);
            $_SESSION['username'] = $user['username'];
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['mongo_session_id'] = $sessionId;
            $_SESSION['login_time'] = time();
            $_SESSION['last_activity'] = time();
            
            // Set remember me cookie if requested
            if ($rememberMe) {
                $cookieValue = base64_encode(json_encode([
                    'user_id' => mongoIdToString($user['_id']),
                    'session_id' => $sessionId,
                    'token' => hash('sha256', $sessionId . $user['username'])
                ]));
                
                setcookie('remember_me', $cookieValue, time() + (30 * 24 * 60 * 60), '/'); // 30 days
            }
            
            // Update last login
            $this->userManager->update($user['_id'], [
                'last_login' => createMongoDate(),
                'last_ip' => $_SERVER['REMOTE_ADDR'] ?? null
            ]);
            
            return [
                'success' => true,
                'message' => 'Login successful',
                'user' => [
                    'id' => mongoIdToString($user['_id']),
                    'username' => $user['username'],
                    'full_name' => $user['full_name'],
                    'role' => $user['role']
                ]
            ];
            
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Login failed. Please try again.'];
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        try {
            // Delete MongoDB session
            if (isset($_SESSION['mongo_session_id'])) {
                $this->sessionManager->deleteSession($_SESSION['mongo_session_id']);
            }
            
            // Clear remember me cookie
            if (isset($_COOKIE['remember_me'])) {
                setcookie('remember_me', '', time() - 3600, '/');
            }
            
            // Destroy PHP session
            session_destroy();
            
            return ['success' => true, 'message' => 'Logout successful'];
            
        } catch (Exception $e) {
            error_log("Logout error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Logout failed'];
        }
    }
    
    /**
     * Check if user is logged in
     */
    public function isLoggedIn() {
        // Check PHP session
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['mongo_session_id'])) {
            return false;
        }
        
        // Check MongoDB session
        try {
            $session = $this->sessionManager->getSession($_SESSION['mongo_session_id']);
            if (!$session) {
                return false;
            }
            
            // Update last activity
            $this->updateLastActivity();
            
            return true;
            
        } catch (Exception $e) {
            error_log("Session check error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get current user
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        try {
            $userId = createMongoId($_SESSION['user_id']);
            return $this->userManager->findById($userId);
        } catch (Exception $e) {
            error_log("Get current user error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Check if user has role
     */
    public function hasRole($role) {
        return isset($_SESSION['role']) && $_SESSION['role'] === $role;
    }
    
    /**
     * Check if user is admin
     */
    public function isAdmin() {
        return $this->hasRole('Admin');
    }
    
    /**
     * Require login
     */
    public function requireLogin() {
        if (!$this->isLoggedIn()) {
            header('Location: login_mongodb.php');
            exit;
        }
    }
    
    /**
     * Require admin role
     */
    public function requireAdmin() {
        $this->requireLogin();
        
        if (!$this->isAdmin()) {
            header('Location: access_denied.php');
            exit;
        }
    }
    
    /**
     * Update last activity
     */
    private function updateLastActivity() {
        try {
            if (isset($_SESSION['mongo_session_id'])) {
                $sessionData = [
                    'last_activity' => new DateTime(),
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
                ];
                
                $this->sessionManager->updateSession($_SESSION['mongo_session_id'], $sessionData);
                $_SESSION['last_activity'] = time();
            }
        } catch (Exception $e) {
            error_log("Update last activity error: " . $e->getMessage());
        }
    }
    
    /**
     * Check remember me cookie
     */
    public function checkRememberMe() {
        if (!isset($_COOKIE['remember_me'])) {
            return false;
        }
        
        try {
            $cookieData = json_decode(base64_decode($_COOKIE['remember_me']), true);
            
            if (!$cookieData || !isset($cookieData['user_id'], $cookieData['session_id'], $cookieData['token'])) {
                return false;
            }
            
            // Verify session exists
            $session = $this->sessionManager->getSession($cookieData['session_id']);
            if (!$session) {
                return false;
            }
            
            // Get user
            $user = $this->userManager->findById(createMongoId($cookieData['user_id']));
            if (!$user || $user['status'] !== 'Active') {
                return false;
            }
            
            // Verify token
            $expectedToken = hash('sha256', $cookieData['session_id'] . $user['username']);
            if (!hash_equals($expectedToken, $cookieData['token'])) {
                return false;
            }
            
            // Auto login
            $_SESSION['user_id'] = mongoIdToString($user['_id']);
            $_SESSION['username'] = $user['username'];
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['mongo_session_id'] = $cookieData['session_id'];
            $_SESSION['login_time'] = time();
            $_SESSION['last_activity'] = time();
            
            return true;
            
        } catch (Exception $e) {
            error_log("Remember me check error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Register new user
     */
    public function register($userData) {
        try {
            // Validate required fields
            $required = ['username', 'password', 'full_name', 'email'];
            foreach ($required as $field) {
                if (empty($userData[$field])) {
                    return ['success' => false, 'message' => "Field '$field' is required"];
                }
            }
            
            // Validate email format
            if (!filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) {
                return ['success' => false, 'message' => 'Invalid email format'];
            }
            
            // Set default role
            $userData['role'] = $userData['role'] ?? 'User';
            $userData['status'] = 'Active';
            
            // Create user
            $userId = $this->userManager->createUser($userData);
            
            return [
                'success' => true,
                'message' => 'Registration successful',
                'user_id' => mongoIdToString($userId)
            ];
            
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'already exists') !== false) {
                return ['success' => false, 'message' => $e->getMessage()];
            }
            
            error_log("Registration error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Registration failed. Please try again.'];
        }
    }
    
    /**
     * Change password
     */
    public function changePassword($currentPassword, $newPassword) {
        if (!$this->isLoggedIn()) {
            return ['success' => false, 'message' => 'Not logged in'];
        }
        
        try {
            $user = $this->getCurrentUser();
            if (!$user) {
                return ['success' => false, 'message' => 'User not found'];
            }
            
            // Verify current password
            if (!password_verify($currentPassword, $user['password'])) {
                return ['success' => false, 'message' => 'Current password is incorrect'];
            }
            
            // Update password
            $updated = $this->userManager->updatePassword($user['_id'], $newPassword);
            
            if ($updated) {
                return ['success' => true, 'message' => 'Password changed successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to update password'];
            }
            
        } catch (Exception $e) {
            error_log("Change password error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Password change failed'];
        }
    }
    
    /**
     * Get user sessions
     */
    public function getUserSessions($userId) {
        try {
            $objectId = createMongoId($userId);
            $db = getMongoDatabase();
            $collection = $db->selectCollection(COLLECTION_SESSIONS);
            
            $sessions = $collection->find([
                'user_id' => $objectId,
                'expires_at' => ['$gt' => createMongoDate()]
            ], [
                'sort' => ['created_date' => -1]
            ])->toArray();
            
            return $sessions;
            
        } catch (Exception $e) {
            error_log("Get user sessions error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Revoke session
     */
    public function revokeSession($sessionId) {
        try {
            return $this->sessionManager->deleteSession($sessionId);
        } catch (Exception $e) {
            error_log("Revoke session error: " . $e->getMessage());
            return false;
        }
    }
}

// Create global auth instance
$mongoAuth = new MongoAuth();

// Check remember me on page load
if (!$mongoAuth->isLoggedIn()) {
    $mongoAuth->checkRememberMe();
}

// Helper functions
function getCurrentUserFullName() {
    return $_SESSION['full_name'] ?? $_SESSION['username'] ?? 'Unknown';
}

function isAdmin() {
    global $mongoAuth;
    return $mongoAuth->isAdmin();
}

function requireLogin() {
    global $mongoAuth;
    $mongoAuth->requireLogin();
}

function requireAdmin() {
    global $mongoAuth;
    $mongoAuth->requireAdmin();
}
?>
