# ระบบ Auto Backup SQL - Asset Management System

## ภาพรวม
ระบบ Auto Backup SQL เป็นระบบสำรองข้อมูลฐานข้อมูลอัตโนมัติสำหรับ Asset Management System ที่สามารถทำ backup ได้ทั้งแบบ manual และ automatic

## ฟีเจอร์หลัก

### 1. การสำรองข้อมูลอัตโนมัติ
- ทำ backup อัตโนมัติผ่าน Cron Job
- กำหนดเวลาได้ตามต้องการ (default: ทุกวันเวลา 02:00 น.)
- ลบไฟล์ backup เก่าอัตโนมัติ (default: เก็บไว้ 30 วัน)

### 2. การจัดการผ่านเว็บ
- สร้าง backup ด้วยตนเองผ่านเว็บ
- ดาวน์โหลดไฟล์ backup
- ลบไฟล์ backup ที่ไม่ต้องการ
- ดูสถานะและข้อมูลการ backup

### 3. ระบบ Logging
- บันทึก log การทำงานทุกครั้ง
- แยก log ตามวันที่
- ตรวจสอบข้อผิดพลาดได้ง่าย

## ไฟล์ที่เกี่ยวข้อง

```
backup_system.php     - หน้าจัดการ backup ผ่านเว็บ
auto_backup.php       - สคริปต์ทำ backup อัตโนมัติ
setup_cron.sh         - สคริปต์ตั้งค่า Cron Job
backups/              - โฟลเดอร์เก็บไฟล์ backup
logs/                 - โฟลเดอร์เก็บ log files
```

## การติดตั้งและตั้งค่า

### วิธีที่ 1: ใช้สคริปต์อัตโนมัติ (Linux/macOS)

```bash
# ให้สิทธิ์ execute
chmod +x setup_cron.sh

# รันสคริปต์ด้วยสิทธิ์ root
sudo ./setup_cron.sh
```

### วิธีที่ 2: ตั้งค่าด้วยตนเอง

#### 1. สร้างโฟลเดอร์ที่จำเป็น
```bash
mkdir -p backups logs
chmod 755 backups logs
```

#### 2. ตั้งค่า Cron Job
```bash
# เปิด crontab editor
crontab -e

# เพิ่มบรรทัดนี้ (แก้ไข path ให้ถูกต้อง)
0 2 * * * cd /path/to/your/project && php auto_backup.php >> logs/cron.log 2>&1
```

#### 3. ทดสอบการทำงาน
```bash
# ทดสอบ backup ด้วยตนเอง
php auto_backup.php

# ตรวจสอบผลลัพธ์
ls -la backups/
```

## การใช้งาน

### การจัดการผ่านเว็บ

1. **เข้าสู่ระบบด้วยสิทธิ์ Admin**
2. **ไปที่เครื่องมือ > Auto Backup SQL**
3. **ใช้งานฟีเจอร์ต่างๆ:**
   - สร้าง backup ใหม่
   - ดาวน์โหลดไฟล์ backup
   - ลบไฟล์ backup เก่า
   - ดูสถานะระบบ

### คำสั่ง Command Line

```bash
# ทำ backup ด้วยตนเอง
php auto_backup.php

# ดู cron jobs ทั้งหมด
crontab -l

# แก้ไข cron jobs
crontab -e

# ลบ cron job สำหรับ backup
crontab -l | grep -v 'auto_backup.php' | crontab -
```

## การตรวจสอบและ Monitoring

### ตรวจสอบ Log Files

```bash
# ดู log backup ล่าสุด
tail -f logs/backup_$(date +%Y-%m-%d).log

# ดู cron log
tail -f logs/cron.log

# ดู log ย้อนหลัง
ls logs/backup_*.log
```

### ตรวจสอบไฟล์ Backup

```bash
# ดูรายการไฟล์ backup
ls -la backups/

# ตรวจสอบขนาดไฟล์
du -h backups/

# ตรวจสอบไฟล์ล่าสุด
ls -lt backups/ | head -5
```

## การตั้งค่าขั้นสูง

### เปลี่ยนเวลา Backup

แก้ไข cron job เพื่อเปลี่ยนเวลา:

```bash
# ทุกวันเวลา 03:30 น.
30 3 * * * cd /path/to/project && php auto_backup.php

# ทุกวันจันทร์เวลา 01:00 น.
0 1 * * 1 cd /path/to/project && php auto_backup.php

# ทุก 6 ชั่วโมง
0 */6 * * * cd /path/to/project && php auto_backup.php
```

### เปลี่ยนระยะเวลาเก็บไฟล์

แก้ไขในไฟล์ `auto_backup.php`:

```php
// เก็บไฟล์ backup ไว้ 60 วัน
$deleted = cleanOldBackups(60);
```

### การส่งอีเมลแจ้งเตือน

แก้ไขในฟังก์ชัน `sendBackupNotification()` ในไฟล์ `auto_backup.php`:

```php
$to = '<EMAIL>';
// ยกเลิก comment บรรทัดนี้
mail($to, $subject, $message, $headers);
```

## การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

#### 1. Cron Job ไม่ทำงาน
```bash
# ตรวจสอบ cron service
sudo systemctl status cron

# ตรวจสอบ cron log
sudo tail -f /var/log/cron
```

#### 2. ไม่มีสิทธิ์เขียนไฟล์
```bash
# ตั้งค่าสิทธิ์โฟลเดอร์
chmod 755 backups logs
chown www-data:www-data backups logs
```

#### 3. PHP ไม่พบ
```bash
# หา path ของ PHP
which php

# ใช้ full path ใน cron job
0 2 * * * cd /path/to/project && /usr/bin/php auto_backup.php
```

#### 4. ฐานข้อมูลเชื่อมต่อไม่ได้
- ตรวจสอบการตั้งค่าใน `includes/config.php`
- ตรวจสอบสิทธิ์ผู้ใช้ฐานข้อมูล
- ตรวจสอบว่า MySQL service ทำงานอยู่

### การตรวจสอบระบบ

```bash
# ตรวจสอบพื้นที่ดิสก์
df -h

# ตรวจสอบ memory
free -h

# ตรวจสอบ MySQL
sudo systemctl status mysql

# ตรวจสอบ PHP
php -v
```

## ข้อควรระวัง

1. **พื้นที่ดิสก์**: ตรวจสอบพื้นที่ดิสก์เป็นประจำ
2. **สิทธิ์ไฟล์**: ตั้งค่าสิทธิ์ให้เหมาะสม
3. **ความปลอดภัย**: เก็บไฟล์ backup ในที่ปลอดภัย
4. **การทดสอบ**: ทดสอบการ restore เป็นประจำ

## การ Restore ข้อมูล

```bash
# Restore จากไฟล์ backup
mysql -u username -p database_name < backups/backup_file.sql

# หรือผ่าน phpMyAdmin
# 1. เข้า phpMyAdmin
# 2. เลือกฐานข้อมูล
# 3. ไปที่แท็บ Import
# 4. เลือกไฟล์ backup และ import
```

## การสนับสนุน

หากพบปัญหาหรือต้องการความช่วยเหลือ:

1. ตรวจสอบ log files ก่อน
2. ทดสอบการทำงานด้วยตนเอง
3. ตรวจสอบการตั้งค่าระบบ
4. ติดต่อผู้ดูแลระบบ

---

**หมายเหตุ**: ระบบนี้ออกแบบมาสำหรับ Asset Management System โดยเฉพาะ กรุณาปรับแต่งให้เหมาะสมกับสภาพแวดล้อมของคุณ
