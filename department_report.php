<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'vendor/autoload.php';

// ตรวจสอบการล็อกอิน
requireLogin();

// สร้าง AssetManager instance
$assetManager = new AssetManager($pdo);

// รับค่าแผนกที่เลือก
$selected_department = $_GET['department'] ?? '';

if (empty($selected_department)) {
    die('Error: กรุณาเลือกแผนก');
}

// ดึงข้อมูล assets ของแผนกที่เลือก
$assets = $assetManager->getAllAssets('', '', '', '', '', $selected_department);

// Create mPDF object with configuration for Thai language support
$mpdf = new \Mpdf\Mpdf([
    'mode' => 'utf-8',
    'format' => 'A4-L',
    'default_font_size' => 11,
    'default_font' => 'thsarabunnew',
    'margin_left' => 10,
    'margin_right' => 10,
    'margin_top' => 15,
    'margin_bottom' => 15,
    'margin_header' => 8,
    'margin_footer' => 8,
    'orientation' => 'L',
    'autoScriptToLang' => true,
    'autoLangToFont' => true,
]);

// Set document properties
$mpdf->SetTitle('รายงาน Asset แผนก ' . $selected_department);
$mpdf->SetAuthor('Asset Management System');
$mpdf->SetCreator('Asset Management System');

// Set page numbering
$mpdf->SetFooter('หน้า {PAGENO} จาก {nb}');

// สร้างตัวแปรสำหรับข้อมูลรายงาน
$title = 'รายงาน Asset แผนก ' . $selected_department;
$totalAssets = count($assets);

// Get current date for report
$reportDate = date('d/m/Y H:i:s');

// สร้าง HTML content
$content = '
<style>
body {
    font-family: "thsarabunnew";
    font-size: 10pt;
    line-height: 1.3;
    margin: 0;
    padding: 0;
}
.header-section {
    border-bottom: 2px solid #333;
    padding-bottom: 10px;
    margin-bottom: 15px;
}
.title {
    font-size: 18pt;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin: 0 0 5px 0;
}
.subtitle {
    font-size: 10pt;
    color: #666;
    text-align: center;
    margin: 0 0 8px 0;
}
.info-section {
    font-size: 9pt;
    color: #666;
    text-align: right;
    margin: 0;
}
.department-info {
    background-color: #f0f8ff;
    padding: 10px;
    margin: 10px 0;
    border-left: 4px solid #4682b4;
    font-size: 11pt;
}
.department-title {
    font-weight: bold;
    color: #4682b4;
    margin-bottom: 5px;
}
table {
    border-collapse: collapse;
    width: 100%;
    font-size: 8pt;
    margin-top: 10px;
}
th {
    background-color: #333;
    color: white;
    font-weight: bold;
    padding: 6px 2px;
    text-align: center;
    border: 1px solid #ccc;
    font-size: 8pt;
    white-space: nowrap;
}
td {
    padding: 4px 2px;
    border: 1px solid #ccc;
    text-align: left;
    vertical-align: top;
    font-size: 8pt;
    word-wrap: break-word;
}
tr:nth-child(even) {
    background-color: #f9f9f9;
}
.status-active { color: #28a745; font-weight: bold; }
.status-damaged { color: #dc3545; font-weight: bold; }
.status-spare { color: #ffc107; font-weight: bold; }
.summary-section {
    margin-top: 20px;
    border-top: 1px solid #ccc;
    padding-top: 15px;
}
.summary-title {
    font-size: 12pt;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin-bottom: 10px;
}
.total-box {
    background-color: #f0f0f0;
    padding: 8px;
    text-align: center;
    margin-top: 10px;
    border: 1px solid #ccc;
}
.total-text {
    font-size: 11pt;
    font-weight: bold;
    color: #333;
}
.report-footer {
    font-size: 8pt;
    color: #666;
    margin-top: 3px;
}
.signature-section {
    margin-top: 30px;
    page-break-inside: avoid;
    border-top: 2px solid #333;
    padding-top: 20px;
}
.signature-title {
    font-size: 12pt;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #ccc;
    padding-bottom: 5px;
}
.signature-grid {
    display: table;
    width: 100%;
    margin-bottom: 20px;
}
.signature-box {
    display: table-cell;
    width: 50%;
    text-align: center;
    padding: 0 20px;
    vertical-align: top;
}
.signature-label {
    font-size: 10pt;
    font-weight: bold;
    color: #333;
    margin-bottom: 40px;
}
.signature-line {
    border-bottom: 1px solid #333;
    height: 50px;
    margin: 0 30px;
    margin-bottom: 8px;
}
.signature-name {
    font-size: 9pt;
    color: #333;
    margin-bottom: 15px;
}
.signature-date {
    font-size: 9pt;
    color: #666;
}
.approval-note {
    margin-top: 20px;
    background-color: #f9f9f9;
    padding: 12px;
    border-left: 3px solid #666;
}
.note-title {
    font-size: 10pt;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
}
.note-content {
    font-size: 9pt;
    color: #666;
    line-height: 1.4;
}
</style>

<div class="header-section">
    <div class="title">' . $title . '</div>
    <div class="subtitle">Asset Management System - Department Report</div>
    <div class="info-section">
        วันที่สร้าง: ' . $reportDate . ' | ผู้สร้าง: ' . getCurrentUserFullName() . ' | จำนวนรายการ: ' . $totalAssets . ' รายการ
    </div>
</div>

<div class="department-info">
    <div class="department-title">ข้อมูลแผนก: ' . htmlspecialchars($selected_department) . '</div>
    <div>รายงานนี้แสดงรายการ Asset ทั้งหมดที่อยู่ในความรับผิดชอบของแผนก ' . htmlspecialchars($selected_department) . '</div>
</div>';

// เพิ่มตารางข้อมูล assets
$content .= '
<table>
<thead>
<tr>
    <th style="width: 5%;">ลำดับ</th>
    <th style="width: 14%;">Asset ID</th>
    <th style="width: 8%;">Tag</th>
    <th style="width: 12%;">Type</th>
    <th style="width: 18%;">Model</th>
    <th style="width: 14%;">Hostname</th>
    <th style="width: 6%;">OS</th>
    <th style="width: 14%;">S/N</th>
    <th style="width: 7%;">Status</th>
</tr>
</thead>
<tbody>';

// Helper function to truncate text
function truncateText($text, $maxLength) {
    if (mb_strlen($text, 'UTF-8') > $maxLength) {
        return mb_substr($text, 0, $maxLength - 3, 'UTF-8') . '...';
    }
    return $text;
}

if (!empty($assets)) {
    $sequenceNumber = 1;
    foreach ($assets as $asset) {
        $status = $asset['status'] ?? '';
        $statusClass = '';
        if ($status == 'ใช้งาน') {
            $statusClass = 'status-active';
        } elseif ($status == 'ชำรุด') {
            $statusClass = 'status-damaged';
        } elseif ($status == 'สำรอง') {
            $statusClass = 'status-spare';
        }
        
        $content .= '
        <tr>
            <td style="text-align: center;">' . $sequenceNumber . '</td>
            <td>' . htmlspecialchars(truncateText($asset['asset_id'] ?? '', 20)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['tag'] ?? '', 10)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['type'] ?? '', 14)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['model'] ?? '', 25)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['hostname'] ?? '', 18)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['operating_system'] ?? '', 6)) . '</td>
            <td>' . htmlspecialchars(truncateText($asset['serial_number'] ?? '', 18)) . '</td>
            <td class="' . $statusClass . '" style="text-align: center;">' . htmlspecialchars($status) . '</td>
        </tr>';
        $sequenceNumber++;
    }
} else {
    $content .= '
    <tr>
        <td colspan="9" style="text-align: center; font-style: italic; color: #999; padding: 20px;">
            ไม่พบข้อมูล Asset ในแผนก ' . htmlspecialchars($selected_department) . '
        </td>
    </tr>';
}

$content .= '
</tbody>
</table>';

// เพิ่มส่วนสรุปรายงาน
if (!empty($assets)) {
    // Count by status and type
    $statusCount = [];
    $typeCount = [];

    foreach ($assets as $asset) {
        $status = $asset['status'] ?? 'ไม่ระบุ';
        $type = $asset['type'] ?? 'ไม่ระบุ';

        $statusCount[$status] = ($statusCount[$status] ?? 0) + 1;
        $typeCount[$type] = ($typeCount[$type] ?? 0) + 1;
    }

    $content .= '
    <div class="summary-section">
        <div class="summary-title">สรุปรายงานแผนก ' . htmlspecialchars($selected_department) . '</div>

        <div style="display: table; width: 100%;">
            <div style="display: table-cell; width: 50%; vertical-align: top; padding: 0 10px;">
                <div style="font-weight: bold; color: #333; font-size: 10pt; margin-bottom: 8px; border-bottom: 1px solid #ccc; padding-bottom: 2px;">สถานะ Asset</div>';

    foreach ($statusCount as $status => $count) {
        $percentage = round(($count / count($assets)) * 100, 1);
        $statusClass = '';
        if ($status == 'ใช้งาน') {
            $statusClass = 'status-active';
        } elseif ($status == 'ชำรุด') {
            $statusClass = 'status-damaged';
        } elseif ($status == 'สำรอง') {
            $statusClass = 'status-spare';
        }

        $content .= '<div style="font-size: 9pt; margin-bottom: 3px; line-height: 1.2;"><span class="' . $statusClass . '">' . htmlspecialchars($status) . ': ' . $count . ' (' . $percentage . '%)</span></div>';
    }

    $content .= '
            </div>
            <div style="display: table-cell; width: 50%; vertical-align: top; padding: 0 10px;">
                <div style="font-weight: bold; color: #333; font-size: 10pt; margin-bottom: 8px; border-bottom: 1px solid #ccc; padding-bottom: 2px;">ประเภท Asset</div>';

    arsort($typeCount);
    $topTypes = array_slice($typeCount, 0, 8, true);
    foreach ($topTypes as $type => $count) {
        $percentage = round(($count / count($assets)) * 100, 1);
        $content .= '<div style="font-size: 9pt; margin-bottom: 3px; line-height: 1.2;">' . htmlspecialchars($type) . ': ' . $count . ' (' . $percentage . '%)</div>';
    }

    $content .= '
            </div>
        </div>

        <div class="total-box">
            <div class="total-text">รวม Asset ในแผนก ' . htmlspecialchars($selected_department) . ': ' . count($assets) . ' รายการ</div>
            <div class="report-footer">รายงานนี้สร้างขึ้นเมื่อ ' . date('d/m/Y เวลา H:i:s น.') . '</div>
        </div>
    </div>';
}

// เพิ่มส่วนลายเซ็นหัวหน้าแผนก
$content .= '
<div class="signature-section">
    <div class="signature-title">การรับทราบและอนุมัติ</div>

    <div class="signature-grid">
        <div class="signature-box">
            <div class="signature-label">ผู้จัดทำรายงาน</div>
            <div class="signature-line"></div>
            <div class="signature-name">(' . getCurrentUserFullName() . ')</div>
            <div class="signature-date">วันที่ .........................</div>
        </div>

        <div class="signature-box">
            <div class="signature-label">หัวหน้าแผนก ' . htmlspecialchars($selected_department) . '</div>
            <div class="signature-line"></div>
            <div class="signature-name">(.......................................)</div>
            <div class="signature-date">วันที่ .........................</div>
        </div>
    </div>

    <div class="approval-note">
        <div class="note-title">หมายเหตุสำหรับหัวหน้าแผนก:</div>
        <div class="note-content">
            - กรุณาตรวจสอบรายการ Asset ในความรับผิดชอบของแผนก<br>
            - หากพบ Asset ที่ไม่ได้ใช้งานหรือชำรุด กรุณาแจ้งแผนก IT<br>
            - หากมี Asset ใหม่ที่ยังไม่ได้ลงทะเบียน กรุณาแจ้งเพื่อดำเนินการเพิ่มเติม<br>
            - การลงนามในรายงานนี้ถือเป็นการรับทราบสถานะ Asset ในแผนก
        </div>
    </div>
</div>';

// Write HTML to PDF
$mpdf->WriteHTML($content);

// Output PDF
$filename = 'department_report_' . str_replace(' ', '_', $selected_department) . '_' . date('Y-m-d_H-i-s') . '.pdf';
$mpdf->Output($filename, 'D');
exit;
?>
