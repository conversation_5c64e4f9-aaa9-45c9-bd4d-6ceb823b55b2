<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// ตรวจสอบการล็อกอิน
requireLogin();

// สร้าง AssetManager instance
$assetManager = new AssetManager($pdo);

// รับค่าการกรอง (ถ้ามี)
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';

// ดึงข้อมูล assets ทั้งหมดตามเงื่อนไขการกรอง
$assets = $assetManager->getAllAssets($search, $filter_type, $filter_brand, $filter_status, $filter_os, $filter_department);

// กำหนด headers สำหรับ CSV download
header('Content-Type: text/csv; charset=UTF-8');
header('Content-Disposition: attachment; filename="assets_export_' . date('Y-m-d_H-i-s') . '.csv"');
header('Pragma: no-cache');
header('Expires: 0');

// เพิ่ม BOM สำหรับ UTF-8
echo "\xEF\xBB\xBF";

// เปิด output stream
$output = fopen('php://output', 'w');

// เขียน header ของ CSV
$headers = [
    'Asset ID',
    'Type',
    'Brand', 
    'Model',
    'Tag',
    'Department',
    'Status',
    'Hostname',
    'Operating System',
    'Serial Number',
    'Warranty Expire',
    'Description',
    'Set',
    'Date Added',
    'Person Added',
    'Date Modified',
    'Person Modified'
];

fputcsv($output, $headers);

// เขียนข้อมูล assets
foreach ($assets as $asset) {
    $row = [
        $asset['asset_id'] ?? '',
        $asset['type'] ?? '',
        $asset['brand'] ?? '',
        $asset['model'] ?? '',
        $asset['tag'] ?? '',
        $asset['department'] ?? '',
        $asset['status'] ?? '',
        $asset['hostname'] ?? '',
        $asset['operating_system'] ?? '',
        $asset['serial_number'] ?? '',
        $asset['warranty_expire'] ?? '',
        $asset['description'] ?? '',
        $asset['set_name'] ?? '',
        formatDateTime($asset['created_date'] ?? ''),
        $asset['created_by'] ?? '',
        formatDateTime($asset['updated_date'] ?? ''),
        $asset['updated_by'] ?? ''
    ];
    
    fputcsv($output, $row);
}

// ปิด output stream
fclose($output);
exit;
?>
