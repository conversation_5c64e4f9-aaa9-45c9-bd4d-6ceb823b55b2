# Asset Management System - MySQL 8.0 Setup Guide

## ภาพรวม
คู่มือนี้จะแนะนำการติดตั้งและตั้งค่า Asset Management System ให้ทำงานกับ MySQL 8.0

## ความต้องการของระบบ

### Software Requirements
- **PHP**: 7.4+ (แนะนำ 8.0+)
- **MySQL**: 8.0+ 
- **Web Server**: Apache 2.4+ หรือ Nginx 1.18+
- **Extensions**: PDO, PDO_MySQL, JSON, OpenSSL

### Hardware Requirements
- **RAM**: ขั้นต่ำ 2GB (แนะนำ 4GB+)
- **Storage**: ขั้นต่ำ 1GB สำหรับระบบ + พื้นที่สำหรับข้อมูล
- **CPU**: ขั้นต่ำ 1 Core (แนะนำ 2+ Cores)

## การติดตั้ง MySQL 8.0

### Windows (XAMPP)
1. ดาวน์โหลด XAMPP ที่มี MySQL 8.0+
2. ติดตั้งและเริ่มต้น Apache และ MySQL
3. เปิด phpMyAdmin และตรวจสอบเวอร์ชัน MySQL

### Linux (Ubuntu/Debian)
```bash
# อัปเดตระบบ
sudo apt update

# ติดตั้ง MySQL 8.0
sudo apt install mysql-server-8.0

# ตั้งค่าความปลอดภัย
sudo mysql_secure_installation

# เริ่มต้นบริการ
sudo systemctl start mysql
sudo systemctl enable mysql
```

### Linux (CentOS/RHEL)
```bash
# เพิ่ม MySQL repository
sudo dnf install mysql80-community-release

# ติดตั้ง MySQL 8.0
sudo dnf install mysql-community-server

# เริ่มต้นบริการ
sudo systemctl start mysqld
sudo systemctl enable mysqld
```

## การตั้งค่าฐานข้อมูล

### 1. สร้างผู้ใช้ฐานข้อมูล (แนะนำ)
```sql
-- เข้า MySQL ด้วย root
mysql -u root -p

-- สร้างผู้ใช้สำหรับ Asset Management
CREATE USER 'asset_user'@'localhost' IDENTIFIED BY 'secure_password_here';

-- สร้างฐานข้อมูล
CREATE DATABASE asset_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- ให้สิทธิ์
GRANT ALL PRIVILEGES ON asset_management.* TO 'asset_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. การตั้งค่าไฟล์ config
แก้ไขไฟล์ `includes/config.php`:
```php
define('DB_HOST', 'localhost');
define('DB_PORT', '3306');
define('DB_NAME', 'asset_management');
define('DB_USER', 'asset_user');        // เปลี่ยนจาก root
define('DB_PASS', 'secure_password_here'); // ใส่รหัสผ่านที่แข็งแกร่ง
```

## การติดตั้งระบบ

### วิธีที่ 1: ใช้ Migration Tool (แนะนำ)

1. **คัดลอกไฟล์ระบบ**
   ```bash
   # คัดลอกโฟลเดอร์ไปยัง web root
   cp -r asset-management /var/www/html/
   # หรือใน Windows: คัดลอกไปยัง C:\xampp\htdocs\
   ```

2. **เรียกใช้ Migration Tool**
   ```
   http://localhost/asset-management/mysql80_migration.php
   ```

3. **ดำเนินการ Full Setup**
   - คลิก "Full Setup (All Actions)"
   - รอให้กระบวนการเสร็จสิ้น
   - ตรวจสอบผลลัพธ์

### วิธีที่ 2: ติดตั้งด้วยตนเอง

1. **Import ไฟล์ SQL**
   ```bash
   mysql -u asset_user -p asset_management < sql/setup_mysql80.sql
   ```

2. **ตรวจสอบการติดตั้ง**
   ```sql
   USE asset_management;
   SHOW TABLES;
   SELECT COUNT(*) FROM users;
   ```

## การตั้งค่า MySQL 8.0 เพิ่มเติม

### 1. การปรับแต่งประสิทธิภาพ
แก้ไขไฟล์ `/etc/mysql/mysql.conf.d/mysqld.cnf` (Linux) หรือ `my.ini` (Windows):

```ini
[mysqld]
# Basic Settings
default_authentication_plugin = mysql_native_password
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# Performance Settings
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_strict_mode = ON

# Character Set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Connection Settings
max_connections = 200
connect_timeout = 60
wait_timeout = 28800

# Query Cache (disabled in MySQL 8.0)
# query_cache_type = OFF
```

### 2. การตั้งค่าความปลอดภัย
```sql
-- ปิดการใช้งาน remote root login
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');

-- ลบ anonymous users
DELETE FROM mysql.user WHERE User='';

-- ลบ test database
DROP DATABASE IF EXISTS test;
DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';

-- รีเฟรชสิทธิ์
FLUSH PRIVILEGES;
```

## การทดสอบระบบ

### 1. ทดสอบการเชื่อมต่อฐานข้อมูล
```php
// สร้างไฟล์ test_connection.php
<?php
require_once 'includes/config.php';

if (testConnection()) {
    echo "✅ Database connection successful!";
    echo "<br>MySQL Version: " . getMySQLVersion();
} else {
    echo "❌ Database connection failed!";
}
?>
```

### 2. ทดสอบการทำงานของระบบ
1. เข้าสู่ระบบด้วย: `admin` / `admin123`
2. ทดสอบเพิ่ม Asset ใหม่
3. ทดสอบการแก้ไขข้อมูล
4. ทดสอบระบบ Backup
5. ทดสอบการ Import/Export

## การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

#### 1. Authentication Plugin Error
```
ERROR 2059 (HY000): Authentication plugin 'caching_sha2_password' cannot be loaded
```

**วิธีแก้:**
```sql
ALTER USER 'asset_user'@'localhost' IDENTIFIED WITH mysql_native_password BY 'your_password';
FLUSH PRIVILEGES;
```

#### 2. Connection Timeout
```
SQLSTATE[HY000] [2002] Connection timed out
```

**วิธีแก้:**
- ตรวจสอบว่า MySQL service ทำงานอยู่
- ตรวจสอบ firewall settings
- เพิ่ม timeout ในการตั้งค่า PDO

#### 3. Character Set Issues
```
Incorrect string value for column
```

**วิธีแก้:**
```sql
ALTER DATABASE asset_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE assets CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 4. Permission Denied
```
Access denied for user 'asset_user'@'localhost'
```

**วิธีแก้:**
```sql
GRANT ALL PRIVILEGES ON asset_management.* TO 'asset_user'@'localhost';
FLUSH PRIVILEGES;
```

### การตรวจสอบ Log
```bash
# MySQL Error Log
sudo tail -f /var/log/mysql/error.log

# Apache Error Log
sudo tail -f /var/log/apache2/error.log

# PHP Error Log
sudo tail -f /var/log/php/error.log
```

## การสำรองข้อมูล

### 1. การสำรองด้วย mysqldump
```bash
# สำรองข้อมูลทั้งหมด
mysqldump -u asset_user -p asset_management > backup_$(date +%Y%m%d_%H%M%S).sql

# สำรองเฉพาะโครงสร้าง
mysqldump -u asset_user -p --no-data asset_management > structure_backup.sql

# สำรองเฉพาะข้อมูล
mysqldump -u asset_user -p --no-create-info asset_management > data_backup.sql
```

### 2. การใช้ระบบ Auto Backup
1. ตั้งค่าใน `backup_settings.php`
2. กำหนด path สำหรับเก็บไฟล์
3. ตั้งค่า Cron Job สำหรับ backup อัตโนมัติ

## การอัปเกรด

### จาก MySQL 5.7 เป็น 8.0
1. สำรองข้อมูลทั้งหมด
2. อัปเกรด MySQL เป็น 8.0
3. รัน `mysql_upgrade`
4. ทดสอบระบบ
5. อัปเดตการตั้งค่าตามคู่มือนี้

## การตรวจสอบประสิทธิภาพ

### 1. คำสั่งตรวจสอบ
```sql
-- ตรวจสอบสถานะ
SHOW STATUS LIKE 'Innodb_buffer_pool%';
SHOW STATUS LIKE 'Connections';
SHOW STATUS LIKE 'Uptime';

-- ตรวจสอบ slow queries
SHOW STATUS LIKE 'Slow_queries';

-- ตรวจสอบการใช้งาน memory
SELECT 
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB' 
FROM information_schema.tables 
WHERE table_schema='asset_management';
```

### 2. การปรับแต่ง
- เพิ่ม `innodb_buffer_pool_size` ตามขนาด RAM
- ตั้งค่า `max_connections` ตามการใช้งาน
- เปิดใช้งาน slow query log เพื่อตรวจสอบ

## การรักษาความปลอดภัย

### 1. การตั้งค่าพื้นฐาน
- ใช้รหัสผ่านที่แข็งแกร่ง
- ปิดการเข้าถึงจากภายนอกที่ไม่จำเป็น
- อัปเดต MySQL เป็นประจำ
- ใช้ SSL/TLS สำหรับการเชื่อมต่อ

### 2. การตรวจสอบความปลอดภัย
```sql
-- ตรวจสอบผู้ใช้
SELECT User, Host FROM mysql.user;

-- ตรวจสอบสิทธิ์
SHOW GRANTS FOR 'asset_user'@'localhost';

-- ตรวจสอบการเชื่อมต่อ
SHOW PROCESSLIST;
```

## การสนับสนุน

หากพบปัญหา:
1. ตรวจสอบ log files
2. ทดสอบการเชื่อมต่อฐานข้อมูล
3. ตรวจสอบการตั้งค่า PHP และ MySQL
4. ใช้ Migration Tool เพื่อตรวจสอบสถานะ

---

**หมายเหตุ**: คู่มือนี้เหมาะสำหรับ MySQL 8.0+ เท่านั้น สำหรับเวอร์ชันเก่า กรุณาใช้คู่มือการติดตั้งแบบเดิม
