<?php

$finder = PhpCsFixer\Finder::create()
    ->exclude(['vendor', 'tests/data/Calculation'])
    ->in('samples')
    ->in('src')
    ->in('tests/PhpSpreadsheetTests')
    ;

return PhpCsFixer\Config::create()
    ->setRiskyAllowed(true)
    ->setFinder($finder)
    ->setCacheFile(sys_get_temp_dir() . '/php-cs-fixer' . preg_replace('~\W~', '-', __DIR__))
    ->setRules([
        'align_multiline_comment' => true,
        'array_syntax' => ['syntax' => 'short'],
        'backtick_to_shell_exec' => true,
        'binary_operator_spaces' => true,
        'blank_line_after_namespace' => true,
        'blank_line_after_opening_tag' => true,
        'blank_line_before_statement' => true,
        'braces' => true,
        'cast_spaces' => true,
        'class_attributes_separation' => ['elements' => ['method', 'property']], // const are often grouped with other related const
        'class_definition' => true,
        'class_keyword_remove' => false, // ::class keyword gives us beter support in IDE
        'combine_consecutive_issets' => true,
        'combine_consecutive_unsets' => true,
        'compact_nullable_typehint' => true,
        'concat_space' => ['spacing' => 'one'],
        'declare_equal_normalize' => true,
        'declare_strict_types' => false, // Too early to adopt strict types
        'dir_constant' => true,
        'doctrine_annotation_array_assignment' => true,
        'doctrine_annotation_braces' => true,
        'doctrine_annotation_indentation' => true,
        'doctrine_annotation_spaces' => true,
        'elseif' => true,
        'encoding' => true,
        'ereg_to_preg' => true,
        'escape_implicit_backslashes' => true,
        'explicit_indirect_variable' => false, // I feel it makes the code actually harder to read
        'explicit_string_variable' => false, // I feel it makes the code actually harder to read
        'final_internal_class' => true,
        'full_opening_tag' => true,
        'function_declaration' => true,
        'function_to_constant' => true,
        'function_typehint_space' => true,
        'general_phpdoc_annotation_remove' => false, // No use for that
        'hash_to_slash_comment' => true,
        'header_comment' => false, // We don't use common header in all our files
        'heredoc_to_nowdoc' => false, // Not sure about this one
        'include' => true,
        'increment_style' => true,
        'indentation_type' => true,
        'is_null' => ['use_yoda_style' => false],
        'linebreak_after_opening_tag' => true,
        'line_ending' => true,
        'list_syntax' => ['syntax' => 'short'],
        'lowercase_cast' => true,
        'lowercase_constants' => true,
        'lowercase_keywords' => true,
        'magic_constant_casing' => true,
        'mb_str_functions' => false, // No, too dangerous to change that
        'method_argument_space' => true,
        'method_chaining_indentation' => true,
        'method_separation' => true,
        'modernize_types_casting' => true,
        'multiline_comment_opening_closing' => true,
        'native_function_casing' => true,
        'native_function_invocation' => false, // This is risky and seems to be micro-optimization that make code uglier so not worth it, at least for now
        'new_with_braces' => true,
        'no_alias_functions' => true,
        'no_blank_lines_after_class_opening' => true,
        'no_blank_lines_after_phpdoc' => true,
        'no_blank_lines_before_namespace' => false, // we want 1 blank line before namespace
        'no_break_comment' => true,
        'no_closing_tag' => true,
        'no_empty_comment' => true,
        'no_empty_phpdoc' => true,
        'no_empty_statement' => true,
        'no_extra_blank_lines' => true,
        'no_homoglyph_names' => true,
        'no_leading_import_slash' => true,
        'no_leading_namespace_whitespace' => true,
        'no_mixed_echo_print' => true,
        'no_multiline_whitespace_around_double_arrow' => true,
        'no_multiline_whitespace_before_semicolons' => true,
        'non_printable_character' => true,
        'no_null_property_initialization' => true,
        'no_php4_constructor' => true,
        'normalize_index_brace' => true,
        'no_short_bool_cast' => true,
        'no_short_echo_tag' => true,
        'no_singleline_whitespace_before_semicolons' => true,
        'no_spaces_after_function_name' => true,
        'no_spaces_around_offset' => true,
        'no_spaces_inside_parenthesis' => true,
        'no_superfluous_elseif' => false, // Might be risky on a huge code base
        'not_operator_with_space' => false, // No we prefer to keep '!' without spaces
        'not_operator_with_successor_space' => false, // idem
        'no_trailing_comma_in_list_call' => true,
        'no_trailing_comma_in_singleline_array' => true,
        'no_trailing_whitespace_in_comment' => true,
        'no_trailing_whitespace' => true,
        'no_unneeded_control_parentheses' => true,
        'no_unneeded_curly_braces' => true,
        'no_unneeded_final_method' => true,
        'no_unreachable_default_argument_value' => true,
        'no_unused_imports' => true,
        'no_useless_else' => true,
        'no_useless_return' => true,
        'no_whitespace_before_comma_in_array' => true,
        'no_whitespace_in_blank_line' => true,
        'object_operator_without_whitespace' => true,
        'ordered_class_elements' => false, // We prefer to keep some freedom
        'ordered_imports' => true,
        'phpdoc_add_missing_param_annotation' => true,
        'phpdoc_align' => false, // Waste of time
        'phpdoc_annotation_without_dot' => true,
        'phpdoc_indent' => true,
        'phpdoc_inline_tag' => true,
        'phpdoc_no_access' => true,
        'phpdoc_no_alias_tag' => true,
        'phpdoc_no_empty_return' => true,
        'phpdoc_no_package' => true,
        'phpdoc_no_useless_inheritdoc' => true,
        'phpdoc_order' => true,
        'phpdoc_return_self_reference' => true,
        'phpdoc_scalar' => true,
        'phpdoc_separation' => true,
        'phpdoc_single_line_var_spacing' => true,
        'phpdoc_summary' => true,
        'phpdoc_to_comment' => true,
        'phpdoc_trim' => true,
        'phpdoc_types_order' => true,
        'phpdoc_types' => true,
        'phpdoc_var_without_name' => true,
        'php_unit_construct' => true,
        'php_unit_dedicate_assert' => true,
        'php_unit_expectation' => true,
        'php_unit_fqcn_annotation' => true,
        'php_unit_mock' => true,
        'php_unit_namespaced' => true,
        'php_unit_no_expectation_annotation' => true,
        'php_unit_strict' => false, // We sometime actually need assertEquals
        'php_unit_test_annotation' => true,
        'php_unit_test_class_requires_covers' => false, // We don't care as much as we should about coverage
        'pow_to_exponentiation' => false,
        'protected_to_private' => true,
        'psr0' => true,
        'psr4' => true,
        'random_api_migration' => false, // This breaks our unit tests
        'return_type_declaration' => true,
        'self_accessor' => true,
        'semicolon_after_instruction' => false, // Buggy in `samples/index.php`
        'short_scalar_cast' => true,
        'silenced_deprecation_error' => true,
        'simplified_null_return' => false, // While technically correct we prefer to be explicit when returning null
        'single_blank_line_at_eof' => true,
        'single_blank_line_before_namespace' => true,
        'single_class_element_per_statement' => true,
        'single_import_per_statement' => true,
        'single_line_after_imports' => true,
        'single_line_comment_style' => true,
        'single_quote' => true,
        'space_after_semicolon' => true,
        'standardize_not_equals' => true,
        'static_lambda' => false, // Risky if we can't guarantee nobody use `bindTo()`
        'strict_comparison' => false, // No, too dangerous to change that
        'strict_param' => false, // No, too dangerous to change that
        'switch_case_semicolon_to_colon' => true,
        'switch_case_space' => true,
        'ternary_operator_spaces' => true,
        'ternary_to_null_coalescing' => true,
        'trailing_comma_in_multiline_array' => true,
        'trim_array_spaces' => true,
        'unary_operator_spaces' => true,
        'visibility_required' => true,
        'void_return' => false, // Cannot use that with PHP 5.6
        'whitespace_after_comma_in_array' => true,
        'yoda_style' => false,
    ]);
