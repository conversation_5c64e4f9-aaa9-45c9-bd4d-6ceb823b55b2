<?php
/**
 * Backup Configuration
 * การตั้งค่าระบบ Backup
 */

class BackupConfig {
    private static $config = [
        // การตั้งค่าพื้นฐาน
        'enabled' => true,
        'backup_path' => 'backups/',
        'log_path' => 'logs/',
        
        // การตั้งค่า Auto Backup
        'auto_backup_on_changes' => true,
        'backup_on_asset_add' => true,
        'backup_on_asset_edit' => true,
        'backup_on_asset_delete' => true,
        'backup_on_user_add' => true,
        'backup_on_user_edit' => false,
        'backup_on_user_delete' => true,
        
        // การตั้งค่าการเก็บไฟล์
        'max_backup_files' => 50,
        'keep_days' => 30,
        'compress_backups' => false,
        
        // การตั้งค่าการแจ้งเตือน
        'email_notifications' => false,
        'admin_email' => '<EMAIL>',
        
        // การตั้งค่าขั้นสูง
        'backup_prefix' => 'auto_',
        'include_logs_in_backup' => false,
        'backup_timeout' => 300, // 5 minutes
    ];
    
    private static $configFile = 'includes/backup_settings.json';
    
    public static function load() {
        if (file_exists(self::$configFile)) {
            $fileConfig = json_decode(file_get_contents(self::$configFile), true);
            if ($fileConfig) {
                self::$config = array_merge(self::$config, $fileConfig);
            }
        }
    }
    
    public static function save() {
        $dir = dirname(self::$configFile);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        return file_put_contents(self::$configFile, json_encode(self::$config, JSON_PRETTY_PRINT));
    }
    
    public static function get($key, $default = null) {
        return isset(self::$config[$key]) ? self::$config[$key] : $default;
    }
    
    public static function set($key, $value) {
        self::$config[$key] = $value;
    }
    
    public static function getAll() {
        return self::$config;
    }
    
    public static function setAll($config) {
        self::$config = array_merge(self::$config, $config);
    }
    
    public static function getBackupPath() {
        $path = self::get('backup_path', 'backups/');
        
        // ตรวจสอบว่าเป็น absolute path หรือไม่
        if (!self::isAbsolutePath($path)) {
            $path = __DIR__ . '/../' . $path;
        }
        
        // สร้างโฟลเดอร์ถ้ายังไม่มี
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        
        return rtrim($path, '/') . '/';
    }
    
    public static function getLogPath() {
        $path = self::get('log_path', 'logs/');
        
        // ตรวจสอบว่าเป็น absolute path หรือไม่
        if (!self::isAbsolutePath($path)) {
            $path = __DIR__ . '/../' . $path;
        }
        
        // สร้างโฟลเดอร์ถ้ายังไม่มี
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        
        return rtrim($path, '/') . '/';
    }
    
    private static function isAbsolutePath($path) {
        // Windows: C:\ หรือ \\
        if (PHP_OS_FAMILY === 'Windows') {
            return preg_match('/^[A-Za-z]:\\\\/', $path) || substr($path, 0, 2) === '\\\\';
        }
        // Unix/Linux: /
        return substr($path, 0, 1) === '/';
    }
    
    public static function isEnabled() {
        return self::get('enabled', true);
    }
    
    public static function shouldBackupOnChange($action) {
        if (!self::get('auto_backup_on_changes', true)) {
            return false;
        }
        
        $actionMap = [
            'asset_add' => 'backup_on_asset_add',
            'asset_edit' => 'backup_on_asset_edit',
            'asset_delete' => 'backup_on_asset_delete',
            'user_add' => 'backup_on_user_add',
            'user_edit' => 'backup_on_user_edit',
            'user_delete' => 'backup_on_user_delete',
        ];
        
        if (isset($actionMap[$action])) {
            return self::get($actionMap[$action], true);
        }
        
        return false;
    }
}

// โหลดการตั้งค่าเมื่อ include ไฟล์นี้
BackupConfig::load();
?>
