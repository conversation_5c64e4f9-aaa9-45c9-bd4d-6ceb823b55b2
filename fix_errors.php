<?php
/**
 * Fix Errors - แก้ไขข้อผิดพลาดในการประมวลผลข้อมูล
 * Asset Management System Error Fixer
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>🔧 แก้ไขข้อผิดพลาดในการประมวลผลข้อมูล</h1>";
echo "<p>Asset Management System - Error Diagnosis and Fix Tool</p>";

// สร้างฟังก์ชันช่วยเหลือ
function logMessage($message, $type = 'info') {
    $colors = [
        'success' => 'green',
        'error' => 'red', 
        'warning' => 'orange',
        'info' => 'blue'
    ];
    
    $color = $colors[$type] ?? 'black';
    $icon = [
        'success' => '✅',
        'error' => '❌',
        'warning' => '⚠️',
        'info' => 'ℹ️'
    ][$type] ?? '•';
    
    echo "<p style='color: $color;'>$icon $message</p>";
}

function checkFileExists($file) {
    if (file_exists($file)) {
        logMessage("File exists: $file", 'success');
        return true;
    } else {
        logMessage("File missing: $file", 'error');
        return false;
    }
}

// 1. ตรวจสอบไฟล์สำคัญ
echo "<h2>📁 ตรวจสอบไฟล์สำคัญ</h2>";

$criticalFiles = [
    'includes/config.php',
    'config/database.php', 
    'includes/auth.php',
    'includes/functions.php',
    'vendor/autoload.php'
];

$missingFiles = [];
foreach ($criticalFiles as $file) {
    if (!checkFileExists($file)) {
        $missingFiles[] = $file;
    }
}

// 2. ตรวจสอบการเชื่อมต่อฐานข้อมูล
echo "<h2>🗄️ ตรวจสอบการเชื่อมต่อฐานข้อมูล</h2>";

$dbConnected = false;
$pdo = null;

// ลองใช้ config ใหม่ก่อน
if (file_exists('includes/config.php')) {
    try {
        require_once 'includes/config.php';
        if (testConnection()) {
            logMessage("Database connection successful (includes/config.php)", 'success');
            $dbConnected = true;
        }
    } catch (Exception $e) {
        logMessage("Database connection failed (includes/config.php): " . $e->getMessage(), 'error');
    }
}

// ถ้าไม่ได้ ลองใช้ config เก่า
if (!$dbConnected && file_exists('config/database.php')) {
    try {
        require_once 'config/database.php';
        $database = new Database();
        $pdo = $database->getConnection();
        logMessage("Database connection successful (config/database.php)", 'success');
        $dbConnected = true;
    } catch (Exception $e) {
        logMessage("Database connection failed (config/database.php): " . $e->getMessage(), 'error');
    }
}

// ถ้ายังไม่ได้ ลองเชื่อมต่อแบบ manual
if (!$dbConnected) {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8mb4", 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        logMessage("Database connection successful (manual connection)", 'success');
        $dbConnected = true;
    } catch (Exception $e) {
        logMessage("Manual database connection failed: " . $e->getMessage(), 'error');
    }
}

// 3. ตรวจสอบโครงสร้างฐานข้อมูล
if ($dbConnected && $pdo) {
    echo "<h2>🏗️ ตรวจสอบโครงสร้างฐานข้อมูล</h2>";
    
    try {
        // ตรวจสอบตาราง
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $requiredTables = ['users', 'assets', 'asset_logs'];
        $missingTables = [];
        
        foreach ($requiredTables as $table) {
            if (in_array($table, $tables)) {
                logMessage("Table exists: $table", 'success');
            } else {
                logMessage("Table missing: $table", 'error');
                $missingTables[] = $table;
            }
        }
        
        // ตรวจสอบคอลัมน์ในตาราง assets
        if (in_array('assets', $tables)) {
            $stmt = $pdo->query("DESCRIBE assets");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $requiredColumns = [
                'id', 'asset_id', 'tag', 'type', 'brand', 'model', 
                'hostname', 'operating_system', 'serial_number', 'status', 
                'department', 'warranty_expire', 'description', 'set_name',
                'created_date', 'created_by', 'updated_date', 'updated_by'
            ];
            
            $missingColumns = [];
            foreach ($requiredColumns as $column) {
                if (in_array($column, $columns)) {
                    logMessage("Column exists: assets.$column", 'success');
                } else {
                    logMessage("Column missing: assets.$column", 'error');
                    $missingColumns[] = $column;
                }
            }
        }
        
    } catch (Exception $e) {
        logMessage("Database structure check failed: " . $e->getMessage(), 'error');
    }
}

// 4. ตรวจสอบ PHP Extensions
echo "<h2>🔧 ตรวจสอบ PHP Extensions</h2>";

$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'openssl'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        logMessage("Extension loaded: $ext", 'success');
    } else {
        logMessage("Extension missing: $ext", 'error');
        $missingExtensions[] = $ext;
    }
}

// 5. ตรวจสอบ Composer Dependencies
echo "<h2>📦 ตรวจสอบ Composer Dependencies</h2>";

if (file_exists('vendor/autoload.php')) {
    logMessage("Composer autoloader exists", 'success');
    
    try {
        require_once 'vendor/autoload.php';
        logMessage("Composer autoloader loaded successfully", 'success');
        
        // ตรวจสอบ libraries สำคัญ
        if (class_exists('Mpdf\Mpdf')) {
            logMessage("mPDF library available", 'success');
        } else {
            logMessage("mPDF library missing", 'error');
        }
        
        if (class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
            logMessage("PhpSpreadsheet library available", 'success');
        } else {
            logMessage("PhpSpreadsheet library missing", 'error');
        }
        
    } catch (Exception $e) {
        logMessage("Composer autoloader error: " . $e->getMessage(), 'error');
    }
} else {
    logMessage("Composer autoloader missing - run 'composer install'", 'error');
}

// 6. ตรวจสอบสิทธิ์ไฟล์
echo "<h2>🔐 ตรวจสอบสิทธิ์ไฟล์</h2>";

$writableDirs = ['backups', 'logs', 'uploads'];
foreach ($writableDirs as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            logMessage("Created directory: $dir", 'success');
        } else {
            logMessage("Failed to create directory: $dir", 'error');
        }
    }
    
    if (is_writable($dir)) {
        logMessage("Directory writable: $dir", 'success');
    } else {
        logMessage("Directory not writable: $dir", 'error');
    }
}

// 7. ตรวจสอบ Session
echo "<h2>🔑 ตรวจสอบ Session</h2>";

if (session_status() === PHP_SESSION_NONE) {
    if (session_start()) {
        logMessage("Session started successfully", 'success');
    } else {
        logMessage("Failed to start session", 'error');
    }
} else {
    logMessage("Session already active", 'success');
}

// 8. แสดงสรุปปัญหาและวิธีแก้ไข
echo "<h2>📋 สรุปปัญหาและวิธีแก้ไข</h2>";

if (!empty($missingFiles)) {
    echo "<h3 style='color: red;'>ไฟล์ที่หายไป:</h3>";
    echo "<ul>";
    foreach ($missingFiles as $file) {
        echo "<li>$file</li>";
    }
    echo "</ul>";
    echo "<p><strong>วิธีแก้:</strong> ตรวจสอบว่าไฟล์ถูกอัปโหลดครบถ้วน</p>";
}

if (!empty($missingExtensions)) {
    echo "<h3 style='color: red;'>PHP Extensions ที่หายไป:</h3>";
    echo "<ul>";
    foreach ($missingExtensions as $ext) {
        echo "<li>$ext</li>";
    }
    echo "</ul>";
    echo "<p><strong>วิธีแก้:</strong> ติดตั้ง PHP extensions ที่จำเป็น</p>";
}

if (!$dbConnected) {
    echo "<h3 style='color: red;'>ปัญหาการเชื่อมต่อฐานข้อมูล:</h3>";
    echo "<p><strong>วิธีแก้:</strong></p>";
    echo "<ol>";
    echo "<li>ตรวจสอบว่า MySQL service ทำงานอยู่</li>";
    echo "<li>ตรวจสอบ username/password ในไฟล์ config</li>";
    echo "<li>ตรวจสอบว่าฐานข้อมูล 'asset_management' มีอยู่</li>";
    echo "<li>รัน mysql80_migration.php เพื่อสร้างฐานข้อมูล</li>";
    echo "</ol>";
}

// 9. ปุ่มแก้ไขอัตโนมัติ
echo "<h2>🚀 แก้ไขอัตโนมัติ</h2>";

if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'fix_database':
            if ($dbConnected && $pdo) {
                echo "<h3>กำลังแก้ไขฐานข้อมูล...</h3>";
                
                // สร้างตารางที่หายไป
                if (!empty($missingTables)) {
                    $sqlFile = 'sql/setup_mysql80.sql';
                    if (file_exists($sqlFile)) {
                        try {
                            $sql = file_get_contents($sqlFile);
                            $statements = explode(';', $sql);
                            
                            foreach ($statements as $statement) {
                                $statement = trim($statement);
                                if (!empty($statement) && !preg_match('/^(CREATE DATABASE|USE|SET|DELIMITER)/i', $statement)) {
                                    try {
                                        $pdo->exec($statement);
                                    } catch (PDOException $e) {
                                        // ข้าม error ถ้าตารางมีอยู่แล้ว
                                        if (!preg_match('/(already exists|duplicate)/i', $e->getMessage())) {
                                            logMessage("SQL Error: " . $e->getMessage(), 'warning');
                                        }
                                    }
                                }
                            }
                            logMessage("Database structure fixed", 'success');
                        } catch (Exception $e) {
                            logMessage("Failed to fix database: " . $e->getMessage(), 'error');
                        }
                    }
                }
            }
            break;
            
        case 'fix_permissions':
            echo "<h3>กำลังแก้ไขสิทธิ์ไฟล์...</h3>";
            
            $dirs = ['backups', 'logs', 'uploads'];
            foreach ($dirs as $dir) {
                if (!is_dir($dir)) {
                    mkdir($dir, 0755, true);
                }
                chmod($dir, 0755);
                logMessage("Fixed permissions for: $dir", 'success');
            }
            break;
            
        case 'create_config':
            echo "<h3>กำลังสร้างไฟล์ config...</h3>";
            
            if (!file_exists('includes/config.php')) {
                $configContent = file_get_contents('includes/config.php.example');
                if ($configContent && file_put_contents('includes/config.php', $configContent)) {
                    logMessage("Created includes/config.php", 'success');
                } else {
                    logMessage("Failed to create includes/config.php", 'error');
                }
            }
            break;
    }
} else {
    echo "<p><a href='?action=fix_database' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>แก้ไขฐานข้อมูล</a></p>";
    echo "<p><a href='?action=fix_permissions' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>แก้ไขสิทธิ์ไฟล์</a></p>";
    echo "<p><a href='mysql80_migration.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>รัน Migration Tool</a></p>";
}

// 10. ข้อมูลระบบ
echo "<h2>💻 ข้อมูลระบบ</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Item</th><th>Value</th></tr>";
echo "<tr><td>PHP Version</td><td>" . phpversion() . "</td></tr>";
echo "<tr><td>Operating System</td><td>" . php_uname() . "</td></tr>";
echo "<tr><td>Server Software</td><td>" . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>Document Root</td><td>" . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>Current Directory</td><td>" . getcwd() . "</td></tr>";
echo "<tr><td>Memory Limit</td><td>" . ini_get('memory_limit') . "</td></tr>";
echo "<tr><td>Max Execution Time</td><td>" . ini_get('max_execution_time') . "</td></tr>";
echo "<tr><td>Error Reporting</td><td>" . error_reporting() . "</td></tr>";
echo "</table>";

echo "<div style='background: #e9ecef; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>📝 คำแนะนำเพิ่มเติม</h3>";
echo "<ul>";
echo "<li>หากยังมีปัญหา ให้ตรวจสอบ error log ของ web server</li>";
echo "<li>ตรวจสอบว่า MySQL service ทำงานอยู่</li>";
echo "<li>ตรวจสอบ firewall settings</li>";
echo "<li>ลองรีสตาร์ท web server และ MySQL</li>";
echo "<li>ตรวจสอบ PHP error log</li>";
echo "</ul>";
echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

table {
    background: white;
    margin: 10px 0;
}

th, td {
    padding: 8px 12px;
    text-align: left;
}

th {
    background: #f8f9fa;
}

a {
    display: inline-block;
    margin: 5px 0;
}
</style>
