<?php
/**
 * MongoDB Main Dashboard
 * Asset Management System - MongoDB Version
 */

require_once 'includes/mongodb_auth.php';

// Require login
requireLogin();

// Get current user
$currentUser = $mongoAuth->getCurrentUser();

// Get statistics
try {
    $assetStats = $mongoAssetManager->getAssetStatistics();
    $totalUsers = $mongoUserManager->count(['status' => 'Active']);
    $recentLogs = $mongoAssetLogManager->getRecentLogs(10);
    $expiringWarranties = $mongoAssetManager->getExpiringWarranties(90);
} catch (Exception $e) {
    $error = "Error loading dashboard data: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>หน้าหลัก - Asset Management (MongoDB)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'TH Sarabun New', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo h1 {
            font-size: 28px;
            font-weight: 600;
        }

        .mongodb-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .nav-menu {
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 15px 0;
        }

        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            gap: 30px;
        }

        .nav-item {
            color: #333;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 8px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .nav-item:hover {
            background: #e9ecef;
            color: #28a745;
        }

        .nav-item.active {
            background: #28a745;
            color: white;
        }

        .main-content {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #28a745;
            transition: transform 0.3s;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card h3 {
            color: #333;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }

        .content-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .content-card h3 {
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .log-item {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .log-item:last-child {
            border-bottom: none;
        }

        .log-action {
            font-weight: 600;
            color: #333;
        }

        .log-user {
            color: #28a745;
            font-size: 14px;
        }

        .log-time {
            color: #6c757d;
            font-size: 12px;
        }

        .warranty-item {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .warranty-item:last-child {
            border-bottom: none;
        }

        .warranty-asset {
            font-weight: 600;
            color: #333;
        }

        .warranty-date {
            color: #dc3545;
            font-size: 14px;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        .btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #28a745;
            color: #28a745;
        }

        .btn-outline:hover {
            background: #28a745;
            color: white;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .system-status {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .system-status h3 {
            margin-bottom: 15px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .status-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-content {
                flex-direction: column;
                gap: 10px;
            }

            .content-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <span style="font-size: 32px;">🏢</span>
                <div>
                    <h1>Asset Management</h1>
                    <div class="mongodb-badge">🍃 MongoDB Version</div>
                </div>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <?= strtoupper(substr($currentUser['full_name'] ?? 'U', 0, 1)) ?>
                </div>
                <div>
                    <div style="font-weight: 600;"><?= htmlspecialchars($currentUser['full_name'] ?? 'User') ?></div>
                    <div style="font-size: 14px; opacity: 0.8;"><?= htmlspecialchars($currentUser['role'] ?? 'User') ?></div>
                </div>
                <a href="logout_mongodb.php" class="btn btn-outline" style="margin-left: 15px;">ออกจากระบบ</a>
            </div>
        </div>
    </header>

    <nav class="nav-menu">
        <div class="nav-content">
            <a href="index_mongodb.php" class="nav-item active">🏠 หน้าหลัก</a>
            <a href="assets_mongodb.php" class="nav-item">📦 จัดการทรัพย์สิน</a>
            <a href="reports_mongodb.php" class="nav-item">📊 รายงาน</a>
            <?php if (isAdmin()): ?>
                <a href="users_mongodb.php" class="nav-item">👥 จัดการผู้ใช้</a>
                <a href="settings_mongodb.php" class="nav-item">⚙️ ตั้งค่า</a>
            <?php endif; ?>
            <a href="mongodb_test.php" class="nav-item">🧪 ทดสอบระบบ</a>
        </div>
    </nav>

    <main class="main-content">
        <?php if (isset($error)): ?>
            <div class="alert alert-warning">
                <strong>⚠️ คำเตือน:</strong> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <div class="system-status">
            <h3>🔍 สถานะระบบ MongoDB</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div style="font-size: 18px; font-weight: bold;">✅ เชื่อมต่อแล้ว</div>
                    <div style="font-size: 14px; opacity: 0.8;">Database Connection</div>
                </div>
                <div class="status-item">
                    <div style="font-size: 18px; font-weight: bold;">🍃 MongoDB</div>
                    <div style="font-size: 14px; opacity: 0.8;">NoSQL Database</div>
                </div>
                <div class="status-item">
                    <div style="font-size: 18px; font-weight: bold;">⚡ High Performance</div>
                    <div style="font-size: 14px; opacity: 0.8;">Optimized Queries</div>
                </div>
                <div class="status-item">
                    <div style="font-size: 18px; font-weight: bold;">🔒 Secure</div>
                    <div style="font-size: 14px; opacity: 0.8;">Authentication Active</div>
                </div>
            </div>
        </div>

        <div class="quick-actions">
            <a href="add_asset_mongodb.php" class="btn">➕ เพิ่มทรัพย์สิน</a>
            <a href="import_mongodb.php" class="btn btn-outline">📥 นำเข้าข้อมูล</a>
            <a href="export_mongodb.php" class="btn btn-outline">📤 ส่งออกข้อมูล</a>
            <a href="backup_mongodb.php" class="btn btn-outline">💾 สำรองข้อมูล</a>
        </div>

        <div class="dashboard-grid">
            <div class="stat-card">
                <h3>📦 ทรัพย์สินทั้งหมด</h3>
                <div class="stat-number"><?= number_format($assetStats['total'] ?? 0) ?></div>
                <div class="stat-label">รายการ</div>
            </div>

            <div class="stat-card">
                <h3>👥 ผู้ใช้งาน</h3>
                <div class="stat-number"><?= number_format($totalUsers ?? 0) ?></div>
                <div class="stat-label">คน</div>
            </div>

            <div class="stat-card">
                <h3>⚠️ ใกล้หมดประกัน</h3>
                <div class="stat-number"><?= number_format(count($expiringWarranties ?? [])) ?></div>
                <div class="stat-label">รายการ</div>
            </div>

            <div class="stat-card">
                <h3>📝 กิจกรรมล่าสุด</h3>
                <div class="stat-number"><?= number_format(count($recentLogs ?? [])) ?></div>
                <div class="stat-label">รายการ</div>
            </div>
        </div>

        <?php if (!empty($assetStats['by_status'])): ?>
            <div class="content-card" style="margin-bottom: 30px;">
                <h3>📊 สถานะทรัพย์สิน</h3>
                <div class="dashboard-grid">
                    <?php foreach ($assetStats['by_status'] as $status): ?>
                        <div style="text-align: center; padding: 15px;">
                            <div style="font-size: 24px; font-weight: bold; color: #28a745;">
                                <?= number_format($status['count']) ?>
                            </div>
                            <div style="color: #6c757d;">
                                <?= htmlspecialchars($status['_id']) ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <div class="content-grid">
            <div class="content-card">
                <h3>📝 กิจกรรมล่าสุด</h3>
                <?php if (!empty($recentLogs)): ?>
                    <?php foreach ($recentLogs as $log): ?>
                        <div class="log-item">
                            <div>
                                <div class="log-action"><?= htmlspecialchars($log['action'] ?? 'Unknown') ?></div>
                                <div class="log-user">โดย: <?= htmlspecialchars($log['changed_by'] ?? 'Unknown') ?></div>
                            </div>
                            <div class="log-time">
                                <?= mongoDateToString($log['changed_date'] ?? null, 'd/m/Y H:i') ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p style="text-align: center; color: #6c757d; padding: 20px;">ไม่มีกิจกรรมล่าสุด</p>
                <?php endif; ?>
            </div>

            <div class="content-card">
                <h3>⚠️ ประกันใกล้หมด</h3>
                <?php if (!empty($expiringWarranties)): ?>
                    <?php foreach (array_slice($expiringWarranties, 0, 5) as $asset): ?>
                        <div class="warranty-item">
                            <div>
                                <div class="warranty-asset"><?= htmlspecialchars($asset['asset_id'] ?? 'N/A') ?></div>
                                <div style="font-size: 14px; color: #6c757d;">
                                    <?= htmlspecialchars($asset['type'] ?? 'N/A') ?>
                                </div>
                            </div>
                            <div class="warranty-date">
                                <?= mongoDateToString($asset['warranty_expire'] ?? null, 'd/m/Y') ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p style="text-align: center; color: #6c757d; padding: 20px;">ไม่มีทรัพย์สินที่ใกล้หมดประกัน</p>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <script>
        // Auto refresh dashboard every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);

        // Add loading states to buttons
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.href && !this.href.includes('#')) {
                    this.style.opacity = '0.7';
                    this.innerHTML = '⏳ กำลังโหลด...';
                }
            });
        });
    </script>
</body>
</html>
