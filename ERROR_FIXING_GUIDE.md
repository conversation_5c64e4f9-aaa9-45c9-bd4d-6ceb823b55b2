# คู่มือแก้ไขข้อผิดพลาด - Asset Management System

## ภาพรวม
คู่มือนี้จะช่วยแก้ไขข้อผิดพลาดที่พบบ่อยในระบบ Asset Management System

## เครื่องมือแก้ไขข้อผิดพลาด

### 1. fix_errors.php - เครื่องมือตรวจสอบและแก้ไขหลัก
```
http://localhost/asset/fix_errors.php
```

**ฟีเจอร์:**
- ตรวจสอบไฟล์สำคัญ
- ทดสอบการเชื่อมต่อฐานข้อมูล
- ตรวจสอบโครงสร้างฐานข้อมูล
- ตรวจสอบ PHP Extensions
- ตรวจสอบ Composer Dependencies
- ตรวจสอบสิทธิ์ไฟล์
- แก้ไขอัตโนมัติ

### 2. fix_missing_functions.php - แก้ไขฟังก์ชันที่ขาดหายไป
```
http://localhost/asset/fix_missing_functions.php
```

**ฟีเจอร์:**
- เพิ่มฟังก์ชันที่จำเป็น
- สร้างคลาส AssetManager และ Auth
- สร้างไฟล์ functions.php
- เพิ่ม backup trigger functions

### 3. fix_session.php - แก้ไขปัญหา Session
```
http://localhost/asset/fix_session.php
```

**ฟีเจอร์:**
- ตรวจสอบการทำงานของ Session
- ทดสอบการเขียน/อ่าน Session
- สร้าง Session Admin
- แก้ไขปัญหา Session

### 4. mysql80_migration.php - แก้ไขปัญหาฐานข้อมูล
```
http://localhost/asset/mysql80_migration.php
```

**ฟีเจอร์:**
- ตรวจสอบ MySQL version
- สร้างฐานข้อมูลและตาราง
- Migration ข้อมูล
- Optimization สำหรับ MySQL 8.0

## ข้อผิดพลาดที่พบบ่อยและวิธีแก้ไข

### 1. ข้อผิดพลาดการเชื่อมต่อฐานข้อมูล

#### ปัญหา: "Connection failed"
```
SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost'
```

**วิธีแก้:**
1. ตรวจสอบ username/password ในไฟล์ config
2. แก้ไข `includes/config.php`:
   ```php
   define('DB_USER', 'root');
   define('DB_PASS', ''); // ใส่รหัสผ่าน MySQL
   ```
3. ตรวจสอบว่า MySQL service ทำงานอยู่

#### ปัญหา: "Unknown database 'asset_management'"
```
SQLSTATE[HY000] [1049] Unknown database 'asset_management'
```

**วิธีแก้:**
1. รัน `mysql80_migration.php`
2. หรือสร้างฐานข้อมูลด้วยตนเอง:
   ```sql
   CREATE DATABASE asset_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

### 2. ข้อผิดพลาด PHP

#### ปัญหา: "Fatal error: Call to undefined function"
```
Fatal error: Call to undefined function formatDateTime()
```

**วิธีแก้:**
1. รัน `fix_missing_functions.php`
2. ตรวจสอบว่าไฟล์ `includes/functions.php` มีอยู่
3. เพิ่ม `require_once 'includes/functions.php';` ในไฟล์ที่ต้องการ

#### ปัญหา: "Class 'AssetManager' not found"
```
Fatal error: Class 'AssetManager' not found
```

**วิธีแก้:**
1. รัน `fix_missing_functions.php`
2. ตรวจสอบการ include ไฟล์ที่ถูกต้อง

### 3. ข้อผิดพลาด Session

#### ปัญหา: "Cannot start session"
```
Warning: session_start(): Cannot send session cookie
```

**วิธีแก้:**
1. รัน `fix_session.php`
2. ตรวจสอบว่าไม่มี output ก่อน `session_start()`
3. ตรวจสอบสิทธิ์ session save path

#### ปัญหา: "Session data lost"
**วิธีแก้:**
1. ใช้ `fix_session.php` เพื่อสร้าง Session Admin
2. ตรวจสอบ cookie settings ในเบราว์เซอร์
3. ลบ cookies และลองใหม่

### 4. ข้อผิดพลาด MySQL 8.0

#### ปัญหา: "Authentication plugin 'caching_sha2_password'"
```
SQLSTATE[HY000] [2054] The server requested authentication method unknown to the client
```

**วิธีแก้:**
```sql
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '';
FLUSH PRIVILEGES;
```

#### ปัญหา: "SQL syntax error"
**วิธีแก้:**
1. รัน `mysql80_migration.php`
2. ตรวจสอบ SQL mode:
   ```sql
   SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';
   ```

### 5. ข้อผิดพลาดไฟล์และสิทธิ์

#### ปัญหา: "Permission denied"
```
Warning: file_put_contents(): failed to open stream: Permission denied
```

**วิธีแก้:**
1. รัน `fix_errors.php` และเลือก "แก้ไขสิทธิ์ไฟล์"
2. ตั้งสิทธิ์โฟลเดอร์:
   ```bash
   chmod 755 backups logs uploads
   ```

#### ปัญหา: "File not found"
**วิธีแก้:**
1. ตรวจสอบว่าไฟล์ถูกอัปโหลดครบถ้วน
2. ตรวจสอบ path ในการ include

### 6. ข้อผิดพลาด Composer

#### ปัญหา: "Class 'Mpdf\Mpdf' not found"
**วิธีแก้:**
1. ติดตั้ง Composer dependencies:
   ```bash
   composer install
   ```
2. หรือดาวน์โหลด libraries ด้วยตนเอง

## ขั้นตอนการแก้ไขแบบครบวงจร

### ขั้นตอนที่ 1: ตรวจสอบพื้นฐาน
1. เรียกใช้ `fix_errors.php`
2. ตรวจสอบผลลัพธ์และปัญหาที่พบ
3. แก้ไขปัญหาพื้นฐานก่อน

### ขั้นตอนที่ 2: แก้ไขฐานข้อมูล
1. เรียกใช้ `mysql80_migration.php`
2. รัน "Full Setup" หากจำเป็น
3. ตรวจสอบการเชื่อมต่อ

### ขั้นตอนที่ 3: แก้ไขฟังก์ชัน
1. เรียกใช้ `fix_missing_functions.php`
2. ตรวจสอบว่าฟังก์ชันถูกเพิ่มแล้ว

### ขั้นตอนที่ 4: แก้ไข Session
1. เรียกใช้ `fix_session.php`
2. ทดสอบการทำงานของ Session
3. สร้าง Session Admin หากจำเป็น

### ขั้นตอนที่ 5: ทดสอบระบบ
1. เข้าสู่ระบบด้วย admin/admin123
2. ทดสอบฟีเจอร์ต่างๆ
3. ตรวจสอบ error log

## การป้องกันข้อผิดพลาด

### 1. การสำรองข้อมูล
- ใช้ระบบ Auto Backup
- สำรองฐานข้อมูลเป็นประจำ
- เก็บไฟล์สำคัญไว้

### 2. การตรวจสอบเป็นประจำ
- รัน `fix_errors.php` เป็นประจำ
- ตรวจสอบ error log
- ทดสอบฟีเจอร์หลัก

### 3. การอัปเดต
- อัปเดต PHP และ MySQL เป็นประจำ
- ตรวจสอบ compatibility
- ทดสอบหลังอัปเดต

## การติดต่อขอความช่วยเหลือ

หากยังมีปัญหา:

1. **ตรวจสอบ Error Log:**
   - PHP Error Log
   - MySQL Error Log
   - Web Server Error Log

2. **รวบรวมข้อมูล:**
   - ข้อความ error ที่แน่นอน
   - ขั้นตอนที่ทำให้เกิด error
   - ผลลัพธ์จาก fix_errors.php

3. **ลำดับการแก้ไข:**
   - แก้ไขปัญหาพื้นฐานก่อน
   - แก้ไขทีละปัญหา
   - ทดสอบหลังแก้ไขแต่ละครั้ง

## เครื่องมือเพิ่มเติม

### การตรวจสอบ PHP
```php
<?php
phpinfo();
?>
```

### การตรวจสอบ MySQL
```sql
SELECT VERSION();
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';
```

### การตรวจสอบ Apache/Nginx
- ตรวจสอบ error log
- ตรวจสอบ virtual host configuration
- ตรวจสอบ .htaccess

---

**หมายเหตุ**: คู่มือนี้ครอบคลุมปัญหาที่พบบ่อย หากพบปัญหาอื่นๆ กรุณาใช้เครื่องมือแก้ไขที่ให้มาหรือติดต่อขอความช่วยเหลือ
