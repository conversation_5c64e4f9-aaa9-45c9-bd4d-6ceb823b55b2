<?php
/**
 * MongoDB Login Page
 * Asset Management System - MongoDB Version
 */

require_once 'includes/mongodb_auth.php';

// Redirect if already logged in
if ($mongoAuth->isLoggedIn()) {
    header('Location: index_mongodb.php');
    exit;
}

$error = '';
$success = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']);
    
    if (empty($username) || empty($password)) {
        $error = 'กรุณากรอก Username และ Password';
    } else {
        $result = $mongoAuth->login($username, $password, $rememberMe);
        
        if ($result['success']) {
            header('Location: index_mongodb.php');
            exit;
        } else {
            $error = $result['message'];
        }
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เข้าสู่ระบบ - Asset Management (MongoDB)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'TH Sarabun New', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }

        .login-header h1 {
            font-size: 28px;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .login-header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .mongodb-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            margin-top: 10px;
            display: inline-block;
        }

        .login-form {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 16px;
        }

        .form-control {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            outline: none;
            border-color: #28a745;
            background: white;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
        }

        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .form-check input {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .form-check label {
            margin-bottom: 0;
            font-weight: normal;
            cursor: pointer;
        }

        .btn-login {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .login-footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }

        .login-footer a {
            color: #28a745;
            text-decoration: none;
            font-weight: 600;
            margin: 0 10px;
        }

        .login-footer a:hover {
            text-decoration: underline;
        }

        .system-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            font-size: 14px;
            color: #6c757d;
        }

        .system-info h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .default-credentials {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .default-credentials h4 {
            margin-bottom: 10px;
            color: #856404;
        }

        .icon {
            font-size: 20px;
            margin-right: 10px;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 10px;
            }
            
            .login-form {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><span class="icon">🔐</span>เข้าสู่ระบบ</h1>
            <p>Asset Management System</p>
            <div class="mongodb-badge">
                🍃 MongoDB Version
            </div>
        </div>

        <div class="login-form">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger">
                    <strong>❌ ข้อผิดพลาด:</strong> <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <strong>✅ สำเร็จ:</strong> <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <div class="default-credentials">
                <h4>🔑 ข้อมูลล็อกอินเริ่มต้น</h4>
                <p><strong>Username:</strong> admin</p>
                <p><strong>Password:</strong> admin123</p>
            </div>

            <form method="POST" action="">
                <div class="form-group">
                    <label for="username">
                        <span class="icon">👤</span>Username
                    </label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-control" 
                        value="<?= htmlspecialchars($_POST['username'] ?? '') ?>"
                        placeholder="กรอก Username"
                        required
                        autocomplete="username"
                    >
                </div>

                <div class="form-group">
                    <label for="password">
                        <span class="icon">🔒</span>Password
                    </label>
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        class="form-control" 
                        placeholder="กรอก Password"
                        required
                        autocomplete="current-password"
                    >
                </div>

                <div class="form-check">
                    <input 
                        type="checkbox" 
                        id="remember_me" 
                        name="remember_me"
                        <?= isset($_POST['remember_me']) ? 'checked' : '' ?>
                    >
                    <label for="remember_me">
                        จดจำการเข้าสู่ระบบ (30 วัน)
                    </label>
                </div>

                <button type="submit" class="btn-login">
                    <span class="icon">🚀</span>เข้าสู่ระบบ
                </button>
            </form>

            <div class="system-info">
                <h4>📊 ข้อมูลระบบ</h4>
                <p><strong>Database:</strong> MongoDB</p>
                <p><strong>Version:</strong> 2.0</p>
                <p><strong>Features:</strong> NoSQL, Scalable, High Performance</p>
            </div>
        </div>

        <div class="login-footer">
            <a href="register_mongodb.php">📝 สมัครสมาชิก</a>
            <a href="mongodb_test.php">🧪 ทดสอบระบบ</a>
            <a href="mongodb_migration.php">🔄 Migration</a>
        </div>
    </div>

    <script>
        // Auto focus on username field
        document.addEventListener('DOMContentLoaded', function() {
            const usernameField = document.getElementById('username');
            if (usernameField && !usernameField.value) {
                usernameField.focus();
            }
        });

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            if (!username || !password) {
                e.preventDefault();
                alert('กรุณากรอก Username และ Password');
                return false;
            }

            // Show loading state
            const submitBtn = document.querySelector('.btn-login');
            submitBtn.innerHTML = '<span class="icon">⏳</span>กำลังเข้าสู่ระบบ...';
            submitBtn.disabled = true;
        });

        // Add enter key support
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const form = document.querySelector('form');
                if (form) {
                    form.submit();
                }
            }
        });

        // Password visibility toggle (optional enhancement)
        function togglePasswordVisibility() {
            const passwordField = document.getElementById('password');
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);
        }

        // Add double-click to show password
        document.getElementById('password').addEventListener('dblclick', togglePasswordVisibility);

        // Check MongoDB connection status
        fetch('mongodb_test.php?check_connection=1')
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    const systemInfo = document.querySelector('.system-info');
                    systemInfo.innerHTML += '<p style="color: #dc3545;"><strong>⚠️ Warning:</strong> MongoDB connection issue detected</p>';
                }
            })
            .catch(error => {
                console.log('Connection check failed:', error);
            });
    </script>
</body>
</html>
