<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// ตรวจสอบการล็อกอิน
requireLogin();

// ตรวจสอบสิทธิ์ Admin
if (!isAdmin()) {
    header('Location: index.php');
    exit;
}

// จัดการการทำงานของระบบ backup
$message = '';
$backupFiles = [];

// ดึงรายการไฟล์ backup ที่มีอยู่
function getBackupFiles() {
    $backupDir = 'backups/';
    if (!is_dir($backupDir)) {
        mkdir($backupDir, 0755, true);
    }
    
    $files = glob($backupDir . '*.sql');
    $backupFiles = [];
    
    foreach ($files as $file) {
        $backupFiles[] = [
            'filename' => basename($file),
            'filepath' => $file,
            'size' => formatBytes(filesize($file)),
            'date' => date('Y-m-d H:i:s', filemtime($file))
        ];
    }
    
    // เรียงตามวันที่ล่าสุด
    usort($backupFiles, function($a, $b) {
        return filemtime($b['filepath']) - filemtime($a['filepath']);
    });
    
    return $backupFiles;
}

// ฟังก์ชันสร้าง backup
function createBackup() {
    global $pdo;
    
    try {
        // สร้างโฟลเดอร์ backup ถ้ายังไม่มี
        $backupDir = 'backups/';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        // ชื่อไฟล์ backup
        $filename = 'asset_backup_' . date('Y-m-d_H-i-s') . '.sql';
        $filepath = $backupDir . $filename;
        
        // เริ่มสร้างไฟล์ backup
        $backup = "-- Asset Management System Database Backup\n";
        $backup .= "-- Created: " . date('Y-m-d H:i:s') . "\n";
        $backup .= "-- Database: asset_management\n\n";
        
        $backup .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";
        
        // ดึงรายชื่อตาราง
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            // สร้าง DROP TABLE
            $backup .= "DROP TABLE IF EXISTS `$table`;\n";
            
            // สร้าง CREATE TABLE
            $createTable = $pdo->query("SHOW CREATE TABLE `$table`")->fetch(PDO::FETCH_ASSOC);
            $backup .= $createTable['Create Table'] . ";\n\n";
            
            // ดึงข้อมูลในตาราง
            $rows = $pdo->query("SELECT * FROM `$table`")->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($rows)) {
                $backup .= "INSERT INTO `$table` VALUES\n";
                $values = [];
                
                foreach ($rows as $row) {
                    $rowValues = [];
                    foreach ($row as $value) {
                        if ($value === null) {
                            $rowValues[] = 'NULL';
                        } else {
                            $rowValues[] = "'" . addslashes($value) . "'";
                        }
                    }
                    $values[] = '(' . implode(', ', $rowValues) . ')';
                }
                
                $backup .= implode(",\n", $values) . ";\n\n";
            }
        }
        
        $backup .= "SET FOREIGN_KEY_CHECKS = 1;\n";
        
        // บันทึกไฟล์
        if (file_put_contents($filepath, $backup)) {
            return ['success' => true, 'filename' => $filename, 'size' => formatBytes(filesize($filepath))];
        } else {
            return ['success' => false, 'error' => 'ไม่สามารถสร้างไฟล์ backup ได้'];
        }
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()];
    }
}

// ฟังก์ชันลบไฟล์ backup
function deleteBackup($filename) {
    $filepath = 'backups/' . $filename;
    if (file_exists($filepath)) {
        if (unlink($filepath)) {
            return ['success' => true, 'message' => 'ลบไฟล์ backup สำเร็จ'];
        } else {
            return ['success' => false, 'error' => 'ไม่สามารถลบไฟล์ได้'];
        }
    } else {
        return ['success' => false, 'error' => 'ไม่พบไฟล์ที่ต้องการลบ'];
    }
}

// ฟังก์ชันดาวน์โหลดไฟล์ backup
function downloadBackup($filename) {
    $filepath = 'backups/' . $filename;
    if (file_exists($filepath)) {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($filepath));
        readfile($filepath);
        exit;
    }
}

// ฟังก์ชันทำความสะอาดไฟล์ backup เก่า
function cleanOldBackups($keepDays = 30) {
    $backupDir = 'backups/';
    $files = glob($backupDir . '*.sql');
    $deleted = 0;
    $cutoffTime = time() - ($keepDays * 24 * 60 * 60);
    
    foreach ($files as $file) {
        if (filemtime($file) < $cutoffTime) {
            if (unlink($file)) {
                $deleted++;
            }
        }
    }
    
    return $deleted;
}

// ฟังก์ชันตั้งค่า Auto backup
function setupAutoBackup() {
    $cronJob = "0 2 * * * cd " . __DIR__ . " && php auto_backup.php > /dev/null 2>&1";
    return $cronJob;
}

// จัดการ POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_backup':
                $result = createBackup();
                if ($result['success']) {
                    $message = ['type' => 'success', 'text' => 'สร้าง backup สำเร็จ: ' . $result['filename'] . ' (' . $result['size'] . ')'];
                } else {
                    $message = ['type' => 'error', 'text' => $result['error']];
                }
                break;
                
            case 'delete_backup':
                if (isset($_POST['filename'])) {
                    $result = deleteBackup($_POST['filename']);
                    $message = ['type' => $result['success'] ? 'success' : 'error', 'text' => $result['success'] ? $result['message'] : $result['error']];
                }
                break;
                
            case 'clean_old_backups':
                $keepDays = intval($_POST['keep_days'] ?? 30);
                $deleted = cleanOldBackups($keepDays);
                $message = ['type' => 'success', 'text' => 'ลบไฟล์ backup เก่า ' . $deleted . ' ไฟล์'];
                break;
        }
    }
}

// จัดการ GET requests
if (isset($_GET['action']) && $_GET['action'] === 'download' && isset($_GET['file'])) {
    downloadBackup($_GET['file']);
}

// ดึงรายการไฟล์ backup
$backupFiles = getBackupFiles();

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ระบบ Auto Backup SQL - Asset Management System</title>
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <link rel="stylesheet" href="assets/style.css">
    
    <style>
        .backup-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .backup-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .backup-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
        }
        
        .backup-card h3 {
            color: #2d3748;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4299e1;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .backup-btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 10px 5px 10px 0;
            transition: background 0.3s;
            text-decoration: none;
        }
        
        .backup-btn:hover {
            background: #3182ce;
        }
        
        .backup-btn.danger {
            background: #e53e3e;
        }
        
        .backup-btn.danger:hover {
            background: #c53030;
        }
        
        .backup-btn.success {
            background: #38a169;
        }
        
        .backup-btn.success:hover {
            background: #2f855a;
        }
        
        .backup-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .backup-table th,
        .backup-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .backup-table th {
            background: #f7fafc;
            font-weight: 600;
            color: #4a5568;
        }
        
        .backup-table tr:hover {
            background: #f7fafc;
        }
        
        .backup-actions {
            display: flex;
            gap: 5px;
        }
        
        .backup-actions button,
        .backup-actions a {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.9em;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #4a5568;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #4299e1;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            color: #3182ce;
        }
        
        .status-info {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .status-info h4 {
            color: #234e52;
            margin: 0 0 10px 0;
        }
        
        .cron-command {
            background: #2d3748;
            color: #e2e8f0;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin-top: 10px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ระบบ Auto Backup SQL</h1>
            <p class="subtitle">SQL Database Backup System - ระบบสำรองข้อมูลฐานข้อมูล</p>
        </div>
    </div>

    <div class="backup-container">
        <a href="index.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            กลับสู่หน้าหลัก
        </a>

        <?php if (!empty($message)): ?>
        <div class="alert <?php echo $message['type']; ?>">
            <i class="fas <?php echo $message['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?>"></i>
            <?php echo htmlspecialchars($message['text']); ?>
        </div>
        <?php endif; ?>

        <div class="status-info">
            <h4><i class="fas fa-info-circle"></i> สถานะระบบ Backup</h4>
            <p><strong>จำนวนไฟล์ Backup:</strong> <?php echo count($backupFiles); ?> ไฟล์</p>
            <p><strong>ขนาดรวม:</strong>
                <?php
                $totalSize = 0;
                foreach ($backupFiles as $file) {
                    $totalSize += filesize($file['filepath']);
                }
                echo formatBytes($totalSize);
                ?>
            </p>
            <p><strong>Backup ล่าสุด:</strong>
                <?php echo !empty($backupFiles) ? $backupFiles[0]['date'] : 'ยังไม่มี'; ?>
            </p>
        </div>

        <div class="backup-grid">
            <!-- การจัดการ Backup -->
            <div class="backup-card">
                <h3>
                    <i class="fas fa-database"></i>
                    การจัดการ Backup
                </h3>

                <form method="POST" style="margin-bottom: 20px;">
                    <input type="hidden" name="action" value="create_backup">
                    <button type="submit" class="backup-btn success">
                        <i class="fas fa-plus"></i>
                        สร้าง Backup ใหม่
                    </button>
                </form>

                <form method="POST">
                    <input type="hidden" name="action" value="clean_old_backups">
                    <div class="form-group">
                        <label for="keep_days">ลบไฟล์ Backup เก่า (เก็บไว้กี่วัน):</label>
                        <input type="number" id="keep_days" name="keep_days" value="30" min="1" max="365">
                    </div>
                    <button type="submit" class="backup-btn danger" onclick="return confirm('คุณต้องการลบไฟล์ backup เก่าหรือไม่?')">
                        <i class="fas fa-trash"></i>
                        ลบไฟล์เก่า
                    </button>
                </form>
            </div>

            <!-- การตั้งค่า Auto Backup -->
            <div class="backup-card">
                <h3>
                    <i class="fas fa-clock"></i>
                    การตั้งค่า Auto Backup
                </h3>

                <p>เพื่อให้ระบบทำ backup อัตโนมัติ กรุณาเพิ่ม Cron Job ต่อไปนี้:</p>

                <div class="cron-command">
                    0 2 * * * cd <?php echo __DIR__; ?> && php auto_backup.php
                </div>

                <p style="margin-top: 15px; font-size: 0.9em; color: #666;">
                    <strong>คำอธิบาย:</strong> จะทำ backup ทุกวันเวลา 02:00 น.<br>
                    <strong>วิธีตั้งค่า:</strong> ใช้คำสั่ง <code>crontab -e</code> และเพิ่มบรรทัดข้างต้น
                </p>

                <button onclick="copyToClipboard()" class="backup-btn">
                    <i class="fas fa-copy"></i>
                    คัดลอกคำสั่ง
                </button>

                <a href="backup_settings.php" class="backup-btn" style="background: #6f42c1; text-decoration: none;">
                    <i class="fas fa-cog"></i>
                    ตั้งค่า Auto Backup
                </a>
            </div>
        </div>

        <!-- รายการไฟล์ Backup -->
        <div class="backup-card" style="margin-top: 20px;">
            <h3>
                <i class="fas fa-list"></i>
                รายการไฟล์ Backup
            </h3>

            <?php if (!empty($backupFiles)): ?>
            <table class="backup-table">
                <thead>
                    <tr>
                        <th>ชื่อไฟล์</th>
                        <th>ขนาด</th>
                        <th>วันที่สร้าง</th>
                        <th>การจัดการ</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($backupFiles as $file): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($file['filename']); ?></td>
                        <td><?php echo $file['size']; ?></td>
                        <td><?php echo $file['date']; ?></td>
                        <td>
                            <div class="backup-actions">
                                <a href="?action=download&file=<?php echo urlencode($file['filename']); ?>"
                                   class="backup-btn" style="background: #38a169;">
                                    <i class="fas fa-download"></i>
                                    ดาวน์โหลด
                                </a>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="delete_backup">
                                    <input type="hidden" name="filename" value="<?php echo htmlspecialchars($file['filename']); ?>">
                                    <button type="submit" class="backup-btn danger"
                                            onclick="return confirm('คุณต้องการลบไฟล์ <?php echo htmlspecialchars($file['filename']); ?> หรือไม่?')">
                                        <i class="fas fa-trash"></i>
                                        ลบ
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php else: ?>
            <p style="text-align: center; color: #666; padding: 40px;">
                <i class="fas fa-inbox" style="font-size: 3em; margin-bottom: 15px; display: block;"></i>
                ยังไม่มีไฟล์ backup<br>
                <small>กดปุ่ม "สร้าง Backup ใหม่" เพื่อเริ่มต้น</small>
            </p>
            <?php endif; ?>
        </div>
    </div>

    <script>
        function copyToClipboard() {
            const cronCommand = "0 2 * * * cd <?php echo __DIR__; ?> && php auto_backup.php";

            if (navigator.clipboard) {
                navigator.clipboard.writeText(cronCommand).then(function() {
                    alert('คัดลอกคำสั่ง Cron Job สำเร็จ!');
                }).catch(function(err) {
                    console.error('ไม่สามารถคัดลอกได้: ', err);
                    fallbackCopyTextToClipboard(cronCommand);
                });
            } else {
                fallbackCopyTextToClipboard(cronCommand);
            }
        }

        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    alert('คัดลอกคำสั่ง Cron Job สำเร็จ!');
                } else {
                    alert('ไม่สามารถคัดลอกได้');
                }
            } catch (err) {
                alert('ไม่สามารถคัดลอกได้');
            }

            document.body.removeChild(textArea);
        }

        // Auto refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
