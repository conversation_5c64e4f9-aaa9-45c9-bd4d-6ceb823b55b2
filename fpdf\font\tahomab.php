<?php
$type = 'TrueType';
$name = 'Tahoma-Bold';
$desc = array('Ascent'=>765,'Descent'=>-207,'CapHeight'=>765,'Flags'=>32,'FontBBox'=>'[-698 -207 1625 1065]','ItalicAngle'=>0,'StemV'=>120,'MissingWidth'=>1000);
$up = -70;
$ut = 98;
$cw = array(
	chr(0)=>1000,chr(1)=>1000,chr(2)=>1000,chr(3)=>1000,chr(4)=>1000,chr(5)=>1000,chr(6)=>1000,chr(7)=>1000,chr(8)=>1000,chr(9)=>1000,chr(10)=>1000,chr(11)=>1000,chr(12)=>1000,chr(13)=>1000,chr(14)=>1000,chr(15)=>1000,chr(16)=>1000,chr(17)=>1000,chr(18)=>1000,chr(19)=>1000,chr(20)=>1000,chr(21)=>1000,
	chr(22)=>1000,chr(23)=>1000,chr(24)=>1000,chr(25)=>1000,chr(26)=>1000,chr(27)=>1000,chr(28)=>1000,chr(29)=>1000,chr(30)=>1000,chr(31)=>1000,' '=>293,'!'=>343,'"'=>489,'#'=>818,'$'=>637,'%'=>1199,'&'=>781,'\''=>275,'('=>454,')'=>454,'*'=>637,'+'=>818,
	','=>313,'-'=>431,'.'=>313,'/'=>577,'0'=>637,'1'=>637,'2'=>637,'3'=>637,'4'=>637,'5'=>637,'6'=>637,'7'=>637,'8'=>637,'9'=>637,':'=>363,';'=>363,'<'=>818,'='=>818,'>'=>818,'?'=>566,'@'=>920,'A'=>685,
	'B'=>686,'C'=>667,'D'=>757,'E'=>615,'F'=>581,'G'=>745,'H'=>764,'I'=>483,'J'=>500,'K'=>696,'L'=>572,'M'=>893,'N'=>771,'O'=>770,'P'=>657,'Q'=>770,'R'=>726,'S'=>633,'T'=>612,'U'=>739,'V'=>675,'W'=>1028,
	'X'=>685,'Y'=>670,'Z'=>623,'['=>454,'\\'=>577,']'=>454,'^'=>818,'_'=>637,'`'=>546,'a'=>599,'b'=>632,'c'=>527,'d'=>629,'e'=>594,'f'=>382,'g'=>629,'h'=>640,'i'=>302,'j'=>363,'k'=>603,'l'=>302,'m'=>954,
	'n'=>640,'o'=>617,'p'=>629,'q'=>629,'r'=>434,'s'=>515,'t'=>416,'u'=>640,'v'=>579,'w'=>890,'x'=>604,'y'=>576,'z'=>526,'{'=>623,'|'=>637,'}'=>623,'~'=>818,chr(127)=>1000,chr(128)=>637,chr(129)=>1000,chr(130)=>1000,chr(131)=>1000,
	chr(132)=>1000,chr(133)=>1000,chr(134)=>1000,chr(135)=>1000,chr(136)=>1000,chr(137)=>1000,chr(138)=>1000,chr(139)=>1000,chr(140)=>1000,chr(141)=>1000,chr(142)=>1000,chr(143)=>1000,chr(144)=>1000,chr(145)=>275,chr(146)=>275,chr(147)=>489,chr(148)=>489,chr(149)=>637,chr(150)=>637,chr(151)=>909,chr(152)=>1000,chr(153)=>1000,
	chr(154)=>1000,chr(155)=>1000,chr(156)=>1000,chr(157)=>1000,chr(158)=>1000,chr(159)=>1000,chr(160)=>293,chr(161)=>680,chr(162)=>699,chr(163)=>718,chr(164)=>688,chr(165)=>688,chr(166)=>732,chr(167)=>544,chr(168)=>617,chr(169)=>672,chr(170)=>744,chr(171)=>771,chr(172)=>947,chr(173)=>957,chr(174)=>697,chr(175)=>697,
	chr(176)=>604,chr(177)=>843,chr(178)=>1007,chr(179)=>1028,chr(180)=>688,chr(181)=>688,chr(182)=>680,chr(183)=>728,chr(184)=>597,chr(185)=>679,chr(186)=>681,chr(187)=>681,chr(188)=>730,chr(189)=>730,chr(190)=>801,chr(191)=>801,chr(192)=>697,chr(193)=>671,chr(194)=>665,chr(195)=>525,chr(196)=>680,chr(197)=>643,
	chr(198)=>697,chr(199)=>531,chr(200)=>688,chr(201)=>708,chr(202)=>643,chr(203)=>710,chr(204)=>822,chr(205)=>633,chr(206)=>633,chr(207)=>563,chr(208)=>476,chr(209)=>0,chr(210)=>531,chr(211)=>531,chr(212)=>0,chr(213)=>0,chr(214)=>0,chr(215)=>0,chr(216)=>0,chr(217)=>0,chr(218)=>0,chr(219)=>1000,
	chr(220)=>1000,chr(221)=>1000,chr(222)=>1000,chr(223)=>686,chr(224)=>343,chr(225)=>631,chr(226)=>481,chr(227)=>481,chr(228)=>524,chr(229)=>531,chr(230)=>543,chr(231)=>0,chr(232)=>0,chr(233)=>0,chr(234)=>0,chr(235)=>0,chr(236)=>0,chr(237)=>0,chr(238)=>0,chr(239)=>618,chr(240)=>632,chr(241)=>716,
	chr(242)=>777,chr(243)=>707,chr(244)=>780,chr(245)=>780,chr(246)=>681,chr(247)=>966,chr(248)=>774,chr(249)=>849,chr(250)=>780,chr(251)=>1044,chr(252)=>1000,chr(253)=>1000,chr(254)=>1000,chr(255)=>1000);
$enc = 'cp874';
$diff = '130 /.notdef /.notdef /.notdef 134 /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef 142 /.notdef 152 /.notdef /.notdef /.notdef /.notdef /.notdef 158 /.notdef /.notdef 161 /kokaithai /khokhaithai /khokhuatthai /khokhwaithai /khokhonthai /khorakhangthai /ngonguthai /chochanthai /chochingthai /chochangthai /sosothai /chochoethai /yoyingthai /dochadathai /topatakthai /thothanthai /thonangmonthothai /thophuthaothai /nonenthai /dodekthai /totaothai /thothungthai /thothahanthai /thothongthai /nonuthai /bobaimaithai /poplathai /phophungthai /fofathai /phophanthai /fofanthai /phosamphaothai /momathai /yoyakthai /roruathai /ruthai /lolingthai /luthai /wowaenthai /sosalathai /sorusithai /sosuathai /hohipthai /lochulathai /oangthai /honokhukthai /paiyannoithai /saraathai /maihanakatthai /saraaathai /saraamthai /saraithai /saraiithai /sarauethai /saraueethai /sarauthai /sarauuthai /phinthuthai /.notdef /.notdef /.notdef /.notdef /bahtthai /saraethai /saraaethai /saraothai /saraaimaimuanthai /saraaimaimalaithai /lakkhangyaothai /maiyamokthai /maitaikhuthai /maiekthai /maithothai /maitrithai /maichattawathai /thanthakhatthai /nikhahitthai /yamakkanthai /fongmanthai /zerothai /onethai /twothai /threethai /fourthai /fivethai /sixthai /seventhai /eightthai /ninethai /angkhankhuthai /khomutthai /.notdef /.notdef /.notdef /.notdef';
$uv = array(0=>array(0,128),128=>8364,133=>8230,145=>array(8216,2),147=>array(8220,2),149=>8226,150=>array(8211,2),160=>160,161=>array(3585,58),223=>array(3647,29));
$file = 'tahomab.z';
$originalsize = 60080;
$subsetted = true;
?>
