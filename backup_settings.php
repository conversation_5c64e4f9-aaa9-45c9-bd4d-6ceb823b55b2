<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'includes/backup_config.php';

// ตรวจสอบการล็อกอิน
requireLogin();

// ตรวจสอบสิทธิ์ Admin
if (!isAdmin()) {
    header('Location: index.php');
    exit;
}

$message = '';

// จัดการการบันทึกการตั้งค่า
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'save_settings') {
        try {
            $config = [
                'enabled' => isset($_POST['enabled']),
                'backup_path' => trim($_POST['backup_path']),
                'log_path' => trim($_POST['log_path']),
                'auto_backup_on_changes' => isset($_POST['auto_backup_on_changes']),
                'backup_on_asset_add' => isset($_POST['backup_on_asset_add']),
                'backup_on_asset_edit' => isset($_POST['backup_on_asset_edit']),
                'backup_on_asset_delete' => isset($_POST['backup_on_asset_delete']),
                'backup_on_user_add' => isset($_POST['backup_on_user_add']),
                'backup_on_user_edit' => isset($_POST['backup_on_user_edit']),
                'backup_on_user_delete' => isset($_POST['backup_on_user_delete']),
                'max_backup_files' => intval($_POST['max_backup_files']),
                'keep_days' => intval($_POST['keep_days']),
                'compress_backups' => isset($_POST['compress_backups']),
                'email_notifications' => isset($_POST['email_notifications']),
                'admin_email' => trim($_POST['admin_email']),
                'backup_prefix' => trim($_POST['backup_prefix']),
                'include_logs_in_backup' => isset($_POST['include_logs_in_backup']),
                'backup_timeout' => intval($_POST['backup_timeout']),
            ];
            
            // ตรวจสอบความถูกต้องของข้อมูล
            if (empty($config['backup_path'])) {
                throw new Exception('กรุณาระบุ Path สำหรับเก็บไฟล์ Backup');
            }
            
            if (empty($config['log_path'])) {
                throw new Exception('กรุณาระบุ Path สำหรับเก็บไฟล์ Log');
            }
            
            if ($config['max_backup_files'] < 1) {
                throw new Exception('จำนวนไฟล์ Backup สูงสุดต้องมากกว่า 0');
            }
            
            if ($config['keep_days'] < 1) {
                throw new Exception('จำนวนวันที่เก็บไฟล์ต้องมากกว่า 0');
            }
            
            if ($config['backup_timeout'] < 30) {
                throw new Exception('Timeout ต้องมากกว่า 30 วินาที');
            }
            
            // บันทึกการตั้งค่า
            BackupConfig::setAll($config);
            if (BackupConfig::save()) {
                $message = ['type' => 'success', 'text' => 'บันทึกการตั้งค่าสำเร็จ'];
            } else {
                throw new Exception('ไม่สามารถบันทึกการตั้งค่าได้');
            }
            
        } catch (Exception $e) {
            $message = ['type' => 'error', 'text' => $e->getMessage()];
        }
    }
}

// ดึงการตั้งค่าปัจจุบัน
$config = BackupConfig::getAll();
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>การตั้งค่า Auto Backup - Asset Management System</title>
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <link rel="stylesheet" href="assets/style.css">
    
    <style>
        .settings-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .settings-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            margin-bottom: 20px;
        }
        
        .settings-card h3 {
            color: #2d3748;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4299e1;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #4a5568;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin: 0;
        }
        
        .checkbox-group label {
            margin: 0;
            font-weight: normal;
            cursor: pointer;
        }
        
        .help-text {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        
        .save-btn {
            background: #38a169;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .save-btn:hover {
            background: #2f855a;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #4299e1;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            color: #3182ce;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .path-input {
            position: relative;
        }
        
        .path-input input {
            padding-right: 40px;
        }
        
        .path-browse {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #4299e1;
            cursor: pointer;
            padding: 4px;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .status-indicator.enabled {
            background: #d4edda;
            color: #155724;
        }
        
        .status-indicator.disabled {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>การตั้งค่า Auto Backup</h1>
            <p class="subtitle">Backup Configuration - การตั้งค่าระบบสำรองข้อมูล</p>
        </div>
    </div>

    <div class="settings-container">
        <a href="backup_system.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            กลับสู่ระบบ Backup
        </a>
        
        <?php if (!empty($message)): ?>
        <div class="alert <?php echo $message['type']; ?>">
            <i class="fas <?php echo $message['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?>"></i>
            <?php echo htmlspecialchars($message['text']); ?>
        </div>
        <?php endif; ?>

        <form method="POST">
            <input type="hidden" name="action" value="save_settings">
            
            <!-- การตั้งค่าพื้นฐาน -->
            <div class="settings-card">
                <h3>
                    <i class="fas fa-cog"></i>
                    การตั้งค่าพื้นฐาน
                </h3>
                
                <div class="form-grid">
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="enabled" name="enabled" <?php echo $config['enabled'] ? 'checked' : ''; ?>>
                            <label for="enabled">เปิดใช้งานระบบ Auto Backup</label>
                        </div>
                        <div class="help-text">เปิด/ปิดการทำงานของระบบ backup อัตโนมัติ</div>
                    </div>
                    
                    <div class="form-group">
                        <label>สถานะปัจจุบัน:</label>
                        <span class="status-indicator <?php echo $config['enabled'] ? 'enabled' : 'disabled'; ?>">
                            <i class="fas <?php echo $config['enabled'] ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                            <?php echo $config['enabled'] ? 'เปิดใช้งาน' : 'ปิดใช้งาน'; ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- การตั้งค่า Path -->
            <div class="settings-card">
                <h3>
                    <i class="fas fa-folder"></i>
                    การตั้งค่า Path
                </h3>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="backup_path">Path สำหรับเก็บไฟล์ Backup:</label>
                        <div class="path-input">
                            <input type="text" id="backup_path" name="backup_path" value="<?php echo htmlspecialchars($config['backup_path']); ?>" required>
                            <button type="button" class="path-browse" onclick="browseFolder('backup_path')">
                                <i class="fas fa-folder-open"></i>
                            </button>
                        </div>
                        <div class="help-text">ระบุ path แบบ absolute (เช่น /var/backups/) หรือ relative (เช่น backups/)</div>
                    </div>

                    <div class="form-group">
                        <label for="log_path">Path สำหรับเก็บไฟล์ Log:</label>
                        <div class="path-input">
                            <input type="text" id="log_path" name="log_path" value="<?php echo htmlspecialchars($config['log_path']); ?>" required>
                            <button type="button" class="path-browse" onclick="browseFolder('log_path')">
                                <i class="fas fa-folder-open"></i>
                            </button>
                        </div>
                        <div class="help-text">ระบุ path สำหรับเก็บไฟล์ log การทำงาน</div>
                    </div>
                </div>
            </div>

            <!-- การตั้งค่า Auto Backup -->
            <div class="settings-card">
                <h3>
                    <i class="fas fa-magic"></i>
                    การตั้งค่า Auto Backup
                </h3>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="auto_backup_on_changes" name="auto_backup_on_changes" <?php echo $config['auto_backup_on_changes'] ? 'checked' : ''; ?>>
                        <label for="auto_backup_on_changes">เปิดใช้งาน Auto Backup เมื่อมีการเปลี่ยนแปลงข้อมูล</label>
                    </div>
                    <div class="help-text">ทำ backup อัตโนมัติเมื่อมีการเพิ่ม/แก้ไข/ลบข้อมูล</div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label>การจัดการ Asset:</label>
                        <div class="checkbox-group">
                            <input type="checkbox" id="backup_on_asset_add" name="backup_on_asset_add" <?php echo $config['backup_on_asset_add'] ? 'checked' : ''; ?>>
                            <label for="backup_on_asset_add">เพิ่ม Asset</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="backup_on_asset_edit" name="backup_on_asset_edit" <?php echo $config['backup_on_asset_edit'] ? 'checked' : ''; ?>>
                            <label for="backup_on_asset_edit">แก้ไข Asset</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="backup_on_asset_delete" name="backup_on_asset_delete" <?php echo $config['backup_on_asset_delete'] ? 'checked' : ''; ?>>
                            <label for="backup_on_asset_delete">ลบ Asset</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>การจัดการ User:</label>
                        <div class="checkbox-group">
                            <input type="checkbox" id="backup_on_user_add" name="backup_on_user_add" <?php echo $config['backup_on_user_add'] ? 'checked' : ''; ?>>
                            <label for="backup_on_user_add">เพิ่ม User</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="backup_on_user_edit" name="backup_on_user_edit" <?php echo $config['backup_on_user_edit'] ? 'checked' : ''; ?>>
                            <label for="backup_on_user_edit">แก้ไข User</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="backup_on_user_delete" name="backup_on_user_delete" <?php echo $config['backup_on_user_delete'] ? 'checked' : ''; ?>>
                            <label for="backup_on_user_delete">ลบ User</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- การตั้งค่าการเก็บไฟล์ -->
            <div class="settings-card">
                <h3>
                    <i class="fas fa-archive"></i>
                    การตั้งค่าการเก็บไฟล์
                </h3>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="max_backup_files">จำนวนไฟล์ Backup สูงสุด:</label>
                        <input type="number" id="max_backup_files" name="max_backup_files" value="<?php echo $config['max_backup_files']; ?>" min="1" max="1000" required>
                        <div class="help-text">จำนวนไฟล์ backup สูงสุดที่เก็บไว้ (เกินจำนวนนี้จะลบไฟล์เก่า)</div>
                    </div>

                    <div class="form-group">
                        <label for="keep_days">เก็บไฟล์ไว้กี่วัน:</label>
                        <input type="number" id="keep_days" name="keep_days" value="<?php echo $config['keep_days']; ?>" min="1" max="365" required>
                        <div class="help-text">จำนวนวันที่เก็บไฟล์ backup (เก่ากว่านี้จะถูกลบ)</div>
                    </div>

                    <div class="form-group">
                        <label for="backup_prefix">Prefix ชื่อไฟล์:</label>
                        <input type="text" id="backup_prefix" name="backup_prefix" value="<?php echo htmlspecialchars($config['backup_prefix']); ?>" required>
                        <div class="help-text">คำนำหน้าชื่อไฟล์ backup (เช่น auto_, manual_)</div>
                    </div>

                    <div class="form-group">
                        <label for="backup_timeout">Timeout (วินาที):</label>
                        <input type="number" id="backup_timeout" name="backup_timeout" value="<?php echo $config['backup_timeout']; ?>" min="30" max="3600" required>
                        <div class="help-text">เวลาสูงสุดในการทำ backup (วินาที)</div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="compress_backups" name="compress_backups" <?php echo $config['compress_backups'] ? 'checked' : ''; ?>>
                        <label for="compress_backups">บีบอัดไฟล์ Backup (ยังไม่รองรับ)</label>
                    </div>
                    <div class="help-text">บีบอัดไฟล์ backup เพื่อประหยัดพื้นที่</div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="include_logs_in_backup" name="include_logs_in_backup" <?php echo $config['include_logs_in_backup'] ? 'checked' : ''; ?>>
                        <label for="include_logs_in_backup">รวมไฟล์ Log ใน Backup</label>
                    </div>
                    <div class="help-text">รวมไฟล์ log ในการ backup ด้วย</div>
                </div>
            </div>

            <!-- การตั้งค่าการแจ้งเตือน -->
            <div class="settings-card">
                <h3>
                    <i class="fas fa-bell"></i>
                    การตั้งค่าการแจ้งเตือน
                </h3>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="email_notifications" name="email_notifications" <?php echo $config['email_notifications'] ? 'checked' : ''; ?>>
                        <label for="email_notifications">เปิดใช้งานการแจ้งเตือนทางอีเมล</label>
                    </div>
                    <div class="help-text">ส่งอีเมลแจ้งเตือนเมื่อ backup สำเร็จหรือล้มเหลว</div>
                </div>

                <div class="form-group">
                    <label for="admin_email">อีเมลผู้ดูแลระบบ:</label>
                    <input type="email" id="admin_email" name="admin_email" value="<?php echo htmlspecialchars($config['admin_email']); ?>">
                    <div class="help-text">อีเมลที่จะรับการแจ้งเตือน</div>
                </div>
            </div>

            <!-- ปุ่มบันทึก -->
            <div class="settings-card">
                <button type="submit" class="save-btn">
                    <i class="fas fa-save"></i>
                    บันทึกการตั้งค่า
                </button>
            </div>
        </form>
    </div>

    <script>
        function browseFolder(inputId) {
            // ฟังก์ชันสำหรับเลือกโฟลเดอร์ (ต้องใช้ File API หรือ dialog)
            alert('กรุณาพิมพ์ path โดยตรง\n\nตัวอย่าง:\n- Relative: backups/\n- Absolute: /var/backups/\n- Windows: C:\\backups\\');
        }

        // ตรวจสอบการเปลี่ยนแปลงการตั้งค่า
        document.getElementById('auto_backup_on_changes').addEventListener('change', function() {
            const checkboxes = [
                'backup_on_asset_add',
                'backup_on_asset_edit',
                'backup_on_asset_delete',
                'backup_on_user_add',
                'backup_on_user_edit',
                'backup_on_user_delete'
            ];

            checkboxes.forEach(function(id) {
                document.getElementById(id).disabled = !this.checked;
            }, this);
        });

        // เรียกใช้เมื่อโหลดหน้า
        document.getElementById('auto_backup_on_changes').dispatchEvent(new Event('change'));

        // ตรวจสอบ path ที่กรอก
        function validatePath(input) {
            const path = input.value.trim();
            if (path && !path.endsWith('/') && !path.endsWith('\\')) {
                input.value = path + '/';
            }
        }

        document.getElementById('backup_path').addEventListener('blur', function() {
            validatePath(this);
        });

        document.getElementById('log_path').addEventListener('blur', function() {
            validatePath(this);
        });
    </script>
</body>
</html>
