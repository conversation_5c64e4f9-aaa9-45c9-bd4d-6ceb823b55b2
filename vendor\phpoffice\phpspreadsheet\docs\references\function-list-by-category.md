# Function list by category

## CATEGORY_CUBE

Excel Function      | PhpSpreadsheet Function
--------------------|-------------------------------------------
CUBEKPIMEMBER       | **Not yet Implemented**
CUBEMEMBER          | **Not yet Implemented**
CUBEMEMBERPROPERTY  | **Not yet Implemented**
CUBERANKEDMEMBER    | **Not yet Implemented**
CUBESET             | **Not yet Implemented**
CUBESETCOUNT        | **Not yet Implemented**
CUBEVALUE           | **Not yet Implemented**

## CATEGORY_DATABASE

Excel Function      | PhpSpreadsheet Function
--------------------|-------------------------------------------
DAVERAGE            | \PhpOffice\PhpSpreadsheet\Calculation\Database::DAVERAGE
DCOUNT              | \PhpOffice\PhpSpreadsheet\Calculation\Database::DCOUNT
DCOUNTA             | \PhpOffice\PhpSpreadsheet\Calculation\Database::DCOUNTA
DGET                | \PhpOffice\PhpSpreadsheet\Calculation\Database::DGET
DMAX                | \PhpOffice\PhpSpreadsheet\Calculation\Database::DMAX
DMIN                | \PhpOffice\PhpSpreadsheet\Calculation\Database::DMIN
DPRODUCT            | \PhpOffice\PhpSpreadsheet\Calculation\Database::DPRODUCT
DSTDEV              | \PhpOffice\PhpSpreadsheet\Calculation\Database::DSTDEV
DSTDEVP             | \PhpOffice\PhpSpreadsheet\Calculation\Database::DSTDEVP
DSUM                | \PhpOffice\PhpSpreadsheet\Calculation\Database::DSUM
DVAR                | \PhpOffice\PhpSpreadsheet\Calculation\Database::DVAR
DVARP               | \PhpOffice\PhpSpreadsheet\Calculation\Database::DVARP

## CATEGORY_DATE_AND_TIME

Excel Function      | PhpSpreadsheet Function
--------------------|-------------------------------------------
DATE                | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::DATE
DATEDIF             | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::DATEDIF
DATEVALUE           | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::DATEVALUE
DAY                 | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::DAYOFMONTH
DAYS                | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::DAYS
DAYS360             | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::DAYS360
EDATE               | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::EDATE
EOMONTH             | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::EOMONTH
HOUR                | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::HOUROFDAY
ISOWEEKNUM          | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::ISOWEEKNUM
MINUTE              | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::MINUTE
MONTH               | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::MONTHOFYEAR
NETWORKDAYS         | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::NETWORKDAYS
NETWORKDAYS.INTL    | **Not yet Implemented**
NOW                 | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::DATETIMENOW
SECOND              | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::SECOND
TIME                | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::TIME
TIMEVALUE           | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::TIMEVALUE
TODAY               | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::DATENOW
WEEKDAY             | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::WEEKDAY
WEEKNUM             | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::WEEKNUM
WORKDAY             | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::WORKDAY
WORKDAY.INTL        | **Not yet Implemented**
YEAR                | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::YEAR
YEARFRAC            | \PhpOffice\PhpSpreadsheet\Calculation\DateTime::YEARFRAC

## CATEGORY_ENGINEERING

Excel Function      | PhpSpreadsheet Function
--------------------|-------------------------------------------
BESSELI             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::BESSELI
BESSELJ             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::BESSELJ
BESSELK             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::BESSELK
BESSELY             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::BESSELY
BIN2DEC             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::BINTODEC
BIN2HEX             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::BINTOHEX
BIN2OCT             | \PhpOffice\PhpSpreadsheet\Calculation\Engineeri
BITAND              | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::BITAND
BITLSHIFT           | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::BITLSHIFT
BITOR               | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::BITOR
BITRSHIFT           | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::BITRSHIFT
BITXOR              | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::BITXOR
COMPLEX             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::COMPLEX
CONVERT             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::CONVERTUOM
DEC2BIN             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::DECTOBIN
DEC2HEX             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::DECTOHEX
DEC2OCT             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::DECTOOCT
DELTA               | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::DELTA
ERF                 | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::ERF
ERF.PRECISE         | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::ERFPRECISE
ERFC                | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::ERFC
ERFC.PRECISE        | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::ERFC
GESTEP              | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::GESTEP
HEX2BIN             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::HEXTOBIN
HEX2DEC             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::HEXTODEC
HEX2OCT             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::HEXTOOCT
IMABS               | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMABS
IMAGINARY           | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMAGINARY
IMARGUMENT          | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMARGUMENT
IMCONJUGATE         | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMCONJUGATE
IMCOS               | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMCOS
IMCOSH              | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMCOSH
IMCOT               | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMCOT
IMCSC               | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMCSC
IMCSCH              | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMCSCH
IMDIV               | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMDIV
IMEXP               | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMEXP
IMLN                | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMLN
IMLOG10             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMLOG10
IMLOG2              | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMLOG2
IMPOWER             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMPOWER
IMPRODUCT           | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMPRODUCT
IMREAL              | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMREAL
IMSEC               | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMSEC
IMSECH              | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMSECH
IMSIN               | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMSIN
IMSINH              | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMSINH
IMSQRT              | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMSQRT
IMSUB               | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMSUB
IMSUM               | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMSUM
IMTAN               | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::IMTAN
OCT2BIN             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::OCTTOBIN
OCT2DEC             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::OCTTODEC
OCT2HEX             | \PhpOffice\PhpSpreadsheet\Calculation\Engineering::OCTTOHEX

## CATEGORY_FINANCIAL

Excel Function      | PhpSpreadsheet Function
--------------------|-------------------------------------------
ACCRINT             | \PhpOffice\PhpSpreadsheet\Calculation\Financial::ACCRINT
ACCRINTM            | \PhpOffice\PhpSpreadsheet\Calculation\Financial::ACCRINTM
AMORDEGRC           | \PhpOffice\PhpSpreadsheet\Calculation\Financial::AMORDEGRC
AMORLINC            | \PhpOffice\PhpSpreadsheet\Calculation\Financial::AMORLINC
COUPDAYBS           | \PhpOffice\PhpSpreadsheet\Calculation\Financial::COUPDAYBS
COUPDAYS            | \PhpOffice\PhpSpreadsheet\Calculation\Financial::COUPDAYS
COUPDAYSNC          | \PhpOffice\PhpSpreadsheet\Calculation\Financial::COUPDAYSNC
COUPNCD             | \PhpOffice\PhpSpreadsheet\Calculation\Financial::COUPNCD
COUPNUM             | \PhpOffice\PhpSpreadsheet\Calculation\Financial::COUPNUM
COUPPCD             | \PhpOffice\PhpSpreadsheet\Calculation\Financial::COUPPCD
CUMIPMT             | \PhpOffice\PhpSpreadsheet\Calculation\Financial::CUMIPMT
CUMPRINC            | \PhpOffice\PhpSpreadsheet\Calculation\Financial::CUMPRINC
DB                  | \PhpOffice\PhpSpreadsheet\Calculation\Financial::DB
DDB                 | \PhpOffice\PhpSpreadsheet\Calculation\Financial::DDB
DISC                | \PhpOffice\PhpSpreadsheet\Calculation\Financial::DISC
DOLLARDE            | \PhpOffice\PhpSpreadsheet\Calculation\Financial::DOLLARDE
DOLLARFR            | \PhpOffice\PhpSpreadsheet\Calculation\Financial::DOLLARFR
DURATION            | **Not yet Implemented**
EFFECT              | \PhpOffice\PhpSpreadsheet\Calculation\Financial::EFFECT
FV                  | \PhpOffice\PhpSpreadsheet\Calculation\Financial::FV
FVSCHEDULE          | \PhpOffice\PhpSpreadsheet\Calculation\Financial::FVSCHEDULE
INTRATE             | \PhpOffice\PhpSpreadsheet\Calculation\Financial::INTRATE
IPMT                | \PhpOffice\PhpSpreadsheet\Calculation\Financial::IPMT
IRR                 | \PhpOffice\PhpSpreadsheet\Calculation\Financial::IRR
ISPMT               | \PhpOffice\PhpSpreadsheet\Calculation\Financial::ISPMT
MDURATION           | **Not yet Implemented**
MIRR                | \PhpOffice\PhpSpreadsheet\Calculation\Financial::MIRR
NOMINAL             | \PhpOffice\PhpSpreadsheet\Calculation\Financial::NOMINAL
NPER                | \PhpOffice\PhpSpreadsheet\Calculation\Financial::NPER
NPV                 | \PhpOffice\PhpSpreadsheet\Calculation\Financial::NPV
ODDFPRICE           | **Not yet Implemented**
ODDFYIELD           | **Not yet Implemented**
ODDLPRICE           | **Not yet Implemented**
ODDLYIELD           | **Not yet Implemented**
PDURATION           | \PhpOffice\PhpSpreadsheet\Calculation\Financial::PDURATION
PMT                 | \PhpOffice\PhpSpreadsheet\Calculation\Financial::PMT
PPMT                | \PhpOffice\PhpSpreadsheet\Calculation\Financial::PPMT
PRICE               | \PhpOffice\PhpSpreadsheet\Calculation\Financial::PRICE
PRICEDISC           | \PhpOffice\PhpSpreadsheet\Calculation\Financial::PRICEDISC
PRICEMAT            | \PhpOffice\PhpSpreadsheet\Calculation\Financial::PRICEMAT
PV                  | \PhpOffice\PhpSpreadsheet\Calculation\Financial::PV
RATE                | \PhpOffice\PhpSpreadsheet\Calculation\Financial::RATE
RECEIVED            | \PhpOffice\PhpSpreadsheet\Calculation\Financial::RECEIVED
RRI                 | \PhpOffice\PhpSpreadsheet\Calculation\Financial::RRI
SLN                 | \PhpOffice\PhpSpreadsheet\Calculation\Financial::SLN
SYD                 | \PhpOffice\PhpSpreadsheet\Calculation\Financial::SYD
TBILLEQ             | \PhpOffice\PhpSpreadsheet\Calculation\Financial::TBILLEQ
TBILLPRICE          | \PhpOffice\PhpSpreadsheet\Calculation\Financial::TBILLPRICE
TBILLYIELD          | \PhpOffice\PhpSpreadsheet\Calculation\Financial::TBILLYIELD
USDOLLAR            | **Not yet Implemented**
VDB                 | **Not yet Implemented**
XIRR                | \PhpOffice\PhpSpreadsheet\Calculation\Financial::XIRR
XNPV                | \PhpOffice\PhpSpreadsheet\Calculation\Financial::XNPV
YIELD               | **Not yet Implemented**
YIELDDISC           | \PhpOffice\PhpSpreadsheet\Calculation\Financial::YIELDDISC
YIELDMAT            | \PhpOffice\PhpSpreadsheet\Calculation\Financial::YIELDMAT

## CATEGORY_INFORMATION

Excel Function      | PhpSpreadsheet Function
--------------------|-------------------------------------------
CELL                | **Not yet Implemented**
ERROR.TYPE          | \PhpOffice\PhpSpreadsheet\Calculation\Functions::ERROR_TYPE
INFO                | **Not yet Implemented**
ISBLANK             | \PhpOffice\PhpSpreadsheet\Calculation\Functions::IS_BLANK
ISERR               | \PhpOffice\PhpSpreadsheet\Calculation\Functions::IS_ERR
ISERROR             | \PhpOffice\PhpSpreadsheet\Calculation\Functions::IS_ERROR
ISEVEN              | \PhpOffice\PhpSpreadsheet\Calculation\Functions::IS_EVEN
ISFORMULA           | \PhpOffice\PhpSpreadsheet\Calculation\Functions::ISFORMULA
ISLOGICAL           | \PhpOffice\PhpSpreadsheet\Calculation\Functions::IS_LOGICAL
ISNA                | \PhpOffice\PhpSpreadsheet\Calculation\Functions::IS_NA
ISNONTEXT           | \PhpOffice\PhpSpreadsheet\Calculation\Functions::IS_NONTEXT
ISNUMBER            | \PhpOffice\PhpSpreadsheet\Calculation\Functions::IS_NUMBER
ISODD               | \PhpOffice\PhpSpreadsheet\Calculation\Functions::IS_ODD
ISREF               | **Not yet Implemented**
ISTEXT              | \PhpOffice\PhpSpreadsheet\Calculation\Functions::IS_TEXT
N                   | \PhpOffice\PhpSpreadsheet\Calculation\Functions::N
NA                  | \PhpOffice\PhpSpreadsheet\Calculation\Functions::NA
TYPE                | \PhpOffice\PhpSpreadsheet\Calculation\Functions::TYPE

## CATEGORY_LOGICAL

Excel Function      | PhpSpreadsheet Function
--------------------|-------------------------------------------
AND                 | \PhpOffice\PhpSpreadsheet\Calculation\Logical::logicalAnd
FALSE               | \PhpOffice\PhpSpreadsheet\Calculation\Logical::FALSE
IF                  | \PhpOffice\PhpSpreadsheet\Calculation\Logical::STATEMENT_IF
IFERROR             | \PhpOffice\PhpSpreadsheet\Calculation\Logical::IFERROR
IFNA                | \PhpOffice\PhpSpreadsheet\Calculation\Logical::IFNA
NOT                 | \PhpOffice\PhpSpreadsheet\Calculation\Logical::NOT
OR                  | \PhpOffice\PhpSpreadsheet\Calculation\Logical::logicalOr
TRUE                | \PhpOffice\PhpSpreadsheet\Calculation\Logical::TRUE
XOR                 | \PhpOffice\PhpSpreadsheet\Calculation\Logical::logicalXor

## CATEGORY_LOOKUP_AND_REFERENCE

Excel Function      | PhpSpreadsheet Function
--------------------|-------------------------------------------
ADDRESS             | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::CELL_ADDRESS
AREAS               | **Not yet Implemented**
CHOOSE              | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::CHOOSE
COLUMN              | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::COLUMN
COLUMNS             | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::COLUMNS
FORMULATEXT         | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::FORMULATEXT
GETPIVOTDATA        | **Not yet Implemented**
HLOOKUP             | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::HLOOKUP
HYPERLINK           | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::HYPERLINK
INDEX               | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::INDEX
INDIRECT            | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::INDIRECT
LOOKUP              | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::LOOKUP
MATCH               | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::MATCH
OFFSET              | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::OFFSET
ROW                 | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::ROW
ROWS                | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::ROWS
RTD                 | **Not yet Implemented**
TRANSPOSE           | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::TRANSPOSE
VLOOKUP             | \PhpOffice\PhpSpreadsheet\Calculation\LookupRef::VLOOKUP

## CATEGORY_MATH_AND_TRIG

Excel Function      | PhpSpreadsheet Function
--------------------|-------------------------------------------
ABS                 | abs
ACOS                | acos
ACOSH               | acosh
ACOT                | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::ACOT
ACOTH               | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::ACOTH
ASIN                | asin
ASINH               | asinh
ATAN                | atan
ATAN2               | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::REVERSE_ATAN2
ATANH               | atanh
CEILING             | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::CEILING
COMBIN              | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::COMBIN
COS                 | cos
COSH                | cosh
COT                 | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::COT
COTH                | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::COTH
CSC                 | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::CSC
CSCH                | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::CSCH
DEGREES             | rad2deg
EVEN                | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::EVEN
EXP                 | exp
FACT                | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::FACT
FACTDOUBLE          | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::FACTDOUBLE
FLOOR               | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::FLOOR
GCD                 | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::GCD
INT                 | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::INT
LCM                 | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::LCM
LN                  | log
LOG                 | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::LOG_BASE
LOG10               | log10
MDETERM             | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::MDETERM
MINVERSE            | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::MINVERSE
MMULT               | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::MMULT
MOD                 | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::MOD
MROUND              | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::MROUND
MULTINOMIAL         | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::MULTINOMIAL
ODD                 | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::ODD
PI                  | pi
POWER               | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::POWER
PRODUCT             | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::PRODUCT
QUOTIENT            | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::QUOTIENT
RADIANS             | deg2rad
RAND                | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::RAND
RANDBETWEEN         | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::RAND
ROMAN               | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::ROMAN
ROUND               | round
ROUNDDOWN           | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::ROUNDDOWN
ROUNDUP             | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::ROUNDUP
SEC                 | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::SEC
SECH                | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::SECH
SERIESSUM           | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::SERIESSUM
SIGN                | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::SIGN
SIN                 | sin
SINH                | sinh
SQRT                | sqrt
SQRTPI              | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::SQRTPI
SUBTOTAL            | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::SUBTOTAL
SUM                 | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::SUM
SUMIF               | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::SUMIF
SUMIFS              | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::SUMIFS
SUMPRODUCT          | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::SUMPRODUCT
SUMSQ               | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::SUMSQ
SUMX2MY2            | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::SUMX2MY2
SUMX2PY2            | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::SUMX2PY2
SUMXMY2             | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::SUMXMY2
TAN                 | tan
TANH                | tanh
TRUNC               | \PhpOffice\PhpSpreadsheet\Calculation\MathTrig::TRUNC

## CATEGORY_STATISTICAL

Excel Function      | PhpSpreadsheet Function
--------------------|-------------------------------------------
AVEDEV              | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::AVEDEV
AVERAGE             | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::AVERAGE
AVERAGEA            | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::AVERAGEA
AVERAGEIF           | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::AVERAGEIF
AVERAGEIFS          | **Not yet Implemented**
BETADIST            | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::BETADIST
BETAINV             | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::BETAINV
BINOMDIST           | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::BINOMDIST
CHIDIST             | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::CHIDIST
CHIINV              | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::CHIINV
CHITEST             | **Not yet Implemented**
CONFIDENCE          | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::CONFIDENCE
CORREL              | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::CORREL
COUNT               | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::COUNT
COUNTA              | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::COUNTA
COUNTBLANK          | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::COUNTBLANK
COUNTIF             | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::COUNTIF
COUNTIFS            | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::COUNTIFS
COVAR               | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::COVAR
CRITBINOM           | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::CRITBINOM
DEVSQ               | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::DEVSQ
EXPONDIST           | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::EXPONDIST
FDIST               | **Not yet Implemented**
FINV                | **Not yet Implemented**
FISHER              | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::FISHER
FISHERINV           | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::FISHERINV
FORECAST            | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::FORECAST
FREQUENCY           | **Not yet Implemented**
FTEST               | **Not yet Implemented**
GAMMADIST           | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::GAMMADIST
GAMMAINV            | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::GAMMAINV
GAMMALN             | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::GAMMALN
GEOMEAN             | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::GEOMEAN
GROWTH              | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::GROWTH
HARMEAN             | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::HARMEAN
HYPGEOMDIST         | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::HYPGEOMDIST
INTERCEPT           | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::INTERCEPT
KURT                | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::KURT
LARGE               | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::LARGE
LINEST              | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::LINEST
LOGEST              | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::LOGEST
LOGINV              | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::LOGINV
LOGNORMDIST         | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::LOGNORMDIST
MAX                 | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::MAX
MAXA                | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::MAXA
MAXIFS              | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::MAXIFS
MEDIAN              | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::MEDIAN
MEDIANIF            | **Not yet Implemented**
MIN                 | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::MIN
MINA                | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::MINA
MINIFS              | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::MINIFS
MODE                | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::MODE
MODE.SNGL           | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::MODE
NEGBINOMDIST        | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::NEGBINOMDIST
NORMDIST            | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::NORMDIST
NORMINV             | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::NORMINV
NORMSDIST           | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::NORMSDIST
NORMSINV            | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::NORMSINV
PEARSON             | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::CORREL
PERCENTILE          | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::PERCENTILE
PERCENTRANK         | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::PERCENTRANK
PERMUT              | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::PERMUT
POISSON             | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::POISSON
PROB                | **Not yet Implemented**
QUARTILE            | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::QUARTILE
RANK                | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::RANK
RSQ                 | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::RSQ
SKEW                | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::SKEW
SLOPE               | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::SLOPE
SMALL               | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::SMALL
STANDARDIZE         | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::STANDARDIZE
STDEV               | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::STDEV
STDEV.S             | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::STDEV
STDEV.P             | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::STDEVP
STDEVA              | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::STDEVA
STDEVP              | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::STDEVP
STDEVPA             | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::STDEVPA
STEYX               | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::STEYX
TDIST               | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::TDIST
TINV                | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::TINV
TREND               | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::TREND
TRIMMEAN            | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::TRIMMEAN
TTEST               | **Not yet Implemented**
VAR                 | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::VARFunc
VAR.P               | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::VARP
VAR.S               | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::VARFunc
VARA                | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::VARA
VARP                | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::VARP
VARPA               | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::VARPA
WEIBULL             | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::WEIBULL
ZTEST               | \PhpOffice\PhpSpreadsheet\Calculation\Statistical::ZTEST

## CATEGORY_TEXT_AND_DATA

Excel Function      | PhpSpreadsheet Function
--------------------|-------------------------------------------
ASC                 | **Not yet Implemented**
BAHTTEXT            | **Not yet Implemented**
CHAR                | \PhpOffice\PhpSpreadsheet\Calculation\TextData::CHARACTER
CLEAN               | \PhpOffice\PhpSpreadsheet\Calculation\TextData::TRIMNONPRINTABLE
CODE                | \PhpOffice\PhpSpreadsheet\Calculation\TextData::ASCIICODE
CONCAT              | \PhpOffice\PhpSpreadsheet\Calculation\TextData::CONCATENATE
CONCATENATE         | \PhpOffice\PhpSpreadsheet\Calculation\TextData::CONCATENATE
DOLLAR              | \PhpOffice\PhpSpreadsheet\Calculation\TextData::DOLLAR
EXACT               | \PhpOffice\PhpSpreadsheet\Calculation\TextData::EXACT
FIND                | \PhpOffice\PhpSpreadsheet\Calculation\TextData::SEARCHSENSITIVE
FINDB               | \PhpOffice\PhpSpreadsheet\Calculation\TextData::SEARCHSENSITIVE
FIXED               | \PhpOffice\PhpSpreadsheet\Calculation\TextData::FIXEDFORMAT
JIS                 | **Not yet Implemented**
LEFT                | \PhpOffice\PhpSpreadsheet\Calculation\TextData::LEFT
LEFTB               | \PhpOffice\PhpSpreadsheet\Calculation\TextData::LEFT
LEN                 | \PhpOffice\PhpSpreadsheet\Calculation\TextData::STRINGLENGTH
LENB                | \PhpOffice\PhpSpreadsheet\Calculation\TextData::STRINGLENGTH
LOWER               | \PhpOffice\PhpSpreadsheet\Calculation\TextData::LOWERCASE
MID                 | \PhpOffice\PhpSpreadsheet\Calculation\TextData::MID
MIDB                | \PhpOffice\PhpSpreadsheet\Calculation\TextData::MID
NUMBERVALUE         | \PhpOffice\PhpSpreadsheet\Calculation\TextData::NUMBERVALUE
PHONETIC            | **Not yet Implemented**
PROPER              | \PhpOffice\PhpSpreadsheet\Calculation\TextData::PROPERCASE
REPLACE             | \PhpOffice\PhpSpreadsheet\Calculation\TextData::REPLACE
REPLACEB            | \PhpOffice\PhpSpreadsheet\Calculation\TextData::REPLACE
REPT                | str_repeat
RIGHT               | \PhpOffice\PhpSpreadsheet\Calculation\TextData::RIGHT
RIGHTB              | \PhpOffice\PhpSpreadsheet\Calculation\TextData::RIGHT
SEARCH              | \PhpOffice\PhpSpreadsheet\Calculation\TextData::SEARCHINSENSITIVE
SEARCHB             | \PhpOffice\PhpSpreadsheet\Calculation\TextData::SEARCHINSENSITIVE
SUBSTITUTE          | \PhpOffice\PhpSpreadsheet\Calculation\TextData::SUBSTITUTE
T                   | \PhpOffice\PhpSpreadsheet\Calculation\TextData::RETURNSTRING
TEXT                | \PhpOffice\PhpSpreadsheet\Calculation\TextData::TEXTFORMAT
TEXTJOIN            | \PhpOffice\PhpSpreadsheet\Calculation\TextData::TEXTJOIN
TRIM                | \PhpOffice\PhpSpreadsheet\Calculation\TextData::TRIMSPACES
UNICHAR             | \PhpOffice\PhpSpreadsheet\Calculation\TextData::CHARACTER
UNICODE             | \PhpOffice\PhpSpreadsheet\Calculation\TextData::ASCIICODE
UPPER               | \PhpOffice\PhpSpreadsheet\Calculation\TextData::UPPERCASE
VALUE               | \PhpOffice\PhpSpreadsheet\Calculation\TextData::VALUE
