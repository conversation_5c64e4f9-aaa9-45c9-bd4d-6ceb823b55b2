<?php
/**
 * MongoDB Manager Classes
 * Asset Management System - MongoDB Version
 */

require_once 'mongodb_config.php';

/**
 * Base MongoDB Manager Class
 */
abstract class BaseMongoManager {
    protected $db;
    protected $collection;
    protected $collectionName;
    
    public function __construct() {
        $this->db = getMongoDatabase();
        $this->collection = $this->db->selectCollection($this->collectionName);
    }
    
    public function findById($id) {
        try {
            $objectId = createMongoId($id);
            return $this->collection->findOne(['_id' => $objectId]);
        } catch (Exception $e) {
            return null;
        }
    }
    
    public function findAll($filter = [], $options = []) {
        return $this->collection->find($filter, $options)->toArray();
    }
    
    public function findOne($filter = []) {
        return $this->collection->findOne($filter);
    }
    
    public function insert($data) {
        $data['_id'] = createMongoId();
        $data['created_date'] = createMongoDate();
        $data['updated_date'] = createMongoDate();
        
        $result = $this->collection->insertOne($data);
        return $result->getInsertedId();
    }
    
    public function update($id, $data) {
        $objectId = createMongoId($id);
        $data['updated_date'] = createMongoDate();
        
        $result = $this->collection->updateOne(
            ['_id' => $objectId],
            ['$set' => $data]
        );
        
        return $result->getModifiedCount() > 0;
    }
    
    public function delete($id) {
        $objectId = createMongoId($id);
        $result = $this->collection->deleteOne(['_id' => $objectId]);
        return $result->getDeletedCount() > 0;
    }
    
    public function count($filter = []) {
        return $this->collection->countDocuments($filter);
    }
    
    public function paginate($filter = [], $page = 1, $limit = 20, $sort = []) {
        if (empty($sort)) {
            $sort = ['_id' => -1];
        }
        
        return createMongoPagination($this->collection, $filter, [
            'page' => $page,
            'limit' => $limit,
            'sort' => $sort
        ]);
    }
}

/**
 * User Manager Class
 */
class MongoUserManager extends BaseMongoManager {
    protected $collectionName = COLLECTION_USERS;
    
    public function createUser($userData) {
        // ตรวจสอบ username ซ้ำ
        $existingUser = $this->findOne(['username' => $userData['username']]);
        if ($existingUser) {
            throw new Exception('Username already exists');
        }
        
        // ตรวจสอบ email ซ้ำ
        if (!empty($userData['email'])) {
            $existingEmail = $this->findOne(['email' => $userData['email']]);
            if ($existingEmail) {
                throw new Exception('Email already exists');
            }
        }
        
        // Hash password
        $userData['password'] = password_hash($userData['password'], PASSWORD_DEFAULT);
        
        // Set default values
        $userData['status'] = $userData['status'] ?? 'Active';
        $userData['role'] = $userData['role'] ?? 'User';
        
        return $this->insert($userData);
    }
    
    public function authenticate($username, $password) {
        $user = $this->findOne(['username' => $username, 'status' => 'Active']);
        
        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }
        
        return false;
    }
    
    public function updatePassword($userId, $newPassword) {
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        return $this->update($userId, ['password' => $hashedPassword]);
    }
    
    public function getUsersByRole($role) {
        return $this->findAll(['role' => $role, 'status' => 'Active']);
    }
    
    public function searchUsers($searchText, $page = 1, $limit = 20) {
        $filter = [];
        
        if (!empty($searchText)) {
            $filter['$or'] = [
                ['username' => new MongoDB\BSON\Regex($searchText, 'i')],
                ['full_name' => new MongoDB\BSON\Regex($searchText, 'i')],
                ['email' => new MongoDB\BSON\Regex($searchText, 'i')]
            ];
        }
        
        return $this->paginate($filter, $page, $limit, ['username' => 1]);
    }
}

/**
 * Asset Manager Class
 */
class MongoAssetManager extends BaseMongoManager {
    protected $collectionName = COLLECTION_ASSETS;
    
    public function createAsset($assetData) {
        // เพิ่ม created_by และ updated_by
        $assetData['created_by'] = $assetData['created_by'] ?? 'System';
        $assetData['updated_by'] = $assetData['updated_by'] ?? 'System';
        
        // แปลง warranty_expire เป็น MongoDB Date
        if (!empty($assetData['warranty_expire'])) {
            $assetData['warranty_expire'] = createMongoDate($assetData['warranty_expire']);
        }
        
        return $this->insert($assetData);
    }
    
    public function updateAsset($assetId, $assetData, $updatedBy = 'System') {
        // เพิ่ม updated_by
        $assetData['updated_by'] = $updatedBy;
        
        // แปลง warranty_expire เป็น MongoDB Date
        if (!empty($assetData['warranty_expire'])) {
            $assetData['warranty_expire'] = createMongoDate($assetData['warranty_expire']);
        }
        
        return $this->update($assetId, $assetData);
    }
    
    public function searchAssets($searchText = '', $filters = [], $page = 1, $limit = 20) {
        $filter = [];
        
        // Text search
        if (!empty($searchText)) {
            $filter['$text'] = ['$search' => $searchText];
        }
        
        // Apply filters
        if (!empty($filters['type'])) {
            $filter['type'] = $filters['type'];
        }
        
        if (!empty($filters['brand'])) {
            $filter['brand'] = $filters['brand'];
        }
        
        if (!empty($filters['status'])) {
            $filter['status'] = $filters['status'];
        }
        
        if (!empty($filters['department'])) {
            $filter['department'] = $filters['department'];
        }
        
        // Date range filter
        if (!empty($filters['date_from']) || !empty($filters['date_to'])) {
            $dateFilter = [];
            
            if (!empty($filters['date_from'])) {
                $dateFilter['$gte'] = createMongoDate($filters['date_from']);
            }
            
            if (!empty($filters['date_to'])) {
                $dateFilter['$lte'] = createMongoDate($filters['date_to']);
            }
            
            $filter['created_date'] = $dateFilter;
        }
        
        $sort = ['created_date' => -1];
        if (!empty($searchText)) {
            $sort = ['score' => ['$meta' => 'textScore']];
        }
        
        return $this->paginate($filter, $page, $limit, $sort);
    }
    
    public function getAssetsByDepartment($department) {
        return $this->findAll(['department' => $department], ['sort' => ['created_date' => -1]]);
    }
    
    public function getAssetsByStatus($status) {
        return $this->findAll(['status' => $status], ['sort' => ['created_date' => -1]]);
    }
    
    public function getExpiringWarranties($days = 90) {
        $futureDate = new DateTime();
        $futureDate->add(new DateInterval("P{$days}D"));
        
        $filter = [
            'warranty_expire' => [
                '$gte' => createMongoDate(),
                '$lte' => createMongoDate($futureDate)
            ]
        ];
        
        return $this->findAll($filter, ['sort' => ['warranty_expire' => 1]]);
    }
    
    public function getAssetStatistics() {
        $pipeline = [
            [
                '$group' => [
                    '_id' => '$status',
                    'count' => ['$sum' => 1]
                ]
            ]
        ];
        
        $statusStats = $this->collection->aggregate($pipeline)->toArray();
        
        $pipeline = [
            [
                '$group' => [
                    '_id' => '$department',
                    'count' => ['$sum' => 1]
                ]
            ],
            ['$sort' => ['count' => -1]]
        ];
        
        $departmentStats = $this->collection->aggregate($pipeline)->toArray();
        
        return [
            'total' => $this->count(),
            'by_status' => $statusStats,
            'by_department' => $departmentStats
        ];
    }
}

/**
 * Asset Log Manager Class
 */
class MongoAssetLogManager extends BaseMongoManager {
    protected $collectionName = COLLECTION_ASSET_LOGS;
    
    public function logAssetAction($assetId, $action, $oldValues = null, $newValues = null, $changedBy = 'System', $ipAddress = null, $userAgent = null) {
        $logData = [
            'asset_id' => createMongoId($assetId),
            'action' => $action,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'changed_by' => $changedBy,
            'changed_date' => createMongoDate(),
            'ip_address' => $ipAddress ?? $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $userAgent ?? $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        return $this->insert($logData);
    }
    
    public function getAssetHistory($assetId, $page = 1, $limit = 20) {
        $objectId = createMongoId($assetId);
        $filter = ['asset_id' => $objectId];
        
        return $this->paginate($filter, $page, $limit, ['changed_date' => -1]);
    }
    
    public function getRecentLogs($limit = 50) {
        return $this->findAll([], [
            'sort' => ['changed_date' => -1],
            'limit' => $limit
        ]);
    }
    
    public function getLogsByUser($username, $page = 1, $limit = 20) {
        $filter = ['changed_by' => $username];
        return $this->paginate($filter, $page, $limit, ['changed_date' => -1]);
    }
}

/**
 * Session Manager Class
 */
class MongoSessionManager extends BaseMongoManager {
    protected $collectionName = COLLECTION_SESSIONS;
    
    public function createSession($userId, $sessionData = []) {
        $sessionId = bin2hex(random_bytes(32));
        $expiresAt = new DateTime();
        $expiresAt->add(new DateInterval('P1D')); // 1 day
        
        $sessionDoc = [
            'session_id' => $sessionId,
            'user_id' => createMongoId($userId),
            'data' => $sessionData,
            'expires_at' => createMongoDate($expiresAt),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        $this->insert($sessionDoc);
        return $sessionId;
    }
    
    public function getSession($sessionId) {
        $session = $this->findOne([
            'session_id' => $sessionId,
            'expires_at' => ['$gt' => createMongoDate()]
        ]);
        
        return $session;
    }
    
    public function updateSession($sessionId, $data) {
        $expiresAt = new DateTime();
        $expiresAt->add(new DateInterval('P1D'));
        
        $result = $this->collection->updateOne(
            ['session_id' => $sessionId],
            [
                '$set' => [
                    'data' => $data,
                    'expires_at' => createMongoDate($expiresAt),
                    'updated_date' => createMongoDate()
                ]
            ]
        );
        
        return $result->getModifiedCount() > 0;
    }
    
    public function deleteSession($sessionId) {
        $result = $this->collection->deleteOne(['session_id' => $sessionId]);
        return $result->getDeletedCount() > 0;
    }
    
    public function cleanExpiredSessions() {
        $result = $this->collection->deleteMany([
            'expires_at' => ['$lt' => createMongoDate()]
        ]);
        
        return $result->getDeletedCount();
    }
}

// Create global instances
$mongoUserManager = new MongoUserManager();
$mongoAssetManager = new MongoAssetManager();
$mongoAssetLogManager = new MongoAssetLogManager();
$mongoSessionManager = new MongoSessionManager();
?>
