<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// ตรวจสอบการล็อกอิน
requireLogin();

// สร้าง AssetManager instance
$assetManager = new AssetManager($pdo);

// รับค่าการกรอง (ถ้ามี)
$search = $_GET['search'] ?? '';
$filter_type = $_GET['filter_type'] ?? '';
$filter_brand = $_GET['filter_brand'] ?? '';
$filter_department = $_GET['filter_department'] ?? '';
$filter_status = $_GET['filter_status'] ?? '';
$filter_os = $_GET['filter_os'] ?? '';

// ดึงข้อมูล assets ทั้งหมดตามเงื่อนไขการกรอง
$assets = $assetManager->getAllAssets($search, $filter_type, $filter_brand, $filter_status, $filter_os, $filter_department);

// สร้าง Spreadsheet object
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// ตั้งชื่อ worksheet
$sheet->setTitle('Asset Report');

// ตั้งค่า metadata
$spreadsheet->getProperties()
    ->setCreator('Asset Management System')
    ->setLastModifiedBy(getCurrentUserFullName())
    ->setTitle('รายงาน Asset Management System')
    ->setSubject('Asset Report')
    ->setDescription('รายงานข้อมูล Asset ที่ส่งออกจากระบบจัดการ Asset')
    ->setKeywords('asset management report excel')
    ->setCategory('Report');

// สร้าง Header
$sheet->setCellValue('A1', 'รายงานระบบจัดการ Asset');
$sheet->mergeCells('A1:L1');
$sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
$sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

// ข้อมูลรายงาน
$sheet->setCellValue('A2', 'วันที่สร้าง: ' . date('d/m/Y H:i:s'));
$sheet->setCellValue('A3', 'ผู้สร้าง: ' . getCurrentUserFullName());
$sheet->setCellValue('A4', 'จำนวนรายการ: ' . count($assets) . ' รายการ');

// เงื่อนไขการกรอง (ถ้ามี)
$row = 5;
$hasFilters = false;
$filterText = '';

if (!empty($search)) {
    $filterText .= 'ค้นหา: "' . $search . '"  ';
    $hasFilters = true;
}
if (!empty($filter_type)) {
    $filterText .= 'ประเภท: ' . $filter_type . '  ';
    $hasFilters = true;
}
if (!empty($filter_brand)) {
    $filterText .= 'ยี่ห้อ: ' . $filter_brand . '  ';
    $hasFilters = true;
}
if (!empty($filter_department)) {
    $filterText .= 'แผนก: ' . $filter_department . '  ';
    $hasFilters = true;
}
if (!empty($filter_status)) {
    $filterText .= 'สถานะ: ' . $filter_status . '  ';
    $hasFilters = true;
}
if (!empty($filter_os)) {
    $filterText .= 'OS: ' . $filter_os . '  ';
    $hasFilters = true;
}

if ($hasFilters) {
    $sheet->setCellValue('A' . $row, 'เงื่อนไขการกรอง: ' . $filterText);
    $row++;
}

$row++; // เว้นบรรทัด

// Header ของตาราง
$headers = [
    'A' => 'ลำดับ',
    'B' => 'แผนก',
    'C' => 'Asset ID',
    'D' => 'Tag',
    'E' => 'ประเภท',
    'F' => 'ยี่ห้อ',
    'G' => 'รุ่น',
    'H' => 'Hostname',
    'I' => 'OS',
    'J' => 'Serial Number',
    'K' => 'สถานะ',
    'L' => 'วันที่หมดประกัน'
];

foreach ($headers as $col => $header) {
    $sheet->setCellValue($col . $row, $header);
}

// จัดรูปแบบ header
$headerRange = 'A' . $row . ':L' . $row;
$sheet->getStyle($headerRange)->getFont()->setBold(true);
$sheet->getStyle($headerRange)->getFill()
    ->setFillType(Fill::FILL_SOLID)
    ->getStartColor()->setARGB('FF4472C4');
$sheet->getStyle($headerRange)->getFont()->getColor()->setARGB('FFFFFFFF');
$sheet->getStyle($headerRange)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

// เพิ่มข้อมูล
$dataStartRow = $row + 1;
$currentRow = $dataStartRow;

if (!empty($assets)) {
    $sequenceNumber = 1;
    foreach ($assets as $asset) {
        // Format warranty date
        $warranty_exp = '';
        if (!empty($asset['warranty_expire'])) {
            $warranty_exp = date('d/m/Y', strtotime($asset['warranty_expire']));
        }
        
        $sheet->setCellValue('A' . $currentRow, $sequenceNumber);
        $sheet->setCellValue('B' . $currentRow, $asset['department'] ?? '');
        $sheet->setCellValue('C' . $currentRow, $asset['asset_id'] ?? '');
        $sheet->setCellValue('D' . $currentRow, $asset['tag'] ?? '');
        $sheet->setCellValue('E' . $currentRow, $asset['type'] ?? '');
        $sheet->setCellValue('F' . $currentRow, $asset['brand'] ?? '');
        $sheet->setCellValue('G' . $currentRow, $asset['model'] ?? '');
        $sheet->setCellValue('H' . $currentRow, $asset['hostname'] ?? '');
        $sheet->setCellValue('I' . $currentRow, $asset['operating_system'] ?? '');
        $sheet->setCellValue('J' . $currentRow, $asset['serial_number'] ?? '');
        $sheet->setCellValue('K' . $currentRow, $asset['status'] ?? '');
        $sheet->setCellValue('L' . $currentRow, $warranty_exp);
        
        // จัดรูปแบบสถานะ
        $status = $asset['status'] ?? '';
        if ($status == 'ใช้งาน') {
            $sheet->getStyle('K' . $currentRow)->getFont()->getColor()->setARGB('FF28A745');
        } elseif ($status == 'ชำรุด') {
            $sheet->getStyle('K' . $currentRow)->getFont()->getColor()->setARGB('FFDC3545');
        } elseif ($status == 'สำรอง') {
            $sheet->getStyle('K' . $currentRow)->getFont()->getColor()->setARGB('FFFFC107');
        }
        
        $currentRow++;
        $sequenceNumber++;
    }
} else {
    $sheet->setCellValue('A' . $currentRow, 'ไม่พบข้อมูล Asset ที่ตรงกับเงื่อนไขการค้นหา');
    $sheet->mergeCells('A' . $currentRow . ':L' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    $sheet->getStyle('A' . $currentRow)->getFont()->setItalic(true);
    $currentRow++;
}

// จัดรูปแบบตาราง
if (!empty($assets)) {
    $dataRange = 'A' . $row . ':L' . ($currentRow - 1);
    $sheet->getStyle($dataRange)->getBorders()->getAllBorders()
        ->setBorderStyle(Border::BORDER_THIN);
    
    // Zebra striping
    for ($i = $dataStartRow; $i < $currentRow; $i += 2) {
        $sheet->getStyle('A' . $i . ':L' . $i)->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setARGB('FFF8F9FA');
    }
}

// ปรับความกว้างคอลัมน์อัตโนมัติ
foreach (range('A', 'L') as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}

// จัดตำแหน่งข้อมูล
$sheet->getStyle('A' . $dataStartRow . ':A' . ($currentRow - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // ลำดับ
$sheet->getStyle('K' . $dataStartRow . ':K' . ($currentRow - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // สถานะ
$sheet->getStyle('L' . $dataStartRow . ':L' . ($currentRow - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // วันที่

// สร้างไฟล์ Excel
$writer = new Xlsx($spreadsheet);

// ตั้งชื่อไฟล์
$filename = 'asset_report_' . date('Y-m-d_H-i-s') . '.xlsx';

// ส่งออกไฟล์
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="' . $filename . '"');
header('Cache-Control: max-age=0');
header('Cache-Control: max-age=1');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
header('Cache-Control: cache, must-revalidate');
header('Pragma: public');

$writer->save('php://output');
exit;
?>
