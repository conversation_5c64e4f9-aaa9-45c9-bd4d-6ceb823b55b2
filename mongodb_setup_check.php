<?php
/**
 * MongoDB Setup Check
 * Asset Management System - MongoDB Installation Checker
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='th'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>MongoDB Setup Check - Asset Management</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; border-radius: 15px; text-align: center; margin-bottom: 30px; }
        .check-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; margin: 20px 0; }
        .check-card { background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .status { padding: 8px 15px; border-radius: 20px; font-weight: bold; font-size: 0.9em; margin: 5px 0; display: inline-block; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .btn { display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn:hover { background: #218838; }
        .btn.danger { background: #dc3545; }
        .btn.primary { background: #007bff; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; border-left: 4px solid #28a745; }
        .install-guide { background: white; padding: 25px; border-radius: 10px; margin: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body>";

echo "<div class='header'>
    <h1>🍃 MongoDB Setup Check</h1>
    <p>Asset Management System - MongoDB Installation & Configuration Checker</p>
</div>";

// Check functions
function checkPHPExtension() {
    if (extension_loaded('mongodb')) {
        echo "<div class='status success'>✅ MongoDB PHP extension is loaded</div>";
        
        // Get extension info
        $version = phpversion('mongodb');
        echo "<div class='status info'>📊 Extension version: $version</div>";
        
        return true;
    } else {
        echo "<div class='status error'>❌ MongoDB PHP extension is NOT loaded</div>";
        return false;
    }
}

function checkComposerPackage() {
    if (file_exists('vendor/mongodb/mongodb/src/Client.php')) {
        echo "<div class='status success'>✅ MongoDB Composer package is installed</div>";
        
        // Check composer.json
        if (file_exists('composer.json')) {
            $composer = json_decode(file_get_contents('composer.json'), true);
            if (isset($composer['require']['mongodb/mongodb'])) {
                echo "<div class='status info'>📦 Package version: {$composer['require']['mongodb/mongodb']}</div>";
            }
        }
        
        return true;
    } else {
        echo "<div class='status error'>❌ MongoDB Composer package is NOT installed</div>";
        return false;
    }
}

function checkMongoDBService() {
    // Check if MongoDB is running on default port
    $connection = @fsockopen('localhost', 27017, $errno, $errstr, 5);
    if ($connection) {
        echo "<div class='status success'>✅ MongoDB service is running on port 27017</div>";
        fclose($connection);
        return true;
    } else {
        echo "<div class='status error'>❌ MongoDB service is NOT running on port 27017</div>";
        echo "<div class='status warning'>⚠️ Error: $errstr ($errno)</div>";
        return false;
    }
}

function testMongoDBConnection() {
    try {
        // Try to connect without authentication
        $client = new MongoDB\Client("mongodb://localhost:27017");
        $db = $client->selectDatabase('test');
        
        // Test connection
        $result = $db->command(['ping' => 1]);
        echo "<div class='status success'>✅ MongoDB connection successful</div>";
        
        // Get server info
        $serverInfo = $db->command(['buildInfo' => 1])->toArray()[0];
        echo "<div class='status info'>🗄️ MongoDB version: {$serverInfo['version']}</div>";
        
        return true;
        
    } catch (Exception $e) {
        echo "<div class='status error'>❌ MongoDB connection failed: " . $e->getMessage() . "</div>";
        return false;
    }
}

function checkConfigFiles() {
    $files = [
        'includes/mongodb_config.php' => 'MongoDB Configuration',
        'includes/mongodb_manager.php' => 'MongoDB Manager Classes',
        'includes/mongodb_auth.php' => 'MongoDB Authentication'
    ];
    
    $allExist = true;
    
    foreach ($files as $file => $description) {
        if (file_exists($file)) {
            echo "<div class='status success'>✅ $description</div>";
        } else {
            echo "<div class='status error'>❌ $description (missing: $file)</div>";
            $allExist = false;
        }
    }
    
    return $allExist;
}

// Main checks
echo "<div class='check-grid'>";

// PHP Extension Check
echo "<div class='check-card'>
<h3>🔧 PHP Extension Check</h3>";
$extensionOk = checkPHPExtension();
echo "</div>";

// Composer Package Check
echo "<div class='check-card'>
<h3>📦 Composer Package Check</h3>";
$packageOk = checkComposerPackage();
echo "</div>";

// MongoDB Service Check
echo "<div class='check-card'>
<h3>🗄️ MongoDB Service Check</h3>";
$serviceOk = checkMongoDBService();
echo "</div>";

// Connection Test
echo "<div class='check-card'>
<h3>🔌 Connection Test</h3>";
$connectionOk = false;
if ($extensionOk && $packageOk) {
    $connectionOk = testMongoDBConnection();
} else {
    echo "<div class='status warning'>⚠️ Cannot test connection - missing requirements</div>";
}
echo "</div>";

// Config Files Check
echo "<div class='check-card'>
<h3>📁 Configuration Files Check</h3>";
$configOk = checkConfigFiles();
echo "</div>";

// System Requirements
echo "<div class='check-card'>
<h3>💻 System Requirements</h3>";
$phpVersion = phpversion();
if (version_compare($phpVersion, '7.4.0', '>=')) {
    echo "<div class='status success'>✅ PHP $phpVersion (>= 7.4.0)</div>";
} else {
    echo "<div class='status error'>❌ PHP $phpVersion (requires >= 7.4.0)</div>";
}

$extensions = ['json', 'openssl', 'curl'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<div class='status success'>✅ $ext extension</div>";
    } else {
        echo "<div class='status error'>❌ $ext extension</div>";
    }
}
echo "</div>";

echo "</div>"; // End check-grid

// Summary
$totalChecks = 6;
$passedChecks = 0;
if ($extensionOk) $passedChecks++;
if ($packageOk) $passedChecks++;
if ($serviceOk) $passedChecks++;
if ($connectionOk) $passedChecks++;
if ($configOk) $passedChecks++;
if (version_compare($phpVersion, '7.4.0', '>=')) $passedChecks++;

$percentage = round(($passedChecks / $totalChecks) * 100);

echo "<div class='install-guide'>
<h2>📊 Setup Status: $passedChecks/$totalChecks ($percentage%)</h2>";

if ($percentage == 100) {
    echo "<div class='status success'>🎉 All checks passed! MongoDB system is ready to use.</div>";
    echo "<p><a href='mongodb_migration.php' class='btn'>🚀 Start Migration</a></p>";
    echo "<p><a href='login_mongodb.php' class='btn'>🔑 Login to System</a></p>";
} else {
    echo "<div class='status warning'>⚠️ Setup incomplete. Please follow the installation guide below.</div>";
}

echo "</div>";

// Installation Guide
if ($percentage < 100) {
    echo "<div class='install-guide'>
    <h2>📋 Installation Guide</h2>";
    
    if (!$extensionOk) {
        echo "<h3>1. Install MongoDB PHP Extension</h3>";
        echo "<h4>Windows (XAMPP):</h4>";
        echo "<div class='code'>
1. Download php_mongodb.dll from https://pecl.php.net/package/mongodb
2. Copy to xampp/php/ext/ folder
3. Add 'extension=mongodb' to php.ini
4. Restart Apache
        </div>";
        
        echo "<h4>Linux (Ubuntu/Debian):</h4>";
        echo "<div class='code'>
sudo apt-get update
sudo apt-get install php-mongodb
sudo systemctl restart apache2
        </div>";
        
        echo "<h4>macOS:</h4>";
        echo "<div class='code'>
brew install php-mongodb
# or
sudo pecl install mongodb
        </div>";
    }
    
    if (!$packageOk) {
        echo "<h3>2. Install MongoDB Composer Package</h3>";
        echo "<div class='code'>
composer require mongodb/mongodb
        </div>";
        
        echo "<p>Or add to composer.json:</p>";
        echo "<div class='code'>
{
    \"require\": {
        \"mongodb/mongodb\": \"^1.15\"
    }
}
        </div>";
    }
    
    if (!$serviceOk) {
        echo "<h3>3. Install and Start MongoDB Service</h3>";
        echo "<h4>Windows:</h4>";
        echo "<div class='code'>
1. Download MongoDB from https://www.mongodb.com/try/download/community
2. Install MongoDB Community Server
3. Start MongoDB service from Services panel
4. Or run: net start MongoDB
        </div>";
        
        echo "<h4>Linux (Ubuntu/Debian):</h4>";
        echo "<div class='code'>
# Import MongoDB public key
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# Add MongoDB repository
echo \"deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse\" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# Install MongoDB
sudo apt-get update
sudo apt-get install -y mongodb-org

# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod
        </div>";
        
        echo "<h4>macOS:</h4>";
        echo "<div class='code'>
# Install via Homebrew
brew tap mongodb/brew
brew install mongodb-community

# Start MongoDB
brew services start mongodb/brew/mongodb-community
        </div>";
    }
    
    if (!$configOk) {
        echo "<h3>4. Setup Configuration Files</h3>";
        echo "<p>Run the migration tool to create configuration files:</p>";
        echo "<div class='code'>
php mongodb_migration.php
        </div>";
    }
    
    echo "</div>";
}

// Quick Actions
echo "<div class='install-guide'>
<h2>🚀 Quick Actions</h2>
<p><a href='mongodb_setup_check.php' class='btn'>🔄 Re-check Setup</a></p>
<p><a href='mongodb_test.php' class='btn primary'>🧪 Test MongoDB System</a></p>
<p><a href='mongodb_migration.php' class='btn'>🔄 Migration Tool</a></p>
<p><a href='index.php' class='btn'>🏠 Back to Main System</a></p>
</div>";

// Troubleshooting
echo "<div class='install-guide'>
<h2>🔧 Troubleshooting</h2>
<h3>Common Issues:</h3>
<ul>
<li><strong>Extension not loading:</strong> Check php.ini file and restart web server</li>
<li><strong>MongoDB not starting:</strong> Check if port 27017 is available</li>
<li><strong>Connection refused:</strong> Verify MongoDB service is running</li>
<li><strong>Permission denied:</strong> Check MongoDB data directory permissions</li>
</ul>

<h3>Useful Commands:</h3>
<div class='code'>
# Check MongoDB status
sudo systemctl status mongod

# View MongoDB logs
sudo tail -f /var/log/mongodb/mongod.log

# Connect to MongoDB shell
mongosh

# Check PHP extensions
php -m | grep mongodb
</div>
</div>";

echo "</body></html>";
?>
