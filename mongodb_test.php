<?php
/**
 * MongoDB Test System
 * Asset Management System - MongoDB Testing
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>
<html lang='th'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>MongoDB Test - Asset Management</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; border-radius: 15px; text-align: center; margin-bottom: 30px; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; margin: 20px 0; }
        .test-card { background: white; border-radius: 10px; padding: 20px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border-left: 5px solid #28a745; }
        .test-card h3 { color: #333; margin-top: 0; display: flex; align-items: center; gap: 10px; }
        .status { padding: 5px 10px; border-radius: 20px; font-weight: bold; font-size: 0.9em; margin: 5px 0; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .btn { display: inline-block; padding: 8px 16px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 3px; font-size: 0.9em; }
        .btn:hover { background: #218838; }
        .btn.danger { background: #dc3545; }
        .btn.primary { background: #007bff; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 0.9em; margin: 10px 0; }
        .summary { background: white; padding: 20px; border-radius: 10px; margin: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body>";

echo "<div class='header'>
    <h1>🧪 MongoDB Test System</h1>
    <p>Asset Management System - MongoDB Testing & Validation</p>
</div>";

// Check MongoDB extension
if (!extension_loaded('mongodb')) {
    echo "<div class='status error'>❌ MongoDB PHP extension not loaded</div>";
    echo "<p>Please install MongoDB PHP extension first.</p>";
    exit;
}

// Include MongoDB files
try {
    require_once 'includes/mongodb_config.php';
    require_once 'includes/mongodb_manager.php';
    echo "<div class='status success'>✅ MongoDB configuration loaded</div>";
} catch (Exception $e) {
    echo "<div class='status error'>❌ Configuration error: " . $e->getMessage() . "</div>";
    exit;
}

// Test functions
function testConnection() {
    try {
        if (testMongoConnection()) {
            $serverInfo = getMongoServerInfo();
            echo "<div class='status success'>✅ Connection successful</div>";
            echo "<div class='status info'>📊 MongoDB " . ($serverInfo['version'] ?? 'Unknown') . "</div>";
            return true;
        } else {
            echo "<div class='status error'>❌ Connection failed</div>";
            return false;
        }
    } catch (Exception $e) {
        echo "<div class='status error'>❌ Connection error: " . $e->getMessage() . "</div>";
        return false;
    }
}

function testCollections() {
    try {
        $db = getMongoDatabase();
        $collections = ['users', 'assets', 'asset_logs', 'sessions'];
        
        foreach ($collections as $collectionName) {
            $collection = $db->selectCollection($collectionName);
            $count = $collection->countDocuments([]);
            echo "<div class='status success'>✅ $collectionName: $count documents</div>";
        }
        
        return true;
    } catch (Exception $e) {
        echo "<div class='status error'>❌ Collections error: " . $e->getMessage() . "</div>";
        return false;
    }
}

function testUserOperations() {
    global $mongoUserManager;
    
    try {
        // Test create user
        $testUser = [
            'username' => 'test_user_' . time(),
            'password' => 'test123',
            'full_name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'User'
        ];
        
        $userId = $mongoUserManager->createUser($testUser);
        echo "<div class='status success'>✅ User created: " . mongoIdToString($userId) . "</div>";
        
        // Test find user
        $user = $mongoUserManager->findById($userId);
        if ($user) {
            echo "<div class='status success'>✅ User found: {$user['username']}</div>";
        }
        
        // Test authenticate
        $authUser = $mongoUserManager->authenticate($testUser['username'], 'test123');
        if ($authUser) {
            echo "<div class='status success'>✅ Authentication successful</div>";
        }
        
        // Test update
        $updated = $mongoUserManager->update($userId, ['full_name' => 'Updated Test User']);
        if ($updated) {
            echo "<div class='status success'>✅ User updated</div>";
        }
        
        // Test delete
        $deleted = $mongoUserManager->delete($userId);
        if ($deleted) {
            echo "<div class='status success'>✅ User deleted</div>";
        }
        
        return true;
        
    } catch (Exception $e) {
        echo "<div class='status error'>❌ User operations error: " . $e->getMessage() . "</div>";
        return false;
    }
}

function testAssetOperations() {
    global $mongoAssetManager;
    
    try {
        // Test create asset
        $testAsset = [
            'asset_id' => 'TEST_' . time(),
            'tag' => 'TAG_' . time(),
            'type' => 'Test Equipment',
            'brand' => 'Test Brand',
            'model' => 'Test Model',
            'status' => 'ใช้งาน',
            'department' => 'IT',
            'created_by' => 'Test System'
        ];
        
        $assetId = $mongoAssetManager->createAsset($testAsset);
        echo "<div class='status success'>✅ Asset created: " . mongoIdToString($assetId) . "</div>";
        
        // Test find asset
        $asset = $mongoAssetManager->findById($assetId);
        if ($asset) {
            echo "<div class='status success'>✅ Asset found: {$asset['asset_id']}</div>";
        }
        
        // Test search
        $searchResults = $mongoAssetManager->searchAssets('Test', [], 1, 5);
        echo "<div class='status success'>✅ Search found: {$searchResults['pagination']['total_count']} assets</div>";
        
        // Test update
        $updated = $mongoAssetManager->updateAsset($assetId, ['status' => 'สำรอง'], 'Test System');
        if ($updated) {
            echo "<div class='status success'>✅ Asset updated</div>";
        }
        
        // Test statistics
        $stats = $mongoAssetManager->getAssetStatistics();
        echo "<div class='status success'>✅ Statistics: {$stats['total']} total assets</div>";
        
        // Test delete
        $deleted = $mongoAssetManager->delete($assetId);
        if ($deleted) {
            echo "<div class='status success'>✅ Asset deleted</div>";
        }
        
        return true;
        
    } catch (Exception $e) {
        echo "<div class='status error'>❌ Asset operations error: " . $e->getMessage() . "</div>";
        return false;
    }
}

function testLogOperations() {
    global $mongoAssetLogManager, $mongoAssetManager;
    
    try {
        // Create a test asset first
        $testAsset = [
            'asset_id' => 'LOG_TEST_' . time(),
            'type' => 'Test for Logging',
            'status' => 'ใช้งาน',
            'created_by' => 'Log Test'
        ];
        
        $assetId = $mongoAssetManager->createAsset($testAsset);
        
        // Test log creation
        $logId = $mongoAssetLogManager->logAssetAction(
            $assetId,
            'test_action',
            ['old_status' => 'ใช้งาน'],
            ['new_status' => 'ชำรุด'],
            'Test System'
        );
        
        echo "<div class='status success'>✅ Log created: " . mongoIdToString($logId) . "</div>";
        
        // Test get asset history
        $history = $mongoAssetLogManager->getAssetHistory($assetId, 1, 10);
        echo "<div class='status success'>✅ Asset history: {$history['pagination']['total_count']} logs</div>";
        
        // Test recent logs
        $recentLogs = $mongoAssetLogManager->getRecentLogs(5);
        echo "<div class='status success'>✅ Recent logs: " . count($recentLogs) . " logs</div>";
        
        // Clean up
        $mongoAssetManager->delete($assetId);
        
        return true;
        
    } catch (Exception $e) {
        echo "<div class='status error'>❌ Log operations error: " . $e->getMessage() . "</div>";
        return false;
    }
}

function testSessionOperations() {
    global $mongoSessionManager, $mongoUserManager;
    
    try {
        // Create test user for session
        $testUser = [
            'username' => 'session_test_' . time(),
            'password' => 'test123',
            'full_name' => 'Session Test User',
            'role' => 'User'
        ];
        
        $userId = $mongoUserManager->createUser($testUser);
        
        // Test create session
        $sessionId = $mongoSessionManager->createSession($userId, ['test_data' => 'test_value']);
        echo "<div class='status success'>✅ Session created: $sessionId</div>";
        
        // Test get session
        $session = $mongoSessionManager->getSession($sessionId);
        if ($session) {
            echo "<div class='status success'>✅ Session retrieved</div>";
        }
        
        // Test update session
        $updated = $mongoSessionManager->updateSession($sessionId, ['updated_data' => 'updated_value']);
        if ($updated) {
            echo "<div class='status success'>✅ Session updated</div>";
        }
        
        // Test delete session
        $deleted = $mongoSessionManager->deleteSession($sessionId);
        if ($deleted) {
            echo "<div class='status success'>✅ Session deleted</div>";
        }
        
        // Clean up
        $mongoUserManager->delete($userId);
        
        return true;
        
    } catch (Exception $e) {
        echo "<div class='status error'>❌ Session operations error: " . $e->getMessage() . "</div>";
        return false;
    }
}

// Main testing
echo "<div class='test-grid'>";

// Connection test
echo "<div class='test-card'>
<h3>🔌 Connection Test</h3>";
$connectionOk = testConnection();
echo "</div>";

// Collections test
echo "<div class='test-card'>
<h3>📚 Collections Test</h3>";
$collectionsOk = testCollections();
echo "</div>";

// User operations test
echo "<div class='test-card'>
<h3>👤 User Operations Test</h3>";
$userOpsOk = testUserOperations();
echo "</div>";

// Asset operations test
echo "<div class='test-card'>
<h3>📦 Asset Operations Test</h3>";
$assetOpsOk = testAssetOperations();
echo "</div>";

// Log operations test
echo "<div class='test-card'>
<h3>📝 Log Operations Test</h3>";
$logOpsOk = testLogOperations();
echo "</div>";

// Session operations test
echo "<div class='test-card'>
<h3>🔑 Session Operations Test</h3>";
$sessionOpsOk = testSessionOperations();
echo "</div>";

echo "</div>"; // End test-grid

// Summary
$totalTests = 6;
$passedTests = 0;
if ($connectionOk) $passedTests++;
if ($collectionsOk) $passedTests++;
if ($userOpsOk) $passedTests++;
if ($assetOpsOk) $passedTests++;
if ($logOpsOk) $passedTests++;
if ($sessionOpsOk) $passedTests++;

$percentage = round(($passedTests / $totalTests) * 100);

echo "<div class='summary'>
<h2>📊 Test Summary</h2>
<p><strong>Tests Passed:</strong> $passedTests/$totalTests ($percentage%)</p>";

if ($percentage == 100) {
    echo "<div class='status success'>✅ All tests passed! MongoDB system is ready.</div>";
    echo "<p><a href='index_mongodb.php' class='btn'>🏠 Go to MongoDB System</a></p>";
} elseif ($percentage >= 80) {
    echo "<div class='status warning'>⚠️ Most tests passed. Some features may not work properly.</div>";
} else {
    echo "<div class='status error'>❌ Multiple tests failed. System needs attention.</div>";
}

echo "<p><a href='mongodb_migration.php' class='btn primary'>🔄 Migration Tool</a></p>";
echo "<p><a href='mongodb_test.php' class='btn'>🔄 Run Tests Again</a></p>";

echo "</div>";

// Performance info
echo "<div class='summary'>
<h2>⚡ Performance Info</h2>";

try {
    $db = getMongoDatabase();
    
    // Database stats
    $stats = $db->command(['dbStats' => 1])->toArray()[0];
    echo "<div class='code'>";
    echo "Database: " . $stats['db'] . "<br>";
    echo "Collections: " . $stats['collections'] . "<br>";
    echo "Data Size: " . round($stats['dataSize'] / 1024 / 1024, 2) . " MB<br>";
    echo "Index Size: " . round($stats['indexSize'] / 1024 / 1024, 2) . " MB<br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='status error'>❌ Could not get performance info: " . $e->getMessage() . "</div>";
}

echo "</div>";

echo "</body></html>";
?>
