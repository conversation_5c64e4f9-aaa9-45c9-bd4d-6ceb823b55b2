<?php
// Database configuration for Asset Management System - MySQL 8.0 Compatible
class Database {
    private $host = 'localhost';
    private $port = '3305';
    private $db_name = 'asset_management';
    private $username = 'root';
    private $password = 'Wxmujwsofu@1234'; // เปลี่ยนเป็นรหัสผ่านที่ถูกต้อง
    private $charset = 'utf8mb4';
    public $pdo;

    public function __construct() {
        $this->connect();
    }

    private function connect() {
        try {
            // ลองเชื่อมต่อฐานข้อมูลก่อน
            $dsn = "mysql:host={$this->host};port={$this->port};dbname={$this->db_name};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                // MySQL 8.0 specific options
                PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
                PDO::MYSQL_ATTR_SSL_CA => null,
                // Handle MySQL 8.0 authentication
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset} COLLATE {$this->charset}_unicode_ci"
            ];

            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);

            // Set timezone to Thailand
            $this->pdo->exec("SET time_zone = '+07:00'");

            // Set SQL mode for MySQL 8.0 compatibility
            $this->pdo->exec("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");

            // Set charset and collation
            $this->pdo->exec("SET NAMES {$this->charset} COLLATE {$this->charset}_unicode_ci");

        } catch (PDOException $e) {
            // ถ้าฐานข้อมูลไม่มี ให้สร้างใหม่
            if ($e->getCode() == 1049) {
                $this->createDatabase();
            } else {
                die("Connection failed: " . $e->getMessage() . " (Error Code: " . $e->getCode() . ")");
            }
        }
    }

    private function createDatabase() {
        try {
            // เชื่อมต่อโดยไม่ระบุฐานข้อมูล
            $dsn = "mysql:host={$this->host};port={$this->port};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                // MySQL 8.0 specific options
                PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
                PDO::MYSQL_ATTR_SSL_CA => null,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset} COLLATE {$this->charset}_unicode_ci"
            ];

            $pdo = new PDO($dsn, $this->username, $this->password, $options);

            // สร้างฐานข้อมูล with MySQL 8.0 compatible settings
            $pdo->exec("CREATE DATABASE IF NOT EXISTS {$this->db_name}
                       CHARACTER SET {$this->charset}
                       COLLATE {$this->charset}_unicode_ci");

            // เชื่อมต่อฐานข้อมูลที่เพิ่งสร้าง
            $dsn = "mysql:host={$this->host};port={$this->port};dbname={$this->db_name};charset={$this->charset}";
            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);

            // Set timezone to Thailand
            $this->pdo->exec("SET time_zone = '+07:00'");

            // Set SQL mode for MySQL 8.0 compatibility
            $this->pdo->exec("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");

            // Set charset and collation
            $this->pdo->exec("SET NAMES {$this->charset} COLLATE {$this->charset}_unicode_ci");

            // สร้างตารางจากไฟล์ SQL
            $this->createTables();

        } catch (PDOException $e) {
            die("Database creation failed: " . $e->getMessage() . " (Error Code: " . $e->getCode() . ")");
        }
    }

    private function createTables() {
        $sql = file_get_contents(__DIR__ . '/../sql/setup.sql');

        // แยก SQL statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));

        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^(CREATE DATABASE|USE)/i', $statement)) {
                try {
                    $this->pdo->exec($statement);
                } catch (PDOException $e) {
                    // ข้าม error ถ้าตารางมีอยู่แล้ว
                    if ($e->getCode() != '42S01') {
                        throw $e;
                    }
                }
            }
        }
    }

    public function getConnection() {
        return $this->pdo;
    }
}

// Global database connection
$database = new Database();
$pdo = $database->getConnection();
?>
