<?php
/**
 * MySQL 8.0 Migration and Setup Tool
 * Asset Management System
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 MySQL 8.0 Migration Tool</h1>";
echo "<p>Asset Management System - Database Migration and Setup</p>";

// Include new config
require_once 'includes/config.php';

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>📋 System Information</h2>";

// Check PHP version
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";

// Check MySQL version
try {
    $version = getMySQLVersion();
    echo "<p><strong>MySQL Version:</strong> " . htmlspecialchars($version) . "</p>";
    
    if (version_compare($version, '8.0.0', '>=')) {
        echo "<p style='color: green;'>✅ MySQL 8.0+ detected - Compatible!</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ MySQL version is below 8.0 - Some features may not work optimally</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Could not determine MySQL version: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test connection
if (testConnection()) {
    echo "<p style='color: green;'>✅ Database connection successful</p>";
} else {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
}

echo "</div>";

// Migration functions
function checkTableExists($pdo, $tableName) {
    try {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$tableName]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

function checkColumnExists($pdo, $tableName, $columnName) {
    try {
        $stmt = $pdo->prepare("SHOW COLUMNS FROM `$tableName` LIKE ?");
        $stmt->execute([$columnName]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

function migrateToMySQL80($pdo) {
    echo "<h2>🔄 Starting MySQL 8.0 Migration</h2>";
    
    $migrations = [];
    
    // Check and create tables
    $requiredTables = ['users', 'assets', 'asset_logs'];
    
    foreach ($requiredTables as $table) {
        if (!checkTableExists($pdo, $table)) {
            $migrations[] = "Table '$table' needs to be created";
        } else {
            echo "<p style='color: green;'>✅ Table '$table' exists</p>";
        }
    }
    
    // Check users table structure
    if (checkTableExists($pdo, 'users')) {
        $userColumns = ['id', 'username', 'password', 'full_name', 'email', 'role', 'status', 'created_date', 'updated_date'];
        foreach ($userColumns as $column) {
            if (!checkColumnExists($pdo, 'users', $column)) {
                $migrations[] = "Column 'users.$column' needs to be added";
            }
        }
    }
    
    // Check assets table structure
    if (checkTableExists($pdo, 'assets')) {
        $assetColumns = ['id', 'asset_id', 'tag', 'type', 'brand', 'model', 'hostname', 'operating_system', 
                        'serial_number', 'status', 'department', 'warranty_expire', 'description', 'set_name',
                        'created_date', 'created_by', 'updated_date', 'updated_by'];
        foreach ($assetColumns as $column) {
            if (!checkColumnExists($pdo, 'assets', $column)) {
                $migrations[] = "Column 'assets.$column' needs to be added";
            }
        }
    }
    
    // Check asset_logs table structure
    if (checkTableExists($pdo, 'asset_logs')) {
        $logColumns = ['id', 'asset_id', 'action', 'old_values', 'new_values', 'changed_by', 'changed_date', 'ip_address', 'user_agent'];
        foreach ($logColumns as $column) {
            if (!checkColumnExists($pdo, 'asset_logs', $column)) {
                $migrations[] = "Column 'asset_logs.$column' needs to be added";
            }
        }
    }
    
    if (empty($migrations)) {
        echo "<p style='color: green;'>✅ Database structure is up to date!</p>";
        return true;
    } else {
        echo "<h3>⚠️ Required Migrations:</h3>";
        echo "<ul>";
        foreach ($migrations as $migration) {
            echo "<li>" . htmlspecialchars($migration) . "</li>";
        }
        echo "</ul>";
        return false;
    }
}

function runMigration($pdo) {
    echo "<h2>🚀 Running Database Migration</h2>";
    
    try {
        // Read and execute setup SQL
        $sqlFile = __DIR__ . '/sql/setup_mysql80.sql';
        
        if (!file_exists($sqlFile)) {
            throw new Exception("Setup SQL file not found: $sqlFile");
        }
        
        $sql = file_get_contents($sqlFile);
        
        // Remove comments and split statements
        $sql = preg_replace('/--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
        
        // Split by delimiter changes and regular statements
        $statements = [];
        $currentDelimiter = ';';
        $lines = explode("\n", $sql);
        $currentStatement = '';
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;
            
            if (preg_match('/^DELIMITER\s+(.+)$/i', $line, $matches)) {
                if (!empty($currentStatement)) {
                    $statements[] = trim($currentStatement);
                    $currentStatement = '';
                }
                $currentDelimiter = $matches[1];
                continue;
            }
            
            $currentStatement .= $line . "\n";
            
            if (substr($line, -strlen($currentDelimiter)) === $currentDelimiter) {
                $statement = trim(substr($currentStatement, 0, -strlen($currentDelimiter)));
                if (!empty($statement)) {
                    $statements[] = $statement;
                }
                $currentStatement = '';
            }
        }
        
        if (!empty($currentStatement)) {
            $statements[] = trim($currentStatement);
        }
        
        // Execute statements
        $executed = 0;
        $errors = 0;
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (empty($statement)) continue;
            
            // Skip certain statements
            if (preg_match('/^(CREATE DATABASE|USE|SET|DELIMITER)/i', $statement)) {
                continue;
            }
            
            try {
                $pdo->exec($statement);
                $executed++;
                echo "<p style='color: green;'>✅ Executed: " . substr(htmlspecialchars($statement), 0, 100) . "...</p>";
            } catch (PDOException $e) {
                $errors++;
                // Only show error if it's not "already exists"
                if (!preg_match('/(already exists|duplicate)/i', $e->getMessage())) {
                    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
                    echo "<p style='color: gray;'>Statement: " . htmlspecialchars(substr($statement, 0, 200)) . "...</p>";
                }
            }
        }
        
        echo "<h3>📊 Migration Summary</h3>";
        echo "<p>Statements executed: $executed</p>";
        echo "<p>Errors encountered: $errors</p>";
        
        if ($errors == 0) {
            echo "<p style='color: green; font-weight: bold;'>✅ Migration completed successfully!</p>";
        } else {
            echo "<p style='color: orange; font-weight: bold;'>⚠️ Migration completed with some warnings</p>";
        }
        
        return true;
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Migration failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        return false;
    }
}

function optimizeForMySQL80($pdo) {
    echo "<h2>⚡ Optimizing for MySQL 8.0</h2>";
    
    $optimizations = [
        "SET GLOBAL innodb_buffer_pool_size = 128M",
        "SET GLOBAL innodb_log_file_size = 64M",
        "SET GLOBAL innodb_flush_log_at_trx_commit = 2",
        "SET GLOBAL query_cache_type = OFF",
        "SET GLOBAL innodb_strict_mode = ON"
    ];
    
    foreach ($optimizations as $sql) {
        try {
            $pdo->exec($sql);
            echo "<p style='color: green;'>✅ Applied: " . htmlspecialchars($sql) . "</p>";
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ Could not apply: " . htmlspecialchars($sql) . " - " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
}

function createDefaultData($pdo) {
    echo "<h2>👤 Creating Default Data</h2>";
    
    try {
        // Check if admin user exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
        $stmt->execute(['admin']);
        
        if ($stmt->fetchColumn() == 0) {
            $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute(['admin', $hashedPassword, 'Administrator', '<EMAIL>', 'Admin']);
            echo "<p style='color: green;'>✅ Created admin user (password: admin123)</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ Admin user already exists</p>";
        }
        
        // Check asset count
        $stmt = $pdo->query("SELECT COUNT(*) FROM assets");
        $assetCount = $stmt->fetchColumn();
        echo "<p>📦 Current assets in database: $assetCount</p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Error creating default data: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

// Main execution
echo "<div style='background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #ddd;'>";

if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'migrate':
            runMigration($pdo);
            break;
        case 'optimize':
            optimizeForMySQL80($pdo);
            break;
        case 'create_data':
            createDefaultData($pdo);
            break;
        case 'full_setup':
            runMigration($pdo);
            optimizeForMySQL80($pdo);
            createDefaultData($pdo);
            break;
        default:
            echo "<p style='color: red;'>Unknown action</p>";
    }
} else {
    // Check current status
    migrateToMySQL80($pdo);
    
    echo "<h2>🎯 Available Actions</h2>";
    echo "<p><a href='?action=migrate' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Run Migration</a></p>";
    echo "<p><a href='?action=optimize' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Optimize for MySQL 8.0</a></p>";
    echo "<p><a href='?action=create_data' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Create Default Data</a></p>";
    echo "<p><a href='?action=full_setup' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Full Setup (All Actions)</a></p>";
}

echo "</div>";

echo "<div style='background: #e9ecef; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>📝 Notes</h3>";
echo "<ul>";
echo "<li>Make sure to backup your database before running migration</li>";
echo "<li>This tool is designed for MySQL 8.0+ compatibility</li>";
echo "<li>Default admin credentials: admin / admin123</li>";
echo "<li>After migration, test all functionality thoroughly</li>";
echo "</ul>";
echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

p {
    line-height: 1.6;
}

a {
    display: inline-block;
    margin: 5px 0;
}
</style>
