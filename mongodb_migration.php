<?php
/**
 * MongoDB Migration Tool
 * Asset Management System - MySQL to MongoDB Migration
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('max_execution_time', 300); // 5 minutes

echo "<h1>🔄 MongoDB Migration Tool</h1>";
echo "<p>Asset Management System - MySQL to MongoDB Migration</p>";

// Check if MongoDB extension is loaded
if (!extension_loaded('mongodb')) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ MongoDB Extension Not Found</h3>";
    echo "<p>กรุณาติดตั้ง MongoDB PHP extension ก่อน:</p>";
    echo "<ul>";
    echo "<li><strong>Windows (XAMPP):</strong> ดาวน์โหลด php_mongodb.dll และใส่ใน ext/ folder</li>";
    echo "<li><strong>Linux:</strong> sudo apt-get install php-mongodb</li>";
    echo "<li><strong>Composer:</strong> composer require mongodb/mongodb</li>";
    echo "</ul>";
    echo "</div>";
    exit;
}

// Include MongoDB configuration
try {
    require_once 'includes/mongodb_config.php';
    require_once 'includes/mongodb_manager.php';
    echo "<p style='color: green;'>✅ MongoDB configuration loaded</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ MongoDB configuration error: " . $e->getMessage() . "</p>";
    exit;
}

// Test MongoDB connection
try {
    if (testMongoConnection()) {
        $serverInfo = getMongoServerInfo();
        echo "<p style='color: green;'>✅ MongoDB connection successful</p>";
        echo "<p style='color: blue;'>📊 MongoDB version: " . ($serverInfo['version'] ?? 'Unknown') . "</p>";
    } else {
        throw new Exception("Connection test failed");
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ MongoDB connection failed: " . $e->getMessage() . "</p>";
    echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>⚠️ MongoDB Setup Required</h3>";
    echo "<p>กรุณาตรวจสอบ:</p>";
    echo "<ul>";
    echo "<li>MongoDB service ทำงานอยู่หรือไม่</li>";
    echo "<li>การตั้งค่าใน includes/mongodb_config.php</li>";
    echo "<li>Port 27017 เปิดอยู่หรือไม่</li>";
    echo "</ul>";
    echo "</div>";
    exit;
}

// Functions
function logMessage($message, $type = 'info') {
    $colors = ['success' => 'green', 'error' => 'red', 'warning' => 'orange', 'info' => 'blue'];
    $icons = ['success' => '✅', 'error' => '❌', 'warning' => '⚠️', 'info' => 'ℹ️'];
    
    $color = $colors[$type] ?? 'black';
    $icon = $icons[$type] ?? '•';
    
    echo "<p style='color: $color; font-weight: bold;'>$icon $message</p>";
}

function connectMySQL() {
    $configs = [
        ['localhost', 'root', '', 'asset_management'],
        ['localhost', 'root', 'root', 'asset_management'],
        ['localhost', 'root', '', 'mysql']
    ];
    
    foreach ($configs as $config) {
        try {
            $pdo = new PDO("mysql:host={$config[0]};dbname={$config[3]};charset=utf8mb4", $config[1], $config[2]);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            return $pdo;
        } catch (PDOException $e) {
            continue;
        }
    }
    
    return null;
}

function migrateUsers($pdo) {
    global $mongoUserManager;
    
    logMessage("Starting users migration...", 'info');
    
    try {
        $stmt = $pdo->query("SELECT * FROM users");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $migrated = 0;
        $errors = 0;
        
        foreach ($users as $user) {
            try {
                // ตรวจสอบว่ามีอยู่แล้วหรือไม่
                $existing = $mongoUserManager->findOne(['username' => $user['username']]);
                if ($existing) {
                    logMessage("User {$user['username']} already exists, skipping", 'warning');
                    continue;
                }
                
                // แปลงข้อมูล
                $mongoUser = [
                    'username' => $user['username'],
                    'password' => $user['password'], // Already hashed
                    'full_name' => $user['full_name'] ?? '',
                    'email' => $user['email'] ?? '',
                    'role' => $user['role'] ?? 'User',
                    'status' => $user['status'] ?? 'Active'
                ];
                
                // ใช้ insert แทน createUser เพื่อไม่ hash password ซ้ำ
                $mongoUserManager->insert($mongoUser);
                $migrated++;
                
            } catch (Exception $e) {
                logMessage("Error migrating user {$user['username']}: " . $e->getMessage(), 'error');
                $errors++;
            }
        }
        
        logMessage("Users migration completed: $migrated migrated, $errors errors", 'success');
        return ['migrated' => $migrated, 'errors' => $errors];
        
    } catch (Exception $e) {
        logMessage("Users migration failed: " . $e->getMessage(), 'error');
        return ['migrated' => 0, 'errors' => 1];
    }
}

function migrateAssets($pdo) {
    global $mongoAssetManager;
    
    logMessage("Starting assets migration...", 'info');
    
    try {
        $stmt = $pdo->query("SELECT * FROM assets");
        $assets = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $migrated = 0;
        $errors = 0;
        
        foreach ($assets as $asset) {
            try {
                // แปลงข้อมูล
                $mongoAsset = [
                    'asset_id' => $asset['asset_id'] ?? '',
                    'tag' => $asset['tag'] ?? '',
                    'type' => $asset['type'] ?? '',
                    'brand' => $asset['brand'] ?? '',
                    'model' => $asset['model'] ?? '',
                    'hostname' => $asset['hostname'] ?? '',
                    'operating_system' => $asset['operating_system'] ?? '',
                    'serial_number' => $asset['serial_number'] ?? '',
                    'status' => $asset['status'] ?? '',
                    'department' => $asset['department'] ?? '',
                    'warranty_expire' => !empty($asset['warranty_expire']) && $asset['warranty_expire'] !== '0000-00-00' 
                        ? createMongoDate($asset['warranty_expire']) : null,
                    'description' => $asset['description'] ?? '',
                    'set_name' => $asset['set_name'] ?? '',
                    'created_by' => $asset['created_by'] ?? 'Migration',
                    'updated_by' => $asset['updated_by'] ?? 'Migration'
                ];
                
                // ใช้ insert แทน createAsset เพื่อควบคุมข้อมูลได้มากขึ้น
                $mongoAssetManager->insert($mongoAsset);
                $migrated++;
                
            } catch (Exception $e) {
                logMessage("Error migrating asset {$asset['asset_id']}: " . $e->getMessage(), 'error');
                $errors++;
            }
        }
        
        logMessage("Assets migration completed: $migrated migrated, $errors errors", 'success');
        return ['migrated' => $migrated, 'errors' => $errors];
        
    } catch (Exception $e) {
        logMessage("Assets migration failed: " . $e->getMessage(), 'error');
        return ['migrated' => 0, 'errors' => 1];
    }
}

function migrateAssetLogs($pdo) {
    global $mongoAssetLogManager, $mongoAssetManager;
    
    logMessage("Starting asset logs migration...", 'info');
    
    try {
        $stmt = $pdo->query("SELECT * FROM asset_logs");
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $migrated = 0;
        $errors = 0;
        
        foreach ($logs as $log) {
            try {
                // หา asset ใน MongoDB
                $asset = $mongoAssetManager->findOne(['asset_id' => $log['asset_id']]);
                if (!$asset) {
                    logMessage("Asset not found for log ID {$log['id']}, skipping", 'warning');
                    continue;
                }
                
                // แปลงข้อมูล
                $mongoLog = [
                    'asset_id' => $asset['_id'],
                    'action' => $log['action'] ?? 'unknown',
                    'old_values' => !empty($log['old_values']) ? json_decode($log['old_values'], true) : null,
                    'new_values' => !empty($log['new_values']) ? json_decode($log['new_values'], true) : null,
                    'changed_by' => $log['changed_by'] ?? 'Unknown',
                    'changed_date' => !empty($log['changed_date']) ? createMongoDate($log['changed_date']) : createMongoDate(),
                    'ip_address' => $log['ip_address'] ?? null,
                    'user_agent' => $log['user_agent'] ?? null
                ];
                
                $mongoAssetLogManager->insert($mongoLog);
                $migrated++;
                
            } catch (Exception $e) {
                logMessage("Error migrating log ID {$log['id']}: " . $e->getMessage(), 'error');
                $errors++;
            }
        }
        
        logMessage("Asset logs migration completed: $migrated migrated, $errors errors", 'success');
        return ['migrated' => $migrated, 'errors' => $errors];
        
    } catch (Exception $e) {
        logMessage("Asset logs migration failed: " . $e->getMessage(), 'error');
        return ['migrated' => 0, 'errors' => 1];
    }
}

// Main execution
echo "<div style='background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #ddd;'>";

if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'migrate_all':
            echo "<h2>🚀 Starting Full Migration</h2>";
            
            // Connect to MySQL
            $pdo = connectMySQL();
            if (!$pdo) {
                logMessage("Cannot connect to MySQL database", 'error');
                break;
            }
            
            logMessage("MySQL connection successful", 'success');
            
            // Migrate data
            $userResults = migrateUsers($pdo);
            $assetResults = migrateAssets($pdo);
            $logResults = migrateAssetLogs($pdo);
            
            // Summary
            echo "<h3>📊 Migration Summary</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Collection</th><th>Migrated</th><th>Errors</th></tr>";
            echo "<tr><td>Users</td><td>{$userResults['migrated']}</td><td>{$userResults['errors']}</td></tr>";
            echo "<tr><td>Assets</td><td>{$assetResults['migrated']}</td><td>{$assetResults['errors']}</td></tr>";
            echo "<tr><td>Asset Logs</td><td>{$logResults['migrated']}</td><td>{$logResults['errors']}</td></tr>";
            echo "</table>";
            
            $totalMigrated = $userResults['migrated'] + $assetResults['migrated'] + $logResults['migrated'];
            $totalErrors = $userResults['errors'] + $assetResults['errors'] + $logResults['errors'];
            
            if ($totalErrors == 0) {
                logMessage("Migration completed successfully! Total records: $totalMigrated", 'success');
            } else {
                logMessage("Migration completed with $totalErrors errors. Total records: $totalMigrated", 'warning');
            }
            
            break;
            
        case 'test_mongodb':
            echo "<h2>🧪 Testing MongoDB</h2>";
            
            // Test collections
            $collections = ['users', 'assets', 'asset_logs'];
            foreach ($collections as $collectionName) {
                try {
                    $collection = getMongoDatabase()->selectCollection($collectionName);
                    $count = $collection->countDocuments([]);
                    logMessage("Collection '$collectionName': $count documents", 'success');
                } catch (Exception $e) {
                    logMessage("Collection '$collectionName' error: " . $e->getMessage(), 'error');
                }
            }
            
            break;
            
        case 'clear_mongodb':
            echo "<h2>🗑️ Clearing MongoDB Data</h2>";
            
            if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
                try {
                    $db = getMongoDatabase();
                    
                    $collections = ['users', 'assets', 'asset_logs', 'sessions'];
                    foreach ($collections as $collectionName) {
                        $result = $db->selectCollection($collectionName)->deleteMany([]);
                        logMessage("Cleared collection '$collectionName': {$result->getDeletedCount()} documents", 'success');
                    }
                    
                    // Recreate default admin user
                    createDefaultMongoUser();
                    logMessage("Recreated default admin user", 'success');
                    
                } catch (Exception $e) {
                    logMessage("Clear operation failed: " . $e->getMessage(), 'error');
                }
            } else {
                echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px;'>";
                echo "<h3>⚠️ Warning</h3>";
                echo "<p>This will delete ALL data in MongoDB collections!</p>";
                echo "<p><a href='?action=clear_mongodb&confirm=yes' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Confirm Delete All Data</a></p>";
                echo "</div>";
            }
            
            break;
    }
} else {
    // Show main menu
    echo "<h2>🎯 Available Actions</h2>";
    
    // Check MySQL connection
    $pdo = connectMySQL();
    if ($pdo) {
        logMessage("MySQL connection available for migration", 'success');
        echo "<p><a href='?action=migrate_all' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>🚀 Migrate All Data from MySQL</a></p>";
    } else {
        logMessage("MySQL connection not available", 'warning');
        echo "<p style='color: orange;'>⚠️ MySQL connection not available. Migration from MySQL is not possible.</p>";
    }
    
    echo "<p><a href='?action=test_mongodb' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>🧪 Test MongoDB Collections</a></p>";
    echo "<p><a href='?action=clear_mongodb' style='background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>🗑️ Clear MongoDB Data</a></p>";
    echo "<p><a href='mongodb_test.php' style='background: #6f42c1; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>🔍 Test MongoDB System</a></p>";
}

echo "</div>";

echo "<div style='background: #e9ecef; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>📝 Notes</h3>";
echo "<ul>";
echo "<li>Make sure MongoDB service is running</li>";
echo "<li>Backup your MySQL data before migration</li>";
echo "<li>Migration may take several minutes for large datasets</li>";
echo "<li>Default admin credentials: admin / admin123</li>";
echo "</ul>";
echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

table {
    background: white;
    margin: 10px 0;
}

th, td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background: #f8f9fa;
}

a {
    display: inline-block;
    margin: 5px 0;
}
</style>
