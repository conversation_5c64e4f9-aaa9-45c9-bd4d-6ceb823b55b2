<?php
/**
 * MongoDB PHP Extension Installation Guide
 * Asset Management System
 */

echo "<!DOCTYPE html>
<html lang='th'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>ติดตั้ง MongoDB PHP Extension</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background: #f5f5f5; line-height: 1.6; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; border-radius: 15px; text-align: center; margin-bottom: 30px; }
        .guide-section { background: white; border-radius: 10px; padding: 25px; margin: 20px 0; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .os-tabs { display: flex; gap: 10px; margin-bottom: 20px; flex-wrap: wrap; }
        .tab-btn { padding: 10px 20px; background: #e9ecef; border: none; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.3s; }
        .tab-btn.active { background: #28a745; color: white; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .code-block { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 15px 0; font-family: 'Courier New', monospace; overflow-x: auto; border-left: 4px solid #28a745; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .step { background: #e9ecef; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #007bff; }
        .btn { display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px; transition: all 0.3s; }
        .btn:hover { background: #218838; transform: translateY(-2px); }
        .btn.primary { background: #007bff; }
        .btn.danger { background: #dc3545; }
        .download-links { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }
        .download-card { background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 10px; padding: 20px; text-align: center; transition: all 0.3s; }
        .download-card:hover { border-color: #28a745; transform: translateY(-2px); }
        .version-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 8px; margin: 15px 0; }
    </style>
</head>
<body>";

echo "<div class='header'>
    <h1>🍃 ติดตั้ง MongoDB PHP Extension</h1>
    <p>คู่มือการติดตั้งสำหรับระบบปฏิบัติการต่างๆ</p>
</div>";

// Check current PHP info
$phpVersion = phpversion();
$phpArch = (PHP_INT_SIZE === 8) ? 'x64' : 'x86';
$isThreadSafe = (defined('ZEND_THREAD_SAFE') && ZEND_THREAD_SAFE) ? 'TS' : 'NTS';
$phpApi = phpversion('Core');

echo "<div class='guide-section'>
<h2>📊 ข้อมูลระบบปัจจุบัน</h2>
<div class='version-info'>
<p><strong>PHP Version:</strong> $phpVersion</p>
<p><strong>Architecture:</strong> $phpArch</p>
<p><strong>Thread Safety:</strong> $isThreadSafe</p>
<p><strong>PHP API:</strong> " . PHP_API_VERSION . "</p>
<p><strong>Zend Engine:</strong> " . zend_version() . "</p>
<p><strong>Operating System:</strong> " . php_uname('s') . " " . php_uname('r') . "</p>
</div>";

// Check if extension is already loaded
if (extension_loaded('mongodb')) {
    echo "<div class='success'>
    <h3>✅ MongoDB Extension Already Installed</h3>
    <p>MongoDB PHP extension version: " . phpversion('mongodb') . "</p>
    <p>Extension is working properly!</p>
    </div>";
} else {
    echo "<div class='warning'>
    <h3>⚠️ MongoDB Extension Not Found</h3>
    <p>MongoDB PHP extension is not installed or not loaded.</p>
    </div>";
}

echo "</div>";

// Installation guide
echo "<div class='guide-section'>
<h2>🚀 เลือกระบบปฏิบัติการ</h2>
<div class='os-tabs'>
    <button class='tab-btn active' onclick='showTab(\"windows\")'>🪟 Windows</button>
    <button class='tab-btn' onclick='showTab(\"linux\")'>🐧 Linux</button>
    <button class='tab-btn' onclick='showTab(\"macos\")'>🍎 macOS</button>
    <button class='tab-btn' onclick='showTab(\"docker\")'>🐳 Docker</button>
</div>";

// Windows Installation
echo "<div id='windows' class='tab-content active'>
<h3>🪟 Windows Installation (XAMPP/WAMP)</h3>

<div class='step'>
<h4>ขั้นตอนที่ 1: ดาวน์โหลด MongoDB PHP Extension</h4>
<p>ดาวน์โหลดไฟล์ที่เหมาะสมกับระบบของคุณ:</p>

<div class='download-links'>
    <div class='download-card'>
        <h4>PHP 8.2 (x64, NTS)</h4>
        <p>สำหรับ XAMPP ล่าสุด</p>
        <a href='https://pecl.php.net/package/mongodb' class='btn' target='_blank'>ดาวน์โหลด</a>
    </div>
    <div class='download-card'>
        <h4>PHP 8.1 (x64, NTS)</h4>
        <p>สำหรับ XAMPP รุ่นก่อน</p>
        <a href='https://pecl.php.net/package/mongodb' class='btn' target='_blank'>ดาวน์โหลด</a>
    </div>
    <div class='download-card'>
        <h4>PHP 8.0 (x64, NTS)</h4>
        <p>สำหรับ XAMPP เก่า</p>
        <a href='https://pecl.php.net/package/mongodb' class='btn' target='_blank'>ดาวน์โหลด</a>
    </div>
</div>

<div class='warning'>
<strong>⚠️ สำคัญ:</strong> เลือกเวอร์ชันที่ตรงกับ PHP ของคุณ ($phpVersion, $phpArch, $isThreadSafe)
</div>
</div>

<div class='step'>
<h4>ขั้นตอนที่ 2: ติดตั้งไฟล์ Extension</h4>
<div class='code-block'>
1. แตกไฟล์ที่ดาวน์โหลดมา
2. คัดลอก php_mongodb.dll ไปยัง xampp/php/ext/
3. เปิดไฟล์ xampp/php/php.ini
4. เพิ่มบรรทัด: extension=mongodb
5. บันทึกไฟล์และรีสตาร์ท Apache
</div>
</div>

<div class='step'>
<h4>ขั้นตอนที่ 3: ตรวจสอบการติดตั้ง</h4>
<div class='code-block'>
# เปิด Command Prompt และรันคำสั่ง:
cd C:\\xampp\\php
php -m | findstr mongodb

# หรือสร้างไฟล์ PHP ทดสอบ:
&lt;?php
if (extension_loaded('mongodb')) {
    echo 'MongoDB extension is loaded!';
} else {
    echo 'MongoDB extension is NOT loaded!';
}
?&gt;
</div>
</div>

<div class='step'>
<h4>🔧 แก้ไขปัญหาที่พบบ่อย (Windows)</h4>
<ul>
<li><strong>Extension ไม่โหลด:</strong> ตรวจสอบ path ใน php.ini</li>
<li><strong>DLL ไม่ถูกต้อง:</strong> ดาวน์โหลดเวอร์ชันที่ตรงกับ PHP</li>
<li><strong>Apache ไม่เริ่ม:</strong> ตรวจสอบ error log</li>
</ul>
</div>
</div>";

// Linux Installation
echo "<div id='linux' class='tab-content'>
<h3>🐧 Linux Installation</h3>

<div class='step'>
<h4>Ubuntu/Debian:</h4>
<div class='code-block'>
# อัปเดต package list
sudo apt update

# ติดตั้ง MongoDB PHP extension
sudo apt install php-mongodb

# รีสตาร์ท web server
sudo systemctl restart apache2
# หรือ
sudo systemctl restart nginx
</div>
</div>

<div class='step'>
<h4>CentOS/RHEL/Fedora:</h4>
<div class='code-block'>
# ติดตั้งผ่าน yum/dnf
sudo yum install php-mongodb
# หรือ
sudo dnf install php-mongodb

# รีสตาร์ท web server
sudo systemctl restart httpd
</div>
</div>

<div class='step'>
<h4>ติดตั้งผ่าน PECL:</h4>
<div class='code-block'>
# ติดตั้ง development tools
sudo apt install php-dev php-pear

# ติดตั้ง MongoDB extension
sudo pecl install mongodb

# เพิ่มใน php.ini
echo 'extension=mongodb' | sudo tee -a /etc/php/8.2/apache2/php.ini
echo 'extension=mongodb' | sudo tee -a /etc/php/8.2/cli/php.ini

# รีสตาร์ท Apache
sudo systemctl restart apache2
</div>
</div>

<div class='step'>
<h4>🔧 แก้ไขปัญหาที่พบบ่อย (Linux)</h4>
<ul>
<li><strong>Permission denied:</strong> ใช้ sudo ในการติดตั้ง</li>
<li><strong>Package not found:</strong> อัปเดต package list</li>
<li><strong>PHP version mismatch:</strong> ระบุเวอร์ชัน PHP ที่ถูกต้อง</li>
</ul>
</div>
</div>";

// macOS Installation
echo "<div id='macos' class='tab-content'>
<h3>🍎 macOS Installation</h3>

<div class='step'>
<h4>ติดตั้งผ่าน Homebrew:</h4>
<div class='code-block'>
# ติดตั้ง Homebrew (ถ้ายังไม่มี)
/bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"

# ติดตั้ง PHP และ MongoDB extension
brew install php
brew install mongodb/brew/mongodb-community

# ติดตั้ง MongoDB PHP extension
pecl install mongodb

# เพิ่มใน php.ini
echo 'extension=mongodb' >> /usr/local/etc/php/8.2/php.ini

# รีสตาร์ท web server
brew services restart httpd
</div>
</div>

<div class='step'>
<h4>ติดตั้งผ่าน MacPorts:</h4>
<div class='code-block'>
# ติดตั้ง MongoDB extension
sudo port install php82-mongodb

# รีสตาร์ท web server
sudo port unload apache2
sudo port load apache2
</div>
</div>
</div>";

// Docker Installation
echo "<div id='docker' class='tab-content'>
<h3>🐳 Docker Installation</h3>

<div class='step'>
<h4>Dockerfile Example:</h4>
<div class='code-block'>
FROM php:8.2-apache

# ติดตั้ง dependencies
RUN apt-get update && apt-get install -y \\
    libssl-dev \\
    libcurl4-openssl-dev \\
    pkg-config

# ติดตั้ง MongoDB extension
RUN pecl install mongodb \\
    && docker-php-ext-enable mongodb

# คัดลอกไฟล์โปรเจค
COPY . /var/www/html/

# ตั้งค่า permissions
RUN chown -R www-data:www-data /var/www/html
</div>
</div>

<div class='step'>
<h4>Docker Compose Example:</h4>
<div class='code-block'>
version: '3.8'
services:
  web:
    build: .
    ports:
      - \"80:80\"
    volumes:
      - .:/var/www/html
    depends_on:
      - mongodb
      
  mongodb:
    image: mongo:6.0
    ports:
      - \"27017:27017\"
    volumes:
      - mongodb_data:/data/db
      
volumes:
  mongodb_data:
</div>
</div>
</div>";

echo "</div>"; // End guide-section

// Verification
echo "<div class='guide-section'>
<h2>✅ ตรวจสอบการติดตั้ง</h2>

<div class='step'>
<h4>วิธีที่ 1: ผ่าน Command Line</h4>
<div class='code-block'>
php -m | grep mongodb
</div>
</div>

<div class='step'>
<h4>วิธีที่ 2: ผ่าน PHP Script</h4>
<div class='code-block'>
&lt;?php
if (extension_loaded('mongodb')) {
    echo 'MongoDB extension version: ' . phpversion('mongodb');
} else {
    echo 'MongoDB extension is not loaded';
}
?&gt;
</div>
</div>

<div class='step'>
<h4>วิธีที่ 3: ผ่าน phpinfo()</h4>
<div class='code-block'>
&lt;?php phpinfo(); ?&gt;
</div>
<p>ค้นหา \"mongodb\" ในหน้า phpinfo</p>
</div>
</div>";

// Test connection
echo "<div class='guide-section'>
<h2>🧪 ทดสอบการเชื่อมต่อ</h2>

<div class='step'>
<h4>ทดสอบการเชื่อมต่อ MongoDB:</h4>
<div class='code-block'>
&lt;?php
try {
    \$client = new MongoDB\\Client(\"mongodb://localhost:27017\");
    \$db = \$client->selectDatabase('test');
    \$result = \$db->command(['ping' => 1]);
    echo 'MongoDB connection successful!';
} catch (Exception \$e) {
    echo 'MongoDB connection failed: ' . \$e->getMessage();
}
?&gt;
</div>
</div>

<p><a href='mongodb_setup_check.php' class='btn'>🔍 ตรวจสอบการติดตั้งแบบละเอียด</a></p>
<p><a href='mongodb_test.php' class='btn primary'>🧪 ทดสอบระบบ MongoDB</a></p>
</div>";

// Troubleshooting
echo "<div class='guide-section'>
<h2>🔧 แก้ไขปัญหา</h2>

<div class='error'>
<h4>❌ ปัญหาที่พบบ่อย:</h4>
<ul>
<li><strong>Extension ไม่โหลด:</strong> ตรวจสอบ php.ini และ extension path</li>
<li><strong>Version mismatch:</strong> ดาวน์โหลด extension ที่ตรงกับ PHP version</li>
<li><strong>Permission denied:</strong> ตรวจสอบสิทธิ์ไฟล์</li>
<li><strong>Missing dependencies:</strong> ติดตั้ง required libraries</li>
</ul>
</div>

<div class='step'>
<h4>🔍 Debug Commands:</h4>
<div class='code-block'>
# ตรวจสอบ PHP version และ extensions
php -v
php -m

# ตรวจสอบ php.ini location
php --ini

# ตรวจสอบ MongoDB service
# Windows:
net start | findstr MongoDB

# Linux/macOS:
sudo systemctl status mongod
brew services list | grep mongodb
</div>
</div>
</div>";

echo "<script>
function showTab(tabName) {
    // Hide all tab contents
    const contents = document.querySelectorAll('.tab-content');
    contents.forEach(content => content.classList.remove('active'));
    
    // Remove active class from all buttons
    const buttons = document.querySelectorAll('.tab-btn');
    buttons.forEach(btn => btn.classList.remove('active'));
    
    // Show selected tab content
    document.getElementById(tabName).classList.add('active');
    
    // Add active class to clicked button
    event.target.classList.add('active');
}

// Auto-detect OS and show appropriate tab
const userAgent = navigator.userAgent;
if (userAgent.includes('Windows')) {
    showTab('windows');
} else if (userAgent.includes('Mac')) {
    showTab('macos');
} else if (userAgent.includes('Linux')) {
    showTab('linux');
}
</script>";

echo "</body></html>";
?>
