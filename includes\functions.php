<?php
// Functions for Asset Management System

// Set timezone to Thailand
date_default_timezone_set('Asia/Bangkok');

// Helper functions
function getThailandDateTime() {
    $timezone = new DateTimeZone('Asia/Bangkok');
    $datetime = new DateTime('now', $timezone);
    return $datetime->format('Y-m-d H:i:s');
}

function formatDate($date) {
    if (empty($date)) return '-';
    $timezone = new DateTimeZone('Asia/Bangkok');
    $datetime = new DateTime($date);
    $datetime->setTimezone($timezone);
    return $datetime->format('d/m/Y');
}

function formatDateTime($datetime) {
    if (empty($datetime)) return '-';
    $timezone = new DateTimeZone('Asia/Bangkok');
    $dt = new DateTime($datetime);
    $dt->setTimezone($timezone);
    return $dt->format('d/m/Y H:i:s');
}

function getStatusBadge($status) {
    $badges = [
        'ใช้งาน' => 'success',
        'ชำรุด' => 'danger',
        'สำรอง' => 'warning'
    ];
    $class = $badges[$status] ?? 'secondary';
    return "<span class='badge badge-{$class}'>{$status}</span>";
}

// Auto Backup Functions
require_once 'auto_backup.php';

/**
 * Trigger backup when asset is added
 */
function triggerAssetAddBackup($assetId, $assetData) {
    AutoBackup::triggerBackup('asset_add', [
        'asset_id' => $assetId,
        'asset_data' => $assetData,
        'user' => getCurrentUserFullName(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * Trigger backup when asset is edited
 */
function triggerAssetEditBackup($assetId, $oldData, $newData) {
    AutoBackup::triggerBackup('asset_edit', [
        'asset_id' => $assetId,
        'old_data' => $oldData,
        'new_data' => $newData,
        'user' => getCurrentUserFullName(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * Trigger backup when asset is deleted
 */
function triggerAssetDeleteBackup($assetId, $assetData) {
    AutoBackup::triggerBackup('asset_delete', [
        'asset_id' => $assetId,
        'asset_data' => $assetData,
        'user' => getCurrentUserFullName(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * Trigger backup when user is added
 */
function triggerUserAddBackup($userId, $userData) {
    AutoBackup::triggerBackup('user_add', [
        'user_id' => $userId,
        'username' => $userData['username'] ?? '',
        'full_name' => $userData['full_name'] ?? '',
        'role' => $userData['role'] ?? '',
        'created_by' => getCurrentUserFullName(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * Trigger backup when user is edited
 */
function triggerUserEditBackup($userId, $oldData, $newData) {
    AutoBackup::triggerBackup('user_edit', [
        'user_id' => $userId,
        'old_data' => $oldData,
        'new_data' => $newData,
        'modified_by' => getCurrentUserFullName(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * Trigger backup when user is deleted
 */
function triggerUserDeleteBackup($userId, $userData) {
    AutoBackup::triggerBackup('user_delete', [
        'user_id' => $userId,
        'user_data' => $userData,
        'deleted_by' => getCurrentUserFullName(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
