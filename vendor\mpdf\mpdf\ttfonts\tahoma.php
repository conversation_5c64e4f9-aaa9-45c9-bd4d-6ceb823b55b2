<?php
$type = 'TrueType';
$name = 'Tahoma';
$desc = array('Ascent'=>765,'Descent'=>-207,'CapHeight'=>765,'Flags'=>32,'FontBBox'=>'[-600 -207 1338 1034]','ItalicAngle'=>0,'StemV'=>70,'MissingWidth'=>1000);
$up = -83;
$ut = 63;
$cw = array(
	chr(0)=>1000,chr(1)=>1000,chr(2)=>1000,chr(3)=>1000,chr(4)=>1000,chr(5)=>1000,chr(6)=>1000,chr(7)=>1000,chr(8)=>1000,chr(9)=>1000,chr(10)=>1000,chr(11)=>1000,chr(12)=>1000,chr(13)=>1000,chr(14)=>1000,chr(15)=>1000,chr(16)=>1000,chr(17)=>1000,chr(18)=>1000,chr(19)=>1000,chr(20)=>1000,chr(21)=>1000,
	chr(22)=>1000,chr(23)=>1000,chr(24)=>1000,chr(25)=>1000,chr(26)=>1000,chr(27)=>1000,chr(28)=>1000,chr(29)=>1000,chr(30)=>1000,chr(31)=>1000,' '=>313,'!'=>332,'"'=>401,'#'=>728,'$'=>546,'%'=>977,'&'=>674,'\''=>211,'('=>383,')'=>383,'*'=>546,'+'=>728,
	','=>303,'-'=>363,'.'=>303,'/'=>382,'0'=>546,'1'=>546,'2'=>546,'3'=>546,'4'=>546,'5'=>546,'6'=>546,'7'=>546,'8'=>546,'9'=>546,':'=>354,';'=>354,'<'=>728,'='=>728,'>'=>728,'?'=>474,'@'=>909,'A'=>600,
	'B'=>589,'C'=>601,'D'=>678,'E'=>561,'F'=>521,'G'=>667,'H'=>675,'I'=>373,'J'=>417,'K'=>588,'L'=>498,'M'=>771,'N'=>667,'O'=>708,'P'=>551,'Q'=>708,'R'=>621,'S'=>557,'T'=>584,'U'=>656,'V'=>597,'W'=>902,
	'X'=>581,'Y'=>576,'Z'=>559,'['=>383,'\\'=>382,']'=>383,'^'=>728,'_'=>546,'`'=>546,'a'=>525,'b'=>553,'c'=>461,'d'=>553,'e'=>526,'f'=>318,'g'=>553,'h'=>558,'i'=>229,'j'=>282,'k'=>498,'l'=>229,'m'=>840,
	'n'=>558,'o'=>543,'p'=>553,'q'=>553,'r'=>360,'s'=>446,'t'=>334,'u'=>558,'v'=>498,'w'=>742,'x'=>495,'y'=>498,'z'=>444,'{'=>480,'|'=>382,'}'=>480,'~'=>728,chr(127)=>1000,chr(128)=>546,chr(129)=>1000,chr(130)=>1000,chr(131)=>1000,
	chr(132)=>1000,chr(133)=>817,chr(134)=>1000,chr(135)=>1000,chr(136)=>1000,chr(137)=>1000,chr(138)=>1000,chr(139)=>1000,chr(140)=>1000,chr(141)=>1000,chr(142)=>1000,chr(143)=>1000,chr(144)=>1000,chr(145)=>211,chr(146)=>211,chr(147)=>401,chr(148)=>401,chr(149)=>455,chr(150)=>546,chr(151)=>909,chr(152)=>1000,chr(153)=>1000,
	chr(154)=>1000,chr(155)=>1000,chr(156)=>1000,chr(157)=>1000,chr(158)=>1000,chr(159)=>1000,chr(160)=>313,chr(161)=>595,chr(162)=>613,chr(163)=>655,chr(164)=>615,chr(165)=>615,chr(166)=>673,chr(167)=>492,chr(168)=>556,chr(169)=>620,chr(170)=>627,chr(171)=>688,chr(172)=>862,chr(173)=>832,chr(174)=>621,chr(175)=>621,
	chr(176)=>556,chr(177)=>809,chr(178)=>872,chr(179)=>853,chr(180)=>615,chr(181)=>615,chr(182)=>585,chr(183)=>716,chr(184)=>540,chr(185)=>612,chr(186)=>630,chr(187)=>630,chr(188)=>632,chr(189)=>632,chr(190)=>708,chr(191)=>698,chr(192)=>621,chr(193)=>599,chr(194)=>579,chr(195)=>429,chr(196)=>567,chr(197)=>616,
	chr(198)=>621,chr(199)=>485,chr(200)=>615,chr(201)=>744,chr(202)=>608,chr(203)=>670,chr(204)=>715,chr(205)=>595,chr(206)=>595,chr(207)=>529,chr(208)=>448,chr(209)=>0,chr(210)=>485,chr(211)=>485,chr(212)=>0,chr(213)=>0,chr(214)=>0,chr(215)=>0,chr(216)=>0,chr(217)=>0,chr(218)=>0,chr(219)=>1000,
	chr(220)=>1000,chr(221)=>1000,chr(222)=>1000,chr(223)=>589,chr(224)=>332,chr(225)=>592,chr(226)=>492,chr(227)=>442,chr(228)=>511,chr(229)=>485,chr(230)=>499,chr(231)=>0,chr(232)=>0,chr(233)=>0,chr(234)=>0,chr(235)=>0,chr(236)=>0,chr(237)=>0,chr(238)=>0,chr(239)=>602,chr(240)=>602,chr(241)=>632,
	chr(242)=>685,chr(243)=>688,chr(244)=>733,chr(245)=>733,chr(246)=>646,chr(247)=>774,chr(248)=>774,chr(249)=>715,chr(250)=>694,chr(251)=>1065,chr(252)=>1000,chr(253)=>1000,chr(254)=>1000,chr(255)=>1000);
$enc = 'cp874';
$diff = '130 /.notdef /.notdef /.notdef 134 /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef 142 /.notdef 152 /.notdef /.notdef /.notdef /.notdef /.notdef 158 /.notdef /.notdef 161 /kokaithai /khokhaithai /khokhuatthai /khokhwaithai /khokhonthai /khorakhangthai /ngonguthai /chochanthai /chochingthai /chochangthai /sosothai /chochoethai /yoyingthai /dochadathai /topatakthai /thothanthai /thonangmonthothai /thophuthaothai /nonenthai /dodekthai /totaothai /thothungthai /thothahanthai /thothongthai /nonuthai /bobaimaithai /poplathai /phophungthai /fofathai /phophanthai /fofanthai /phosamphaothai /momathai /yoyakthai /roruathai /ruthai /lolingthai /luthai /wowaenthai /sosalathai /sorusithai /sosuathai /hohipthai /lochulathai /oangthai /honokhukthai /paiyannoithai /saraathai /maihanakatthai /saraaathai /saraamthai /saraithai /saraiithai /sarauethai /saraueethai /sarauthai /sarauuthai /phinthuthai /.notdef /.notdef /.notdef /.notdef /bahtthai /saraethai /saraaethai /saraothai /saraaimaimuanthai /saraaimaimalaithai /lakkhangyaothai /maiyamokthai /maitaikhuthai /maiekthai /maithothai /maitrithai /maichattawathai /thanthakhatthai /nikhahitthai /yamakkanthai /fongmanthai /zerothai /onethai /twothai /threethai /fourthai /fivethai /sixthai /seventhai /eightthai /ninethai /angkhankhuthai /khomutthai /.notdef /.notdef /.notdef /.notdef';
$uv = array(0=>array(0,128),128=>8364,133=>8230,145=>array(8216,2),147=>array(8220,2),149=>8226,150=>array(8211,2),160=>160,161=>array(3585,58),223=>array(3647,29));
$file = 'tahoma.z';
$originalsize = 72388;
$subsetted = true;
?>
