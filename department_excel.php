<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// ตรวจสอบการล็อกอิน
requireLogin();

// สร้าง AssetManager instance
$assetManager = new AssetManager($pdo);

// รับค่าแผนกที่เลือก
$selected_department = $_GET['department'] ?? '';

if (empty($selected_department)) {
    die('Error: กรุณาเลือกแผนก');
}

// ดึงข้อมูล assets ของแผนกที่เลือก
$assets = $assetManager->getAllAssets('', '', '', '', '', $selected_department);

// สร้าง Spreadsheet object
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// ตั้งชื่อ worksheet
$sheet->setTitle('Department Report');

// ตั้งค่า metadata
$spreadsheet->getProperties()
    ->setCreator('Asset Management System')
    ->setLastModifiedBy(getCurrentUserFullName())
    ->setTitle('รายงาน Asset แผนก ' . $selected_department)
    ->setSubject('Department Asset Report')
    ->setDescription('รายงานข้อมูล Asset ของแผนก ' . $selected_department)
    ->setKeywords('asset management department report excel')
    ->setCategory('Department Report');

// สร้าง Header
$sheet->setCellValue('A1', 'รายงาน Asset แผนก ' . $selected_department);
$sheet->mergeCells('A1:K1');
$sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
$sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

// ข้อมูลรายงาน
$sheet->setCellValue('A2', 'วันที่สร้าง: ' . date('d/m/Y H:i:s'));
$sheet->setCellValue('A3', 'ผู้สร้าง: ' . getCurrentUserFullName());
$sheet->setCellValue('A4', 'แผนก: ' . $selected_department);
$sheet->setCellValue('A5', 'จำนวนรายการ: ' . count($assets) . ' รายการ');

$row = 7; // เว้นบรรทัด

// Header ของตาราง
$headers = [
    'A' => 'ลำดับ',
    'B' => 'Asset ID',
    'C' => 'Tag',
    'D' => 'ประเภท',
    'E' => 'ยี่ห้อ',
    'F' => 'รุ่น',
    'G' => 'Hostname',
    'H' => 'OS',
    'I' => 'Serial Number',
    'J' => 'สถานะ',
    'K' => 'วันที่หมดประกัน'
];

foreach ($headers as $col => $header) {
    $sheet->setCellValue($col . $row, $header);
}

// จัดรูปแบบ header
$headerRange = 'A' . $row . ':K' . $row;
$sheet->getStyle($headerRange)->getFont()->setBold(true);
$sheet->getStyle($headerRange)->getFill()
    ->setFillType(Fill::FILL_SOLID)
    ->getStartColor()->setARGB('FF4472C4');
$sheet->getStyle($headerRange)->getFont()->getColor()->setARGB('FFFFFFFF');
$sheet->getStyle($headerRange)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

// เพิ่มข้อมูล
$dataStartRow = $row + 1;
$currentRow = $dataStartRow;

if (!empty($assets)) {
    $sequenceNumber = 1;
    foreach ($assets as $asset) {
        // Format warranty date
        $warranty_exp = '';
        if (!empty($asset['warranty_expire'])) {
            $warranty_exp = date('d/m/Y', strtotime($asset['warranty_expire']));
        }
        
        $sheet->setCellValue('A' . $currentRow, $sequenceNumber);
        $sheet->setCellValue('B' . $currentRow, $asset['asset_id'] ?? '');
        $sheet->setCellValue('C' . $currentRow, $asset['tag'] ?? '');
        $sheet->setCellValue('D' . $currentRow, $asset['type'] ?? '');
        $sheet->setCellValue('E' . $currentRow, $asset['brand'] ?? '');
        $sheet->setCellValue('F' . $currentRow, $asset['model'] ?? '');
        $sheet->setCellValue('G' . $currentRow, $asset['hostname'] ?? '');
        $sheet->setCellValue('H' . $currentRow, $asset['operating_system'] ?? '');
        $sheet->setCellValue('I' . $currentRow, $asset['serial_number'] ?? '');
        $sheet->setCellValue('J' . $currentRow, $asset['status'] ?? '');
        $sheet->setCellValue('K' . $currentRow, $warranty_exp);
        
        // จัดรูปแบบสถานะ
        $status = $asset['status'] ?? '';
        if ($status == 'ใช้งาน') {
            $sheet->getStyle('J' . $currentRow)->getFont()->getColor()->setARGB('FF28A745');
            $sheet->getStyle('J' . $currentRow)->getFont()->setBold(true);
        } elseif ($status == 'ชำรุด') {
            $sheet->getStyle('J' . $currentRow)->getFont()->getColor()->setARGB('FFDC3545');
            $sheet->getStyle('J' . $currentRow)->getFont()->setBold(true);
        } elseif ($status == 'สำรอง') {
            $sheet->getStyle('J' . $currentRow)->getFont()->getColor()->setARGB('FFFFC107');
            $sheet->getStyle('J' . $currentRow)->getFont()->setBold(true);
        }
        
        $currentRow++;
        $sequenceNumber++;
    }
} else {
    $sheet->setCellValue('A' . $currentRow, 'ไม่พบข้อมูล Asset ในแผนก ' . $selected_department);
    $sheet->mergeCells('A' . $currentRow . ':K' . $currentRow);
    $sheet->getStyle('A' . $currentRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    $sheet->getStyle('A' . $currentRow)->getFont()->setItalic(true);
    $currentRow++;
}

// จัดรูปแบบตาราง
if (!empty($assets)) {
    $dataRange = 'A' . $row . ':K' . ($currentRow - 1);
    $sheet->getStyle($dataRange)->getBorders()->getAllBorders()
        ->setBorderStyle(Border::BORDER_THIN);
    
    // Zebra striping
    for ($i = $dataStartRow; $i < $currentRow; $i += 2) {
        $sheet->getStyle('A' . $i . ':K' . $i)->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setARGB('FFF8F9FA');
    }
}

// เพิ่มส่วนสรุป
if (!empty($assets)) {
    $summaryStartRow = $currentRow + 2;
    
    // หัวข้อสรุป
    $sheet->setCellValue('A' . $summaryStartRow, 'สรุปรายงานแผนก ' . $selected_department);
    $sheet->mergeCells('A' . $summaryStartRow . ':K' . $summaryStartRow);
    $sheet->getStyle('A' . $summaryStartRow)->getFont()->setBold(true)->setSize(14);
    $sheet->getStyle('A' . $summaryStartRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    
    $summaryRow = $summaryStartRow + 2;
    
    // สรุปสถานะ
    $statusCount = [];
    $typeCount = [];
    
    foreach ($assets as $asset) {
        $status = $asset['status'] ?? 'ไม่ระบุ';
        $type = $asset['type'] ?? 'ไม่ระบุ';
        
        $statusCount[$status] = ($statusCount[$status] ?? 0) + 1;
        $typeCount[$type] = ($typeCount[$type] ?? 0) + 1;
    }
    
    // สถานะ Asset
    $sheet->setCellValue('A' . $summaryRow, 'สถานะ Asset');
    $sheet->getStyle('A' . $summaryRow)->getFont()->setBold(true);
    $summaryRow++;
    
    foreach ($statusCount as $status => $count) {
        $percentage = round(($count / count($assets)) * 100, 1);
        $sheet->setCellValue('A' . $summaryRow, $status);
        $sheet->setCellValue('B' . $summaryRow, $count . ' (' . $percentage . '%)');
        
        // จัดสีตามสถานะ
        if ($status == 'ใช้งาน') {
            $sheet->getStyle('A' . $summaryRow . ':B' . $summaryRow)->getFont()->getColor()->setARGB('FF28A745');
        } elseif ($status == 'ชำรุด') {
            $sheet->getStyle('A' . $summaryRow . ':B' . $summaryRow)->getFont()->getColor()->setARGB('FFDC3545');
        } elseif ($status == 'สำรอง') {
            $sheet->getStyle('A' . $summaryRow . ':B' . $summaryRow)->getFont()->getColor()->setARGB('FFFFC107');
        }
        
        $summaryRow++;
    }
    
    $summaryRow++; // เว้นบรรทัด
    
    // ประเภท Asset
    $sheet->setCellValue('A' . $summaryRow, 'ประเภท Asset');
    $sheet->getStyle('A' . $summaryRow)->getFont()->setBold(true);
    $summaryRow++;
    
    arsort($typeCount);
    $topTypes = array_slice($typeCount, 0, 10, true);
    foreach ($topTypes as $type => $count) {
        $percentage = round(($count / count($assets)) * 100, 1);
        $sheet->setCellValue('A' . $summaryRow, $type);
        $sheet->setCellValue('B' . $summaryRow, $count . ' (' . $percentage . '%)');
        $summaryRow++;
    }
    
    $summaryRow++; // เว้นบรรทัด
    
    // รวมทั้งหมด
    $sheet->setCellValue('A' . $summaryRow, 'รวม Asset ในแผนก ' . $selected_department . ': ' . count($assets) . ' รายการ');
    $sheet->mergeCells('A' . $summaryRow . ':K' . $summaryRow);
    $sheet->getStyle('A' . $summaryRow)->getFont()->setBold(true);
    $sheet->getStyle('A' . $summaryRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    $sheet->getStyle('A' . $summaryRow)->getFill()
        ->setFillType(Fill::FILL_SOLID)
        ->getStartColor()->setARGB('FFE3F2FD');
}

// ปรับความกว้างคอลัมน์อัตโนมัติ
foreach (range('A', 'K') as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}

// จัดตำแหน่งข้อมูล
if (!empty($assets)) {
    $sheet->getStyle('A' . $dataStartRow . ':A' . ($currentRow - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // ลำดับ
    $sheet->getStyle('J' . $dataStartRow . ':J' . ($currentRow - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // สถานะ
    $sheet->getStyle('K' . $dataStartRow . ':K' . ($currentRow - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // วันที่
}

// สร้างไฟล์ Excel
$writer = new Xlsx($spreadsheet);

// ตั้งชื่อไฟล์
$filename = 'department_report_' . str_replace(' ', '_', $selected_department) . '_' . date('Y-m-d_H-i-s') . '.xlsx';

// ส่งออกไฟล์
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="' . $filename . '"');
header('Cache-Control: max-age=0');
header('Cache-Control: max-age=1');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
header('Cache-Control: cache, must-revalidate');
header('Pragma: public');

$writer->save('php://output');
exit;
?>
