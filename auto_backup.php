<?php
/**
 * Auto Backup Script
 * ไฟล์นี้ใช้สำหรับการทำ backup อัตโนมัติผ่าน Cron Job
 * 
 * วิธีการตั้งค่า Cron Job:
 * 0 2 * * * cd /path/to/your/project && php auto_backup.php
 */

// ตั้งค่าเวลาให้เป็น Thailand
date_default_timezone_set('Asia/Bangkok');

// เริ่มต้น log
$logFile = 'logs/backup_' . date('Y-m-d') . '.log';
$logDir = dirname($logFile);

// สร้างโฟลเดอร์ logs ถ้ายังไม่มี
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

function writeLog($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    echo $logMessage; // แสดงใน console ด้วย
}

writeLog("=== Auto Backup Started ===");

try {
    // โหลดการตั้งค่าฐานข้อมูล
    require_once 'includes/config.php';
    
    writeLog("Database connection established");
    
    // สร้างโฟลเดอร์ backup ถ้ายังไม่มี
    $backupDir = 'backups/';
    if (!is_dir($backupDir)) {
        mkdir($backupDir, 0755, true);
        writeLog("Created backup directory: $backupDir");
    }
    
    // ชื่อไฟล์ backup
    $filename = 'auto_backup_' . date('Y-m-d_H-i-s') . '.sql';
    $filepath = $backupDir . $filename;
    
    writeLog("Starting backup to: $filename");
    
    // เริ่มสร้างไฟล์ backup
    $backup = "-- Asset Management System Auto Backup\n";
    $backup .= "-- Created: " . date('Y-m-d H:i:s') . "\n";
    $backup .= "-- Type: Automatic Backup\n";
    $backup .= "-- Database: asset_management\n\n";
    
    $backup .= "SET FOREIGN_KEY_CHECKS = 0;\n";
    $backup .= "SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';\n";
    $backup .= "SET AUTOCOMMIT = 0;\n";
    $backup .= "START TRANSACTION;\n\n";
    
    // ดึงรายชื่อตาราง
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    writeLog("Found " . count($tables) . " tables to backup");
    
    foreach ($tables as $table) {
        writeLog("Backing up table: $table");
        
        // สร้าง DROP TABLE
        $backup .= "-- Table: $table\n";
        $backup .= "DROP TABLE IF EXISTS `$table`;\n";
        
        // สร้าง CREATE TABLE
        $createTable = $pdo->query("SHOW CREATE TABLE `$table`")->fetch(PDO::FETCH_ASSOC);
        $backup .= $createTable['Create Table'] . ";\n\n";
        
        // ดึงข้อมูลในตาราง
        $stmt = $pdo->query("SELECT * FROM `$table`");
        $rowCount = 0;
        
        // ใช้ prepared statement เพื่อประสิทธิภาพ
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            if ($rowCount === 0) {
                $columns = array_keys($row);
                $backup .= "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES\n";
            }
            
            $values = [];
            foreach ($row as $value) {
                if ($value === null) {
                    $values[] = 'NULL';
                } else {
                    $values[] = "'" . addslashes($value) . "'";
                }
            }
            
            if ($rowCount > 0) {
                $backup .= ",\n";
            }
            $backup .= '(' . implode(', ', $values) . ')';
            $rowCount++;
        }
        
        if ($rowCount > 0) {
            $backup .= ";\n\n";
        }
        
        writeLog("Table $table: $rowCount rows backed up");
    }
    
    $backup .= "COMMIT;\n";
    $backup .= "SET FOREIGN_KEY_CHECKS = 1;\n";
    $backup .= "-- Backup completed at " . date('Y-m-d H:i:s') . "\n";
    
    // บันทึกไฟล์
    if (file_put_contents($filepath, $backup)) {
        $fileSize = filesize($filepath);
        writeLog("Backup completed successfully");
        writeLog("File: $filename");
        writeLog("Size: " . formatBytes($fileSize));
        
        // ส่งอีเมลแจ้งเตือน (ถ้าต้องการ)
        sendBackupNotification($filename, $fileSize, true);
        
    } else {
        writeLog("ERROR: Failed to write backup file");
        sendBackupNotification($filename, 0, false, "Failed to write backup file");
    }
    
} catch (Exception $e) {
    $errorMessage = "ERROR: " . $e->getMessage();
    writeLog($errorMessage);
    sendBackupNotification('', 0, false, $errorMessage);
}

// ทำความสะอาดไฟล์ backup เก่า (เก็บไว้ 30 วัน)
try {
    writeLog("Cleaning old backup files...");
    $deleted = cleanOldBackups(30);
    writeLog("Deleted $deleted old backup files");
} catch (Exception $e) {
    writeLog("ERROR cleaning old backups: " . $e->getMessage());
}

writeLog("=== Auto Backup Completed ===\n");

// ฟังก์ชันช่วยเหลือ
function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

function cleanOldBackups($keepDays = 30) {
    $backupDir = 'backups/';
    $files = glob($backupDir . '*.sql');
    $deleted = 0;
    $cutoffTime = time() - ($keepDays * 24 * 60 * 60);
    
    foreach ($files as $file) {
        if (filemtime($file) < $cutoffTime) {
            if (unlink($file)) {
                $deleted++;
                writeLog("Deleted old backup: " . basename($file));
            }
        }
    }
    
    return $deleted;
}

function sendBackupNotification($filename, $fileSize, $success, $errorMessage = '') {
    // ตัวอย่างการส่งอีเมลแจ้งเตือน
    // คุณสามารถปรับแต่งให้เหมาะสมกับระบบของคุณ
    
    $to = '<EMAIL>'; // เปลี่ยนเป็นอีเมลของคุณ
    $subject = $success ? 'Backup สำเร็จ - Asset Management System' : 'Backup ล้มเหลว - Asset Management System';
    
    if ($success) {
        $message = "การ backup ฐานข้อมูลสำเร็จ\n\n";
        $message .= "ไฟล์: $filename\n";
        $message .= "ขนาด: " . formatBytes($fileSize) . "\n";
        $message .= "เวลา: " . date('Y-m-d H:i:s') . "\n";
    } else {
        $message = "การ backup ฐานข้อมูลล้มเหลว\n\n";
        $message .= "ข้อผิดพลาด: $errorMessage\n";
        $message .= "เวลา: " . date('Y-m-d H:i:s') . "\n";
    }
    
    $headers = 'From: <EMAIL>' . "\r\n" .
               'Reply-To: <EMAIL>' . "\r\n" .
               'X-Mailer: PHP/' . phpversion();
    
    // ยกเลิก comment บรรทัดด้านล่างถ้าต้องการส่งอีเมล
    // mail($to, $subject, $message, $headers);
    
    writeLog("Notification prepared: " . ($success ? 'Success' : 'Failed'));
}

// ฟังก์ชันตรวจสอบพื้นที่ดิสก์
function checkDiskSpace() {
    $freeBytes = disk_free_space('.');
    $totalBytes = disk_total_space('.');
    $usedBytes = $totalBytes - $freeBytes;
    $percentUsed = ($usedBytes / $totalBytes) * 100;
    
    writeLog("Disk usage: " . formatBytes($usedBytes) . " / " . formatBytes($totalBytes) . " (" . round($percentUsed, 2) . "%)");
    
    if ($percentUsed > 90) {
        writeLog("WARNING: Disk space is running low!");
        return false;
    }
    
    return true;
}

// ตรวจสอบพื้นที่ดิสก์ก่อนทำ backup
checkDiskSpace();
?>
