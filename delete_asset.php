<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// ตรวจสอบการล็อกอิน
requireLogin();

// ตรวจสอบ ID
$id = $_GET['id'] ?? 0;
if (!$id) {
    header('Location: index.php');
    exit;
}

// ดึงข้อมูล asset
try {
    $stmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
    $stmt->execute([$id]);
    $asset = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$asset) {
        header('Location: index.php');
        exit;
    }
} catch (Exception $e) {
    header('Location: index.php');
    exit;
}

$message = '';
$messageType = '';

// ประมวลผลการลบ
if ($_POST && isset($_POST['confirm_delete'])) {
    $deletedBy = $_POST['deleted_by'] ?? getCurrentUserFullName();

    try {
        // เริ่ม transaction
        $pdo->beginTransaction();

        // บันทึก log ก่อนลบ
        $logStmt = $pdo->prepare("INSERT INTO asset_logs (asset_id, action_type, changed_by, changed_date, description) VALUES (?, 'DELETE', ?, NOW(), ?)");
        $description = "ลบ Asset ID: " . $asset['asset_id'] . " (" . $asset['type'] . " - " . $asset['brand'] . " " . $asset['model'] . ")";
        $logStmt->execute([$id, $deletedBy, $description]);

        // ลบ asset
        $deleteStmt = $pdo->prepare("DELETE FROM assets WHERE id = ?");
        $result = $deleteStmt->execute([$id]);

        if ($result) {
            // Trigger auto backup
            triggerAssetDeleteBackup($id, $asset);

            $pdo->commit();
            // Redirect with success message
            header('Location: index.php?message=ลบ Asset สำเร็จ&type=success');
            exit;
        } else {
            $pdo->rollback();
            $message = 'เกิดข้อผิดพลาดในการลบ Asset';
            $messageType = 'danger';
        }
    } catch (Exception $e) {
        $pdo->rollback();
        $message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
        $messageType = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ลบ Asset - Asset Management System</title>

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ระบบจัดการ Asset</h1>
            <p class="subtitle">Asset Management System - จัดการทรัพย์สินองค์กรอย่างมีประสิทธิภาพ</p>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="index.php" class="active"><i class="fas fa-list"></i> รายการ Assets</a></li>
                <?php if (isAdmin()): ?>
                    <li><a href="users.php"><i class="fas fa-users"></i> จัดการผู้ใช้</a></li>
                <?php endif; ?>
                <li><a href="#" onclick="openToolsModal()"><i class="fas fa-tools"></i> เครื่องมือ</a></li>
                <li><a href="#" onclick="openProfileModal()"><i class="fas fa-user"></i> โปรไฟล์</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> ออกจากระบบ</a></li>
            </ul>
        </nav>

        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                <h2>ยืนยันการลบ Asset</h2>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <strong>คำเตือน!</strong> คุณกำลังจะลบ Asset นี้ การดำเนินการนี้ไม่สามารถยกเลิกได้
                </div>

                <h3>ข้อมูล Asset ที่จะลบ:</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>Asset ID:</label>
                        <p><strong><?= htmlspecialchars($asset['asset_id']) ?></strong></p>
                    </div>
                    <div class="form-group">
                        <label>Type:</label>
                        <p><?= htmlspecialchars($asset['type']) ?></p>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Brand:</label>
                        <p><?= htmlspecialchars($asset['brand']) ?: '-' ?></p>
                    </div>
                    <div class="form-group">
                        <label>Model:</label>
                        <p><?= htmlspecialchars($asset['model']) ?: '-' ?></p>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Department:</label>
                        <p><?= htmlspecialchars($asset['department']) ?: '-' ?></p>
                    </div>
                    <div class="form-group">
                        <label>Status:</label>
                        <p><?= getStatusBadge($asset['status']) ?></p>
                    </div>
                </div>

                <div class="form-group">
                    <label>Description:</label>
                    <p><?= htmlspecialchars($asset['description']) ?: '-' ?></p>
                </div>

                <hr>

                <form method="POST" action="">
                    <div class="form-group">
                        <label for="deleted_by">ผู้ลบ:</label>
                        <input type="text" id="deleted_by" name="deleted_by" class="form-control" required readonly
                               value="<?= htmlspecialchars(getCurrentUserFullName()) ?>" style="background-color: #f8f9fa;">
                    </div>

                    <div class="form-group">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                            <label>
                                <input type="checkbox" required> 
                                ฉันยืนยันที่จะลบ Asset นี้และเข้าใจว่าการดำเนินการนี้ไม่สามารถยกเลิกได้
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" name="confirm_delete" class="btn btn-danger">
                            ยืนยันการลบ Asset
                        </button>
                        <a href="view_asset.php?id=<?= $id ?>" class="btn btn-secondary">ยกเลิก</a>
                        <a href="index.php" class="btn btn-primary">กลับหน้าหลัก</a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // เพิ่มการยืนยันอีกครั้งก่อนส่งฟอร์ม
        document.querySelector('form').addEventListener('submit', function(e) {
            if (!confirm('คุณแน่ใจหรือไม่ที่จะลบ Asset นี้? การดำเนินการนี้ไม่สามารถยกเลิกได้!')) {
                e.preventDefault();
            }
        });
    </script>
</body>
</html>
