<?php
/**
 * Fix Session Issues - แก้ไขปัญหา Session
 * Asset Management System
 */

echo "<h1>🔧 แก้ไขปัญหา Session</h1>";

// ตรวจสอบและแก้ไข session
if (session_status() === PHP_SESSION_NONE) {
    // ตั้งค่า session ก่อนเริ่ม
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', 0); // เปลี่ยนเป็น 1 ถ้าใช้ HTTPS
    
    if (session_start()) {
        echo "<p style='color: green;'>✅ Session เริ่มต้นสำเร็จ</p>";
    } else {
        echo "<p style='color: red;'>❌ ไม่สามารถเริ่ม Session ได้</p>";
    }
} else {
    echo "<p style='color: blue;'>ℹ️ Session ทำงานอยู่แล้ว</p>";
}

// แสดงข้อมูล session
echo "<h2>📋 ข้อมูล Session</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Key</th><th>Value</th></tr>";

if (!empty($_SESSION)) {
    foreach ($_SESSION as $key => $value) {
        $displayValue = is_array($value) ? json_encode($value) : htmlspecialchars($value);
        echo "<tr><td>$key</td><td>$displayValue</td></tr>";
    }
} else {
    echo "<tr><td colspan='2'>ไม่มีข้อมูลใน Session</td></tr>";
}
echo "</table>";

// ตรวจสอบการตั้งค่า session
echo "<h2>⚙️ การตั้งค่า Session</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>Session ID</td><td>" . session_id() . "</td></tr>";
echo "<tr><td>Session Name</td><td>" . session_name() . "</td></tr>";
echo "<tr><td>Session Status</td><td>" . session_status() . "</td></tr>";
echo "<tr><td>Session Save Path</td><td>" . session_save_path() . "</td></tr>";
echo "<tr><td>Session Cookie Lifetime</td><td>" . ini_get('session.cookie_lifetime') . "</td></tr>";
echo "<tr><td>Session GC Maxlifetime</td><td>" . ini_get('session.gc_maxlifetime') . "</td></tr>";
echo "<tr><td>Session Use Cookies</td><td>" . ini_get('session.use_cookies') . "</td></tr>";
echo "<tr><td>Session Cookie HTTPOnly</td><td>" . ini_get('session.cookie_httponly') . "</td></tr>";
echo "</table>";

// ฟังก์ชันทดสอบ session
if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'test_session':
            echo "<h2>🧪 ทดสอบ Session</h2>";
            
            // ทดสอบการเขียน session
            $_SESSION['test_key'] = 'test_value_' . time();
            $_SESSION['test_array'] = ['item1', 'item2', 'item3'];
            $_SESSION['test_number'] = 12345;
            
            echo "<p style='color: green;'>✅ เขียนข้อมูลทดสอบลง Session แล้ว</p>";
            echo "<p><a href='?action=read_session'>อ่านข้อมูลทดสอบ</a></p>";
            break;
            
        case 'read_session':
            echo "<h2>📖 อ่านข้อมูลทดสอบ</h2>";
            
            if (isset($_SESSION['test_key'])) {
                echo "<p style='color: green;'>✅ test_key: " . $_SESSION['test_key'] . "</p>";
            } else {
                echo "<p style='color: red;'>❌ ไม่พบ test_key</p>";
            }
            
            if (isset($_SESSION['test_array'])) {
                echo "<p style='color: green;'>✅ test_array: " . json_encode($_SESSION['test_array']) . "</p>";
            } else {
                echo "<p style='color: red;'>❌ ไม่พบ test_array</p>";
            }
            
            if (isset($_SESSION['test_number'])) {
                echo "<p style='color: green;'>✅ test_number: " . $_SESSION['test_number'] . "</p>";
            } else {
                echo "<p style='color: red;'>❌ ไม่พบ test_number</p>";
            }
            
            echo "<p><a href='?action=clear_test'>ลบข้อมูลทดสอบ</a></p>";
            break;
            
        case 'clear_test':
            echo "<h2>🗑️ ลบข้อมูลทดสอบ</h2>";
            
            unset($_SESSION['test_key']);
            unset($_SESSION['test_array']);
            unset($_SESSION['test_number']);
            
            echo "<p style='color: green;'>✅ ลบข้อมูลทดสอบแล้ว</p>";
            break;
            
        case 'create_admin_session':
            echo "<h2>👤 สร้าง Session Admin</h2>";
            
            $_SESSION['user_id'] = 1;
            $_SESSION['username'] = 'admin';
            $_SESSION['full_name'] = 'Administrator';
            $_SESSION['role'] = 'Admin';
            $_SESSION['email'] = '<EMAIL>';
            
            echo "<p style='color: green;'>✅ สร้าง Session Admin แล้ว</p>";
            echo "<p><a href='index.php'>ไปยังหน้าหลัก</a></p>";
            break;
            
        case 'destroy_session':
            echo "<h2>💥 ทำลาย Session</h2>";
            
            session_destroy();
            echo "<p style='color: green;'>✅ ทำลาย Session แล้ว</p>";
            echo "<p><a href='fix_session.php'>รีเฟรชหน้า</a></p>";
            break;
            
        case 'regenerate_session':
            echo "<h2>🔄 สร้าง Session ID ใหม่</h2>";
            
            $oldId = session_id();
            session_regenerate_id(true);
            $newId = session_id();
            
            echo "<p>Session ID เก่า: $oldId</p>";
            echo "<p>Session ID ใหม่: $newId</p>";
            echo "<p style='color: green;'>✅ สร้าง Session ID ใหม่แล้ว</p>";
            break;
    }
} else {
    // แสดงเมนูการทดสอบ
    echo "<h2>🧪 เครื่องมือทดสอบ Session</h2>";
    echo "<p><a href='?action=test_session' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>ทดสอบการเขียน Session</a></p>";
    echo "<p><a href='?action=read_session' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>อ่านข้อมูลทดสอบ</a></p>";
    echo "<p><a href='?action=create_admin_session' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>สร้าง Session Admin</a></p>";
    echo "<p><a href='?action=regenerate_session' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>สร้าง Session ID ใหม่</a></p>";
    echo "<p><a href='?action=destroy_session' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>ทำลาย Session</a></p>";
}

// ตรวจสอบปัญหา session ที่พบบ่อย
echo "<h2>🔍 ตรวจสอบปัญหา Session</h2>";

$issues = [];

// ตรวจสอบ session save path
$savePath = session_save_path();
if (empty($savePath)) {
    $issues[] = "Session save path ไม่ได้ตั้งค่า";
} elseif (!is_dir($savePath)) {
    $issues[] = "Session save path ไม่มีอยู่: $savePath";
} elseif (!is_writable($savePath)) {
    $issues[] = "Session save path ไม่สามารถเขียนได้: $savePath";
} else {
    echo "<p style='color: green;'>✅ Session save path ใช้งานได้: $savePath</p>";
}

// ตรวจสอบ session files
if (!empty($savePath) && is_dir($savePath)) {
    $sessionFiles = glob($savePath . '/sess_*');
    echo "<p>จำนวนไฟล์ session: " . count($sessionFiles) . "</p>";
}

// ตรวจสอบ cookies
if (isset($_COOKIE[session_name()])) {
    echo "<p style='color: green;'>✅ Session cookie มีอยู่</p>";
} else {
    $issues[] = "Session cookie ไม่มีอยู่";
}

// ตรวจสอบ headers
if (headers_sent($file, $line)) {
    $issues[] = "Headers ถูกส่งไปแล้วที่ $file:$line (อาจทำให้ session ไม่ทำงาน)";
} else {
    echo "<p style='color: green;'>✅ Headers ยังไม่ถูกส่ง</p>";
}

// แสดงปัญหาที่พบ
if (!empty($issues)) {
    echo "<h3 style='color: red;'>ปัญหาที่พบ:</h3>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li style='color: red;'>❌ $issue</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: green; font-weight: bold;'>✅ ไม่พบปัญหา Session</p>";
}

// คำแนะนำการแก้ไข
echo "<h2>💡 คำแนะนำการแก้ไข</h2>";
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 8px;'>";
echo "<h3>หากมีปัญหา Session:</h3>";
echo "<ol>";
echo "<li>ตรวจสอบว่า session_start() ถูกเรียกก่อน output ใดๆ</li>";
echo "<li>ตรวจสอบสิทธิ์การเขียนของ session save path</li>";
echo "<li>ตรวจสอบการตั้งค่า php.ini สำหรับ session</li>";
echo "<li>ลองใช้ session_regenerate_id() เพื่อสร้าง ID ใหม่</li>";
echo "<li>ตรวจสอบว่าไม่มี output ก่อน session_start()</li>";
echo "<li>ตรวจสอบ cookie settings ในเบราว์เซอร์</li>";
echo "</ol>";

echo "<h3>การตั้งค่า php.ini ที่แนะนำ:</h3>";
echo "<pre>";
echo "session.save_handler = files\n";
echo "session.save_path = /tmp\n";
echo "session.use_cookies = 1\n";
echo "session.use_only_cookies = 1\n";
echo "session.cookie_httponly = 1\n";
echo "session.cookie_lifetime = 0\n";
echo "session.gc_maxlifetime = 1440\n";
echo "</pre>";
echo "</div>";

echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ทดสอบระบบ</a></p>";
echo "<p><a href='fix_errors.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ตรวจสอบข้อผิดพลาดอื่น</a></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

table {
    background: white;
    margin: 10px 0;
    width: 100%;
}

th, td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background: #f8f9fa;
}

a {
    display: inline-block;
    margin: 5px 0;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
}
</style>
