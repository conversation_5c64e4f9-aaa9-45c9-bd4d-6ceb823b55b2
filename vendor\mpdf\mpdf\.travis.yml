language: php

dist: xenial

group: edge

php:
  - 5.6
  - 7.0
  - 7.1
  - 7.2
  - 7.3
  - nightly

matrix:

  include:

    - env: LINT=1
      php: 7.1
    #- env: COVERAGE=1
    #  php: 7.1

  allow_failures:
    - php: nightly

install:
  - composer self-update
  - echo "${TRAVIS_PHP_VERSION:0:3}"
  - |
    if [[ "${TRAVIS_PHP_VERSION:0:7}" == "nightly" ]]; then
      composer install --ignore-platform-reqs
    else
      composer install
    fi

script:
  - |
    if [[ "$LINT" == "1" ]]; then
      ./vendor/bin/phpcs -v --report-width=160 --standard=ruleset.xml --severity=1 --warning-severity=0 --extensions=php src utils tests
    elif [[ "$COVERAGE" == "1" ]]; then
      vendor/bin/phpunit --coverage-text
    else
      vendor/bin/phpunit
    fi

notifications:
  email:
    on_success: change
    on_failure: always
