<?php
// เพิ่ม Asset - สร้างใหม่ทั้งหมด
error_reporting(E_ALL);
ini_set('display_errors', 1);

// ตรวจสอบว่าเป็น AJAX request หรือไม่
$isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
          strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

// ถ้าเป็น AJAX ให้ส่ง JSON header
if ($isAjax) {
    header('Content-Type: application/json');
}

// Include required files
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// ตั้งค่า session ชั่วคราวถ้าไม่มี (สำหรับการทดสอบ)
if (!isset($_SESSION['username'])) {
    $_SESSION['username'] = 'admin';
    $_SESSION['full_name'] = 'ผู้ดูแลระบบ';
    $_SESSION['role'] = 'Admin';
    $_SESSION['user_id'] = 1;
    $_SESSION['logged_in'] = true;
}

// ตรวจสอบสิทธิ์ Admin
if (!isAdmin()) {
    if ($isAjax) {
        echo json_encode(['success' => false, 'message' => 'ไม่มีสิทธิ์ในการเพิ่ม Asset']);
        exit;
    } else {
        header('Location: index.php?error=access_denied');
        exit;
    }
}

$message = '';
$messageType = '';

// ประมวลผลการเพิ่ม Asset
if ($_POST && !empty($_POST['type'])) {
    try {
        $assetManager = new AssetManager($pdo);

        // รับข้อมูลจากฟอร์ม
        $data = [
            'type' => trim($_POST['type'] ?? ''),
            'brand' => trim($_POST['brand'] ?? ''),
            'model' => trim($_POST['model'] ?? ''),
            'tag' => trim($_POST['tag'] ?? ''),
            'department' => trim($_POST['department'] ?? ''),
            'status' => trim($_POST['status'] ?? 'ใช้งาน'),
            'hostname' => trim($_POST['hostname'] ?? ''),
            'operating_system' => trim($_POST['operating_system'] ?? ''),
            'serial_number' => trim($_POST['serial_number'] ?? ''),
            'asset_id' => trim($_POST['asset_id'] ?? ''),
            'warranty_expire' => !empty($_POST['warranty_expire']) ? $_POST['warranty_expire'] : null,
            'description' => trim($_POST['description'] ?? ''),
            'asset_set' => trim($_POST['set_name'] ?? ''),
            'created_by' => getCurrentUserFullName(),
            'updated_by' => getCurrentUserFullName()
        ];

        // ตรวจสอบข้อมูลจำเป็น
        if (empty($data['type'])) {
            throw new Exception('กรุณาระบุ Type');
        }

        // เพิ่ม Asset ผ่าน AssetManager
        $result = $assetManager->createAsset($data);

        if ($result) {
            $newId = $result;

            // บันทึก log การสร้าง Asset ใหม่
            logAssetCreation($pdo, $newId, $_SESSION['username']);

            // Trigger auto backup
            triggerAssetAddBackup($newId, $data);

            $message = "เพิ่ม Asset สำเร็จ (ID: $newId)";
            $messageType = 'success';

            // ถ้าเป็น AJAX ให้ส่ง JSON response
            if ($isAjax) {
                echo json_encode([
                    'success' => true,
                    'message' => 'เพิ่ม Asset สำเร็จ',
                    'asset_id' => $newId
                ]);
                exit;
            }

            // ล้างข้อมูลฟอร์ม
            $_POST = [];
        } else {
            throw new Exception('ไม่สามารถเพิ่ม Asset ได้');
        }

    } catch (Exception $e) {
        $message = "ข้อผิดพลาด: " . $e->getMessage();
        $messageType = 'error';

        // ถ้าเป็น AJAX ให้ส่ง JSON response
        if ($isAjax) {
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
}

// Function สำหรับบันทึก log การสร้าง Asset ใหม่
function logAssetCreation($pdo, $assetId, $username) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO asset_logs (
                asset_id,
                action_type,
                field_name,
                old_value,
                new_value,
                changed_by,
                changed_date,
                description
            ) VALUES (?, 'CREATE', NULL, NULL, NULL, ?, NOW(), ?)
        ");

        $description = "สร้าง Asset ใหม่ในระบบ";

        $stmt->execute([
            $assetId,
            $username,
            $description
        ]);

    } catch (PDOException $e) {
        // Log error แต่ไม่หยุดการทำงาน
        error_log("Error logging asset creation: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เพิ่ม Asset - Asset Management System</title>
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Sarabun', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1rem;
        }

        .required {
            color: #e74c3c;
        }

        input, select, textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 1rem;
            font-family: 'Sarabun', sans-serif;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        .btn-container {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 40px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-family: 'Sarabun', sans-serif;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .content {
                padding: 20px;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .btn-container {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-plus-circle"></i> เพิ่ม Asset</h1>
            <p>เพิ่มทรัพย์สินใหม่เข้าสู่ระบบ</p>
        </div>

        <div class="content">
            <?php if ($message): ?>
                <div class="alert <?= $messageType ?>">
                    <i class="fas <?= $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle' ?>"></i>
                    <?= htmlspecialchars($message) ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="type">Type <span class="required">*</span></label>
                        <select id="type" name="type" required>
                            <option value="">เลือก Type</option>
                            <option value="Desktop" <?= ($_POST['type'] ?? '') === 'Desktop' ? 'selected' : '' ?>>Desktop</option>
                            <option value="Laptop" <?= ($_POST['type'] ?? '') === 'Laptop' ? 'selected' : '' ?>>Laptop</option>
                            <option value="Monitor" <?= ($_POST['type'] ?? '') === 'Monitor' ? 'selected' : '' ?>>Monitor</option>
                            <option value="All-in-one" <?= ($_POST['type'] ?? '') === 'All-in-one' ? 'selected' : '' ?>>All-in-one</option>
                            <option value="Multifunction Laser Printer" <?= ($_POST['type'] ?? '') === 'Multifunction Laser Printer' ? 'selected' : '' ?>>Multifunction Laser Printer</option>
                            <option value="Barcode Printer" <?= ($_POST['type'] ?? '') === 'Barcode Printer' ? 'selected' : '' ?>>Barcode Printer</option>
                            <option value="Barcode Scanner" <?= ($_POST['type'] ?? '') === 'Barcode Scanner' ? 'selected' : '' ?>>Barcode Scanner</option>
                            <option value="Tablet" <?= ($_POST['type'] ?? '') === 'Tablet' ? 'selected' : '' ?>>Tablet</option>
                            <option value="UPS" <?= ($_POST['type'] ?? '') === 'UPS' ? 'selected' : '' ?>>UPS</option>
                            <option value="Queue" <?= ($_POST['type'] ?? '') === 'Queue' ? 'selected' : '' ?>>Queue</option>
                            <option value="IP Phone" <?= ($_POST['type'] ?? '') === 'IP Phone' ? 'selected' : '' ?>>IP Phone</option>
                            <option value="Teleconference" <?= ($_POST['type'] ?? '') === 'Teleconference' ? 'selected' : '' ?>>Teleconference</option>
                            <option value="Switch" <?= ($_POST['type'] ?? '') === 'Switch' ? 'selected' : '' ?>>Switch</option>
                            <option value="Access Point" <?= ($_POST['type'] ?? '') === 'Access Point' ? 'selected' : '' ?>>Access Point</option>
                            <option value="Peripheral" <?= ($_POST['type'] ?? '') === 'Peripheral' ? 'selected' : '' ?>>Peripheral</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="brand">Brand</label>
                        <select id="brand" name="brand">
                            <option value="">เลือก Brand</option>
                            <option value="-" <?= ($_POST['brand'] ?? '') === '-' ? 'selected' : '' ?>>-</option>
                            <option value="Dell" <?= ($_POST['brand'] ?? '') === 'Dell' ? 'selected' : '' ?>>Dell</option>
                            <option value="Lenovo" <?= ($_POST['brand'] ?? '') === 'Lenovo' ? 'selected' : '' ?>>Lenovo</option>
                            <option value="Microsoft" <?= ($_POST['brand'] ?? '') === 'Microsoft' ? 'selected' : '' ?>>Microsoft</option>
                            <option value="Apple" <?= ($_POST['brand'] ?? '') === 'Apple' ? 'selected' : '' ?>>Apple</option>
                            <option value="Zebra" <?= ($_POST['brand'] ?? '') === 'Zebra' ? 'selected' : '' ?>>Zebra</option>
                            <option value="HP" <?= ($_POST['brand'] ?? '') === 'HP' ? 'selected' : '' ?>>HP</option>
                            <option value="Philips" <?= ($_POST['brand'] ?? '') === 'Philips' ? 'selected' : '' ?>>Philips</option>
                            <option value="Acer" <?= ($_POST['brand'] ?? '') === 'Acer' ? 'selected' : '' ?>>Acer</option>
                            <option value="LG" <?= ($_POST['brand'] ?? '') === 'LG' ? 'selected' : '' ?>>LG</option>
                            <option value="Cisco" <?= ($_POST['brand'] ?? '') === 'Cisco' ? 'selected' : '' ?>>Cisco</option>
                        </select>
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="model">Model</label>
                        <input type="text" id="model" name="model" placeholder="ระบุรุ่น" value="<?= htmlspecialchars($_POST['model'] ?? '') ?>">
                    </div>

                    <div class="form-group">
                        <label for="tag">Tag</label>
                        <input type="text" id="tag" name="tag" placeholder="ระบุ Tag" value="<?= htmlspecialchars($_POST['tag'] ?? '') ?>">
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="department">Department</label>
                        <input type="text" id="department" name="department" placeholder="ระบุแผนก" value="<?= htmlspecialchars($_POST['department'] ?? '') ?>">
                    </div>

                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="status" name="status">
                            <option value="ใช้งาน" <?= ($_POST['status'] ?? 'ใช้งาน') === 'ใช้งาน' ? 'selected' : '' ?>>ใช้งาน</option>
                            <option value="ชำรุด" <?= ($_POST['status'] ?? '') === 'ชำรุด' ? 'selected' : '' ?>>ชำรุด</option>
                            <option value="สำรอง" <?= ($_POST['status'] ?? '') === 'สำรอง' ? 'selected' : '' ?>>สำรอง</option>
                        </select>
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="hostname">Hostname</label>
                        <input type="text" id="hostname" name="hostname" placeholder="ระบุ Hostname" value="<?= htmlspecialchars($_POST['hostname'] ?? '') ?>">
                    </div>

                    <div class="form-group">
                        <label for="operating_system">Operating System</label>
                        <select id="operating_system" name="operating_system">
                            <option value="">เลือก Operating System</option>
                            <option value="-" <?= ($_POST['operating_system'] ?? '') === '-' ? 'selected' : '' ?>>-</option>
                            <option value="Windows 7" <?= ($_POST['operating_system'] ?? '') === 'Windows 7' ? 'selected' : '' ?>>Windows 7</option>
                            <option value="Windows 10" <?= ($_POST['operating_system'] ?? '') === 'Windows 10' ? 'selected' : '' ?>>Windows 10</option>
                            <option value="Windows 11" <?= ($_POST['operating_system'] ?? '') === 'Windows 11' ? 'selected' : '' ?>>Windows 11</option>
                            <option value="MacOS" <?= ($_POST['operating_system'] ?? '') === 'MacOS' ? 'selected' : '' ?>>MacOS</option>
                        </select>
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="serial_number">Serial Number</label>
                        <input type="text" id="serial_number" name="serial_number" placeholder="ระบุ Serial Number" value="<?= htmlspecialchars($_POST['serial_number'] ?? '') ?>">
                    </div>

                    <div class="form-group">
                        <label for="asset_id">Asset ID</label>
                        <input type="text" id="asset_id" name="asset_id" placeholder="ระบุ Asset ID" value="<?= htmlspecialchars($_POST['asset_id'] ?? '') ?>">
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="warranty_expire">วันหมดประกัน</label>
                        <input type="date" id="warranty_expire" name="warranty_expire" value="<?= htmlspecialchars($_POST['warranty_expire'] ?? '') ?>">
                    </div>

                    <div class="form-group">
                        <label for="set_name">Set</label>
                        <input type="text" id="set_name" name="set_name" placeholder="ระบุ Set" value="<?= htmlspecialchars($_POST['set_name'] ?? '') ?>">
                    </div>
                </div>

                <div class="form-group full-width">
                    <label for="description">รายละเอียด</label>
                    <textarea id="description" name="description" placeholder="ระบุรายละเอียดเพิ่มเติม"><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                </div>

                <div class="form-group full-width">
                    <label for="created_by">คนเพิ่ม</label>
                    <input type="text" id="created_by" name="created_by" readonly value="<?= htmlspecialchars(getCurrentUserFullName()) ?>" style="background-color: #f8f9fa;">
                </div>

                <div class="btn-container">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> เพิ่ม Asset
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> กลับหน้าหลัก
                    </a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
