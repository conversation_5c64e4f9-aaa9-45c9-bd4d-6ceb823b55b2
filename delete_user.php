<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// ตรวจสอบการล็อกอิน
requireLogin();

// ตรวจสอบสิทธิ์ Admin
if (!isAdmin()) {
    header('Location: users.php');
    exit;
}

// ตรวจสอบ ID
$id = $_GET['id'] ?? 0;
if (!$id) {
    header('Location: users.php');
    exit;
}

// ป้องกันการลบตัวเอง
if ($id == $_SESSION['user_id']) {
    header('Location: users.php?message=' . urlencode('ไม่สามารถลบผู้ใช้ตัวเองได้') . '&type=error');
    exit;
}

// ดึงข้อมูล user
try {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        header('Location: users.php?message=' . urlencode('ไม่พบผู้ใช้ที่ต้องการลบ') . '&type=error');
        exit;
    }
} catch (Exception $e) {
    header('Location: users.php?message=' . urlencode('เกิดข้อผิดพลาดในการดึงข้อมูล') . '&type=error');
    exit;
}

$message = '';
$messageType = '';

// ประมวลผลการลบ
if ($_POST && isset($_POST['confirm_delete'])) {
    try {
        // เริ่ม transaction
        $pdo->beginTransaction();

        // ลบ user
        $deleteStmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
        $result = $deleteStmt->execute([$id]);

        if ($result) {
            // Trigger auto backup
            triggerUserDeleteBackup($id, $user);
            
            $pdo->commit();
            // Redirect with success message
            header('Location: users.php?message=' . urlencode('ลบผู้ใช้สำเร็จ') . '&type=success');
            exit;
        } else {
            $pdo->rollback();
            $message = 'เกิดข้อผิดพลาดในการลบผู้ใช้';
            $messageType = 'danger';
        }
    } catch (Exception $e) {
        $pdo->rollback();
        $message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
        $messageType = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ลบผู้ใช้ - Asset Management System</title>
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <link rel="stylesheet" href="assets/style.css">
    
    <style>
        .delete-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .delete-card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
        }
        
        .delete-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .delete-icon {
            font-size: 4em;
            color: #e53e3e;
            margin-bottom: 15px;
        }
        
        .delete-title {
            color: #e53e3e;
            margin-bottom: 10px;
        }
        
        .user-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #e53e3e;
        }
        
        .user-info h3 {
            color: #2d3748;
            margin-bottom: 15px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #4a5568;
        }
        
        .info-value {
            color: #2d3748;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .warning-box h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .warning-box ul {
            color: #856404;
            margin: 0;
            padding-left: 20px;
        }
        
        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .btn-danger {
            background: #e53e3e;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c53030;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-primary {
            background: #4299e1;
            color: white;
        }
        
        .btn-primary:hover {
            background: #3182ce;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .alert.alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ระบบจัดการ Asset</h1>
            <p class="subtitle">Asset Management System - จัดการทรัพย์สินองค์กรอย่างมีประสิทธิภาพ</p>
        </div>
    </div>

    <div class="delete-container">
        <div class="delete-card">
            <?php if (!empty($message)): ?>
            <div class="alert alert-<?= $messageType ?>">
                <i class="fas fa-exclamation-triangle"></i>
                <?= htmlspecialchars($message) ?>
            </div>
            <?php endif; ?>

            <div class="delete-header">
                <div class="delete-icon">
                    <i class="fas fa-user-times"></i>
                </div>
                <h2 class="delete-title">ยืนยันการลบผู้ใช้</h2>
                <p>คุณกำลังจะลบผู้ใช้ออกจากระบบ</p>
            </div>

            <div class="user-info">
                <h3><i class="fas fa-user"></i> ข้อมูลผู้ใช้ที่จะลบ</h3>
                
                <div class="info-row">
                    <span class="info-label">ID:</span>
                    <span class="info-value"><?= htmlspecialchars($user['id']) ?></span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Username:</span>
                    <span class="info-value"><?= htmlspecialchars($user['username']) ?></span>
                </div>
                
                <?php if (isset($user['full_name'])): ?>
                <div class="info-row">
                    <span class="info-label">ชื่อ-นามสกุล:</span>
                    <span class="info-value"><?= htmlspecialchars($user['full_name']) ?></span>
                </div>
                <?php endif; ?>
                
                <?php if (isset($user['email'])): ?>
                <div class="info-row">
                    <span class="info-label">Email:</span>
                    <span class="info-value"><?= htmlspecialchars($user['email']) ?></span>
                </div>
                <?php endif; ?>
                
                <?php if (isset($user['role'])): ?>
                <div class="info-row">
                    <span class="info-label">บทบาท:</span>
                    <span class="info-value"><?= htmlspecialchars($user['role']) ?></span>
                </div>
                <?php endif; ?>
                
                <?php if (isset($user['created_date'])): ?>
                <div class="info-row">
                    <span class="info-label">วันที่สร้าง:</span>
                    <span class="info-value"><?= formatDateTime($user['created_date']) ?></span>
                </div>
                <?php endif; ?>
            </div>

            <div class="warning-box">
                <h4><i class="fas fa-exclamation-triangle"></i> คำเตือน</h4>
                <ul>
                    <li>การลบผู้ใช้นี้ไม่สามารถยกเลิกได้</li>
                    <li>ข้อมูลทั้งหมดของผู้ใช้จะถูกลบออกจากระบบ</li>
                    <li>ระบบจะสร้าง backup อัตโนมัติก่อนการลบ</li>
                    <li>กรุณาตรวจสอบข้อมูลให้แน่ใจก่อนดำเนินการ</li>
                </ul>
            </div>

            <form method="POST">
                <div class="form-actions">
                    <button type="submit" name="confirm_delete" class="btn btn-danger">
                        <i class="fas fa-trash"></i>
                        ยืนยันการลบผู้ใช้
                    </button>
                    <a href="users.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        ยกเลิก
                    </a>
                </div>
            </form>
        </div>
    </div>

    <script>
        // เพิ่มการยืนยันอีกครั้งก่อนส่งฟอร์ม
        document.querySelector('form').addEventListener('submit', function(e) {
            if (!confirm('คุณแน่ใจหรือไม่ที่จะลบผู้ใช้ "<?= htmlspecialchars($user['username']) ?>"?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้!')) {
                e.preventDefault();
            }
        });
    </script>
</body>
</html>
