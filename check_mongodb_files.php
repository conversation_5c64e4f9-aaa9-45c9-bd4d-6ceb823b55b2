<?php
/**
 * Check MongoDB Files
 * Asset Management System - File Checker for Downloaded MongoDB Extension
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='th'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>ตรวจสอบไฟล์ MongoDB Extension</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; background: #f5f5f5; line-height: 1.6; }
        .header { background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%); color: white; padding: 30px; border-radius: 15px; text-align: center; margin-bottom: 30px; }
        .check-section { background: white; border-radius: 10px; padding: 25px; margin: 20px 0; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .status { padding: 12px 20px; border-radius: 8px; margin: 15px 0; font-weight: bold; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .file-info { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #6f42c1; }
        .file-path { background: #e3f2fd; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; border: 1px solid #bbdefb; word-break: break-all; }
        .btn { display: inline-block; padding: 12px 24px; background: #6f42c1; color: white; text-decoration: none; border-radius: 8px; margin: 5px; transition: all 0.3s; }
        .btn:hover { background: #5a32a3; transform: translateY(-2px); }
        .btn.success { background: #28a745; }
        .btn.danger { background: #dc3545; }
        .file-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0; }
        .file-card { background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 10px; padding: 20px; transition: all 0.3s; }
        .file-card.found { border-color: #28a745; background: #d4edda; }
        .file-card.not-found { border-color: #dc3545; background: #f8d7da; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; margin: 10px 0; border-left: 4px solid #6f42c1; }
    </style>
</head>
<body>";

echo "<div class='header'>
    <h1>🔍 ตรวจสอบไฟล์ MongoDB Extension</h1>
    <p>ตรวจสอบไฟล์ที่ดาวน์โหลดและความพร้อมในการติดตั้ง</p>
</div>";

// System information
$phpVersion = phpversion();
$phpMajor = substr($phpVersion, 0, 3); // เช่น 8.2
$phpArch = (PHP_INT_SIZE === 8) ? 'x64' : 'x86';
$isThreadSafe = (defined('ZEND_THREAD_SAFE') && ZEND_THREAD_SAFE) ? 'TS' : 'NTS';
$extensionDir = ini_get('extension_dir');
$phpIniFile = php_ini_loaded_file();

echo "<div class='check-section'>
<h2>📊 ข้อมูลระบบ</h2>
<div class='status info'>
<p><strong>PHP Version:</strong> $phpVersion</p>
<p><strong>PHP Major:</strong> $phpMajor</p>
<p><strong>Architecture:</strong> $phpArch</p>
<p><strong>Thread Safety:</strong> $isThreadSafe</p>
<p><strong>Extension Directory:</strong> $extensionDir</p>
<p><strong>PHP.ini File:</strong> $phpIniFile</p>
</div>
</div>";

// Check download path
$downloadPath = 'C:\\Users\\<USER>\\Downloads\\mongodb-2.1.0';

echo "<div class='check-section'>
<h2>📁 ตรวจสอบไฟล์ที่ดาวน์โหลด</h2>
<div class='file-path'>
<strong>Path:</strong> $downloadPath
</div>";

// Check if download path exists
if (is_dir($downloadPath)) {
    echo "<div class='status success'>✅ พบโฟลเดอร์ที่ดาวน์โหลด</div>";
    
    // List files in download directory
    $files = scandir($downloadPath);
    $dllFiles = [];
    $otherFiles = [];
    
    foreach ($files as $file) {
        if ($file === '.' || $file === '..') continue;
        
        $fullPath = $downloadPath . '\\' . $file;
        
        if (is_file($fullPath)) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'dll') {
                $dllFiles[] = $file;
            } else {
                $otherFiles[] = $file;
            }
        } elseif (is_dir($fullPath)) {
            // Check subdirectories for DLL files
            $subFiles = scandir($fullPath);
            foreach ($subFiles as $subFile) {
                if (pathinfo($subFile, PATHINFO_EXTENSION) === 'dll') {
                    $dllFiles[] = $file . '\\' . $subFile;
                }
            }
        }
    }
    
    echo "<div class='file-info'>
    <h4>📋 ไฟล์ที่พบ:</h4>
    <p><strong>DLL Files:</strong> " . count($dllFiles) . " ไฟล์</p>
    <p><strong>Other Files:</strong> " . count($otherFiles) . " ไฟล์</p>
    </div>";
    
    // Show DLL files
    if (!empty($dllFiles)) {
        echo "<h3>🔧 ไฟล์ DLL ที่พบ:</h3>
        <div class='file-grid'>";
        
        foreach ($dllFiles as $dll) {
            $isCorrect = false;
            $reasons = [];
            
            // Check if filename contains correct version info
            if (strpos($dll, 'mongodb') !== false) {
                $reasons[] = '✅ ชื่อไฟล์ถูกต้อง (mongodb)';
                
                // Check PHP version
                if (strpos($dll, $phpMajor) !== false || strpos($dll, str_replace('.', '', $phpMajor)) !== false) {
                    $reasons[] = '✅ เวอร์ชัน PHP ตรงกัน (' . $phpMajor . ')';
                } else {
                    $reasons[] = '❌ เวอร์ชัน PHP ไม่ตรงกัน (ต้องการ ' . $phpMajor . ')';
                }
                
                // Check architecture
                if (strpos($dll, $phpArch) !== false) {
                    $reasons[] = '✅ Architecture ตรงกัน (' . $phpArch . ')';
                } else {
                    $reasons[] = '❌ Architecture ไม่ตรงกัน (ต้องการ ' . $phpArch . ')';
                }
                
                // Check thread safety
                if (strpos($dll, $isThreadSafe) !== false) {
                    $reasons[] = '✅ Thread Safety ตรงกัน (' . $isThreadSafe . ')';
                } else {
                    $reasons[] = '❌ Thread Safety ไม่ตรงกัน (ต้องการ ' . $isThreadSafe . ')';
                }
                
                // Determine if this is the correct file
                $correctCount = 0;
                foreach ($reasons as $reason) {
                    if (strpos($reason, '✅') === 0) $correctCount++;
                }
                
                $isCorrect = $correctCount >= 3; // At least 3 correct criteria
            } else {
                $reasons[] = '❌ ไม่ใช่ไฟล์ MongoDB extension';
            }
            
            $cardClass = $isCorrect ? 'found' : 'not-found';
            
            echo "<div class='file-card $cardClass'>
            <h4>📄 $dll</h4>";
            
            foreach ($reasons as $reason) {
                echo "<p>$reason</p>";
            }
            
            if ($isCorrect) {
                echo "<div class='status success'>🎯 ไฟล์นี้เหมาะสมสำหรับระบบของคุณ</div>";
                echo "<p><strong>Path:</strong></p>";
                echo "<div class='file-path'>$downloadPath\\$dll</div>";
            }
            
            echo "</div>";
        }
        
        echo "</div>";
    } else {
        echo "<div class='status error'>❌ ไม่พบไฟล์ .dll ในโฟลเดอร์</div>";
    }
    
    // Show other files
    if (!empty($otherFiles)) {
        echo "<h3>📄 ไฟล์อื่นๆ:</h3>
        <div class='code'>";
        foreach ($otherFiles as $file) {
            echo "$file\n";
        }
        echo "</div>";
    }
    
} else {
    echo "<div class='status error'>❌ ไม่พบโฟลเดอร์ที่ดาวน์โหลด</div>";
    echo "<div class='status warning'>
    <h4>⚠️ แนะนำ:</h4>
    <ul>
    <li>ตรวจสอบว่าแตกไฟล์แล้วหรือยัง</li>
    <li>ตรวจสอบ path ที่ถูกต้อง</li>
    <li>ลองค้นหาไฟล์ php_mongodb.dll ในเครื่อง</li>
    </ul>
    </div>";
}

echo "</div>";

// Check current extension status
echo "<div class='check-section'>
<h2>🔍 สถานะ Extension ปัจจุบัน</h2>";

if (extension_loaded('mongodb')) {
    echo "<div class='status success'>
    <h3>✅ MongoDB Extension ติดตั้งแล้ว</h3>
    <p><strong>Version:</strong> " . phpversion('mongodb') . "</p>
    <p>ไม่จำเป็นต้องติดตั้งใหม่</p>
    </div>";
} else {
    echo "<div class='status warning'>
    <h3>⚠️ MongoDB Extension ยังไม่ได้ติดตั้ง</h3>
    <p>ต้องติดตั้ง extension เพื่อใช้งานระบบ MongoDB</p>
    </div>";
    
    // Check if DLL already exists in extension directory
    $existingDll = $extensionDir . '\\php_mongodb.dll';
    if (file_exists($existingDll)) {
        echo "<div class='status info'>
        <h4>📁 พบไฟล์ DLL ในโฟลเดอร์ Extension</h4>
        <p>Path: $existingDll</p>
        <p>อาจต้องเพิ่มใน php.ini หรือรีสตาร์ท web server</p>
        </div>";
    }
}

echo "</div>";

// Installation recommendations
echo "<div class='check-section'>
<h2>💡 คำแนะนำการติดตั้ง</h2>";

if (!empty($dllFiles)) {
    $correctFile = null;
    foreach ($dllFiles as $dll) {
        if (strpos($dll, 'mongodb') !== false && 
            (strpos($dll, $phpMajor) !== false || strpos($dll, str_replace('.', '', $phpMajor)) !== false) &&
            strpos($dll, $phpArch) !== false &&
            strpos($dll, $isThreadSafe) !== false) {
            $correctFile = $dll;
            break;
        }
    }
    
    if ($correctFile) {
        echo "<div class='status success'>
        <h3>🎯 ไฟล์ที่แนะนำ:</h3>
        <p><strong>$correctFile</strong></p>
        </div>";
        
        echo "<div class='code'>
# ขั้นตอนการติดตั้ง:
1. คัดลอกไฟล์: $downloadPath\\$correctFile
2. ไปยัง: $extensionDir\\php_mongodb.dll
3. เพิ่มใน php.ini: extension=mongodb
4. รีสตาร์ท Apache/Web Server
        </div>";
    } else {
        echo "<div class='status warning'>
        <h3>⚠️ ไม่พบไฟล์ที่เหมาะสม</h3>
        <p>ไฟล์ที่มีอาจไม่ตรงกับระบบของคุณ</p>
        </div>";
    }
} else {
    echo "<div class='status error'>
    <h3>❌ ไม่พบไฟล์ DLL</h3>
    <p>กรุณาดาวน์โหลดไฟล์ที่ถูกต้องจาก PECL</p>
    </div>";
}

echo "<div class='status info'>
<h4>📋 ข้อมูลที่ต้องการ:</h4>
<ul>
<li><strong>PHP Version:</strong> $phpVersion</li>
<li><strong>Architecture:</strong> $phpArch</li>
<li><strong>Thread Safety:</strong> $isThreadSafe</li>
<li><strong>ชื่อไฟล์ที่ต้องการ:</strong> php_mongodb-*-$phpMajor-$isThreadSafe-$phpArch.dll</li>
</ul>
</div>";

echo "</div>";

// Quick actions
echo "<div class='check-section'>
<h2>🚀 การดำเนินการ</h2>
<p><a href='install_mongodb_windows.php' class='btn'>📖 คู่มือติดตั้ง Windows</a></p>
<p><a href='mongodb_setup_check.php' class='btn'>🔍 ตรวจสอบการติดตั้ง</a></p>
<p><a href='https://pecl.php.net/package/mongodb' class='btn' target='_blank'>📥 ดาวน์โหลดไฟล์ใหม่</a></p>";

if (extension_loaded('mongodb')) {
    echo "<p><a href='mongodb_test.php' class='btn success'>🧪 ทดสอบระบบ</a></p>";
}

echo "</div>";

// Search for DLL files in common locations
echo "<div class='check-section'>
<h2>🔍 ค้นหาไฟล์ DLL ในตำแหน่งอื่น</h2>";

$searchPaths = [
    'C:\\Users\\<USER>\\Downloads',
    'C:\\Users\\<USER>\\Desktop',
    'C:\\xampp\\php\\ext',
    'C:\\wamp64\\bin\\php\\php' . $phpMajor . '\\ext',
    'C:\\laragon\\bin\\php\\php-' . $phpVersion . '\\ext'
];

foreach ($searchPaths as $path) {
    if (is_dir($path)) {
        $found = false;
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($path));
        
        foreach ($iterator as $file) {
            if ($file->isFile() && pathinfo($file->getFilename(), PATHINFO_EXTENSION) === 'dll' && 
                strpos($file->getFilename(), 'mongodb') !== false) {
                if (!$found) {
                    echo "<div class='status success'>✅ พบไฟล์ใน: $path</div>";
                    $found = true;
                }
                echo "<div class='file-path'>" . $file->getPathname() . "</div>";
            }
        }
        
        if (!$found) {
            echo "<div class='status info'>ℹ️ ไม่พบไฟล์ใน: $path</div>";
        }
    }
}

echo "</div>";

echo "</body></html>";
?>
