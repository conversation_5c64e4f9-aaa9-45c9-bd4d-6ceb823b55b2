-- Asset Management System Database Setup for MySQL 8.0
-- Created: 2024
-- Compatible with: MySQL 8.0+

-- Set MySQL 8.0 specific settings
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
SET time_zone = '+07:00';
SET SESSION innodb_strict_mode = 1;
SET SESSION foreign_key_checks = 1;

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS asset_management 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE asset_management;

-- Drop tables if they exist (for clean installation)
DROP TABLE IF EXISTS asset_logs;
DROP TABLE IF EXISTS assets;
DROP TABLE IF EXISTS users;

-- Create users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) DEFAULT NULL,
    email VARCHAR(100) DEFAULT NULL,
    role ENUM('Admin', 'User') NOT NULL DEFAULT 'User',
    status ENUM('Active', 'Inactive') NOT NULL DEFAULT 'Active',
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for better performance
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created_date (created_date)
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci
  COMMENT='User accounts for asset management system';

-- Create assets table
CREATE TABLE assets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_id VARCHAR(50) DEFAULT NULL,
    tag VARCHAR(50) DEFAULT NULL,
    type VARCHAR(100) DEFAULT NULL,
    brand VARCHAR(100) DEFAULT NULL,
    model VARCHAR(100) DEFAULT NULL,
    hostname VARCHAR(100) DEFAULT NULL,
    operating_system VARCHAR(100) DEFAULT NULL,
    serial_number VARCHAR(100) DEFAULT NULL,
    status VARCHAR(50) DEFAULT NULL,
    department VARCHAR(100) DEFAULT NULL,
    warranty_expire DATE DEFAULT NULL,
    description TEXT DEFAULT NULL,
    set_name VARCHAR(100) DEFAULT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) DEFAULT NULL,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT NULL,
    
    -- Indexes for better performance
    INDEX idx_asset_id (asset_id),
    INDEX idx_tag (tag),
    INDEX idx_type (type),
    INDEX idx_brand (brand),
    INDEX idx_model (model),
    INDEX idx_hostname (hostname),
    INDEX idx_serial_number (serial_number),
    INDEX idx_status (status),
    INDEX idx_department (department),
    INDEX idx_warranty_expire (warranty_expire),
    INDEX idx_created_date (created_date),
    INDEX idx_created_by (created_by),
    INDEX idx_updated_date (updated_date),
    
    -- Full-text search indexes
    FULLTEXT idx_search_text (type, brand, model, hostname, description)
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci
  COMMENT='Asset inventory management';

-- Create asset_logs table for audit trail
CREATE TABLE asset_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_id INT DEFAULT NULL,
    action VARCHAR(50) NOT NULL,
    old_values JSON DEFAULT NULL,
    new_values JSON DEFAULT NULL,
    changed_by VARCHAR(100) DEFAULT NULL,
    changed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    
    -- Indexes for better performance
    INDEX idx_asset_id (asset_id),
    INDEX idx_action (action),
    INDEX idx_changed_by (changed_by),
    INDEX idx_changed_date (changed_date),
    
    -- Foreign key constraint
    CONSTRAINT fk_asset_logs_asset_id 
        FOREIGN KEY (asset_id) 
        REFERENCES assets(id) 
        ON DELETE CASCADE 
        ON UPDATE CASCADE
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci
  COMMENT='Asset change history and audit trail';

-- Insert default admin user
INSERT INTO users (username, password, full_name, email, role, status) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '<EMAIL>', 'Admin', 'Active');
-- Default password is: admin123

-- Insert sample user
INSERT INTO users (username, password, full_name, email, role, status) VALUES 
('user1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John Doe', '<EMAIL>', 'User', 'Active');
-- Default password is: user123

-- Insert sample assets
INSERT INTO assets (
    asset_id, tag, type, brand, model, hostname, operating_system, 
    serial_number, status, department, warranty_expire, description, 
    set_name, created_by
) VALUES 
('AST001', 'TAG001', 'Desktop', 'Dell', 'OptiPlex 7090', 'PC-001', 'Windows 11', 
 'DL123456789', 'ใช้งาน', 'IT', '2025-12-31', 'Desktop computer for IT department', 
 'IT-SET-001', 'admin'),

('AST002', 'TAG002', 'Laptop', 'Lenovo', 'ThinkPad X1', 'LT-001', 'Windows 11', 
 'LN987654321', 'ใช้งาน', 'Accounting', '2025-06-30', 'Laptop for accounting staff', 
 'ACC-SET-001', 'admin'),

('AST003', 'TAG003', 'Monitor', 'LG', '27UP850', 'MON-001', '-', 
 'LG555666777', 'ใช้งาน', 'IT', '2024-12-31', '27-inch 4K monitor', 
 'IT-SET-001', 'admin'),

('AST004', 'TAG004', 'Printer', 'HP', 'LaserJet Pro M404n', 'PRT-001', '-', 
 'HP111222333', 'สำรอง', 'Administration', '2024-08-15', 'Laser printer for office use', 
 'ADM-SET-001', 'admin'),

('AST005', 'TAG005', 'Server', 'Dell', 'PowerEdge R740', 'SRV-001', 'Windows Server 2022', 
 'DL999888777', 'ใช้งาน', 'IT', '2026-03-31', 'Main application server', 
 'IT-SRV-001', 'admin');

-- Create views for reporting
CREATE VIEW asset_summary AS
SELECT 
    department,
    COUNT(*) as total_assets,
    SUM(CASE WHEN status = 'ใช้งาน' THEN 1 ELSE 0 END) as active_assets,
    SUM(CASE WHEN status = 'ชำรุด' THEN 1 ELSE 0 END) as damaged_assets,
    SUM(CASE WHEN status = 'สำรอง' THEN 1 ELSE 0 END) as spare_assets
FROM assets 
WHERE department IS NOT NULL 
GROUP BY department;

CREATE VIEW warranty_expiring AS
SELECT 
    id, asset_id, tag, type, brand, model, department, warranty_expire,
    DATEDIFF(warranty_expire, CURDATE()) as days_until_expiry
FROM assets 
WHERE warranty_expire IS NOT NULL 
  AND warranty_expire >= CURDATE()
  AND warranty_expire <= DATE_ADD(CURDATE(), INTERVAL 90 DAY)
ORDER BY warranty_expire ASC;

-- Create stored procedures for common operations
DELIMITER //

CREATE PROCEDURE GetAssetsByDepartment(IN dept_name VARCHAR(100))
BEGIN
    SELECT * FROM assets 
    WHERE department = dept_name 
    ORDER BY created_date DESC;
END //

CREATE PROCEDURE GetAssetHistory(IN asset_id_param INT)
BEGIN
    SELECT 
        al.*,
        a.asset_id,
        a.tag,
        a.type,
        a.brand,
        a.model
    FROM asset_logs al
    LEFT JOIN assets a ON al.asset_id = a.id
    WHERE al.asset_id = asset_id_param
    ORDER BY al.changed_date DESC;
END //

CREATE PROCEDURE UpdateAssetStatus(
    IN asset_id_param INT, 
    IN new_status VARCHAR(50),
    IN changed_by_param VARCHAR(100)
)
BEGIN
    DECLARE old_status VARCHAR(50);
    
    -- Get current status
    SELECT status INTO old_status FROM assets WHERE id = asset_id_param;
    
    -- Update status
    UPDATE assets 
    SET status = new_status, 
        updated_by = changed_by_param,
        updated_date = CURRENT_TIMESTAMP
    WHERE id = asset_id_param;
    
    -- Log the change
    INSERT INTO asset_logs (asset_id, action, old_values, new_values, changed_by)
    VALUES (
        asset_id_param, 
        'status_update',
        JSON_OBJECT('status', old_status),
        JSON_OBJECT('status', new_status),
        changed_by_param
    );
END //

DELIMITER ;

-- Create triggers for automatic logging
DELIMITER //

CREATE TRIGGER asset_insert_log 
AFTER INSERT ON assets
FOR EACH ROW
BEGIN
    INSERT INTO asset_logs (asset_id, action, new_values, changed_by)
    VALUES (
        NEW.id, 
        'create',
        JSON_OBJECT(
            'asset_id', NEW.asset_id,
            'tag', NEW.tag,
            'type', NEW.type,
            'brand', NEW.brand,
            'model', NEW.model,
            'status', NEW.status,
            'department', NEW.department
        ),
        NEW.created_by
    );
END //

CREATE TRIGGER asset_update_log 
AFTER UPDATE ON assets
FOR EACH ROW
BEGIN
    INSERT INTO asset_logs (asset_id, action, old_values, new_values, changed_by)
    VALUES (
        NEW.id, 
        'update',
        JSON_OBJECT(
            'asset_id', OLD.asset_id,
            'tag', OLD.tag,
            'type', OLD.type,
            'brand', OLD.brand,
            'model', OLD.model,
            'status', OLD.status,
            'department', OLD.department
        ),
        JSON_OBJECT(
            'asset_id', NEW.asset_id,
            'tag', NEW.tag,
            'type', NEW.type,
            'brand', NEW.brand,
            'model', NEW.model,
            'status', NEW.status,
            'department', NEW.department
        ),
        NEW.updated_by
    );
END //

CREATE TRIGGER asset_delete_log 
BEFORE DELETE ON assets
FOR EACH ROW
BEGIN
    INSERT INTO asset_logs (asset_id, action, old_values, changed_by)
    VALUES (
        OLD.id, 
        'delete',
        JSON_OBJECT(
            'asset_id', OLD.asset_id,
            'tag', OLD.tag,
            'type', OLD.type,
            'brand', OLD.brand,
            'model', OLD.model,
            'status', OLD.status,
            'department', OLD.department
        ),
        USER()
    );
END //

DELIMITER ;

-- Grant privileges (adjust as needed)
-- GRANT ALL PRIVILEGES ON asset_management.* TO 'asset_user'@'localhost' IDENTIFIED BY 'secure_password';
-- FLUSH PRIVILEGES;

-- Show completion message
SELECT 'Asset Management System database setup completed successfully for MySQL 8.0!' as message;
