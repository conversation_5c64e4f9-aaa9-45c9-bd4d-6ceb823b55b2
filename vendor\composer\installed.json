{"packages": [{"name": "mpdf/mpdf", "version": "v6.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "7f138bf7508eac895ac2c13d2509b056ac7e7e97"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/7f138bf7508eac895ac2c13d2509b056ac7e7e97", "reference": "7f138bf7508eac895ac2c13d2509b056ac7e7e97", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4.0", "setasign/fpdi": "1.6.*"}, "require-dev": {"phpunit/phpunit": "^4.7"}, "suggest": {"ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "time": "2016-12-12T10:42:18+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["mpdf.php", "classes"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0"], "authors": [{"name": "<PERSON>", "role": "Developer"}], "description": "A PHP class to generate PDF files from HTML with Unicode/UTF-8 and CJK support", "homepage": "http://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "support": {"docs": "http://mpdf.github.io", "issues": "https://github.com/mpdf/mpdf/issues", "source": "https://github.com/mpdf/mpdf"}, "install-path": "../mpdf/mpdf"}, {"name": "setasign/fpdi", "version": "1.6.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "a6ad58897a6d97cc2d2cd2adaeda343b25a368ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/a6ad58897a6d97cc2d2cd2adaeda343b25a368ea", "reference": "a6ad58897a6d97cc2d2cd2adaeda343b25a368ea", "shasum": ""}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use \"tecnickcom/tcpdf\" as an alternative there's no fixed dependency configured.", "setasign/fpdi-fpdf": "Use this package to automatically evaluate dependencies to FPDF.", "setasign/fpdi-tcpdf": "Use this package to automatically evaluate dependencies to TCPDF."}, "time": "2017-05-11T14:25:49+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["filters/", "fpdi.php", "fpdf_tpl.php", "fpdi_pdf_parser.php", "pdf_context.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/master"}, "install-path": "../setasign/fpdi"}], "dev": true, "dev-package-names": []}